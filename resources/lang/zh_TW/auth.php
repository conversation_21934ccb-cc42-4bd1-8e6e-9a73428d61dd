<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Authentication Language Lines
    |--------------------------------------------------------------------------
    |
    | The following language lines are used during authentication for various
    | messages that we need to display to the user. You are free to modify
    | these language lines according to your application's requirements.
    |
    */

    'failed' => '所提供的帳號密碼與資料庫中的記錄不相符。',
    'password' => '提供的密碼不正確。',
    'throttle' => '嘗試登入次數過多。請在 :seconds 秒後重試。',
    'not_authorized' => '未授權',
    'email_password_incorrect' => '信箱或密碼有誤',
    'phone_password_incorrect' => '手機或密碼有誤',
    'verification_code_error' => '驗證碼錯誤 或 已經過期',
    'verification_code_used' => '驗證碼已經失效',
    'verification_code_expired' => '驗證碼已過期',
    'email_already_registered' => '此信箱已經註冊過',
    'verification_email_sent' => '已寄送驗證信',
    'registration_success' => '註冊成功',
    'logout_success' => '登出成功',
    'account_not_found' => '查無此帳號',
    'verification_throttle' => '請稍候再試，驗證碼已寄出',

    // JWT Token 錯誤訊息
    'token_not_found' => '找不到認證令牌',
    'token_expired' => '認證令牌已過期',
    'token_invalid' => '無效的token',

    // 密碼重置相關
    'password_reset_success' => '密碼重置成功',
];
