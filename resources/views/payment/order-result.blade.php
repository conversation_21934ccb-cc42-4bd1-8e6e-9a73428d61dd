<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>處理中...</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }

        .container {
            text-align: center;
            background: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        #redirectForm {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="spinner"></div>
        <h3>處理中，請稍候...</h3>
        <p>正在轉向至結果頁面</p>
    </div>

    <!-- 隱藏的自動提交表單 -->
    <form id="redirectForm" method="POST" action="{{ $targetUrl }}">
        @csrf
        @foreach($formData as $key => $value)
            @if(is_array($value))
                <input type="hidden" name="{{ $key }}" value="{{ json_encode($value) }}">
            @else
                <input type="hidden" name="{{ $key }}" value="{{ $value }}">
            @endif
        @endforeach
    </form>

    <script>
        // 立即提交表單
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('redirectForm').submit();
        });
    </script>
</body>
</html>
