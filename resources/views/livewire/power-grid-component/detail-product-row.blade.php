<div class="p-4 bg-gray-100 dark:bg-pg-primary-800 rounded-lg shadow-md max-h-[50vh] overflow-y-auto">
    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">
        {{ $row->category_name }} 的產品清單
    </h3>

    <div class="overflow-y-auto">
        <ul class="space-y-2">
            @foreach ($row->products as $product)
                    <?php
                    $url = \Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ProductResource::getUrl('view', [
                        'record' => $product
                    ])
                    ?>
                <a href="{{ $url }}" class="block">
                    <li class="bg-white dark:bg-pg-primary-700 shadow-md rounded-lg p-4 hover:bg-gray-100 dark:hover:bg-gray-700 transition">
                        <h4 class="text-md font-semibold text-gray-900 dark:text-white">{{ $product->translations->where('lang','=','zh_TW')->first()->title }}</h4>
                    </li>
                </a>
            @endforeach
        </ul>
    </div>
</div>
