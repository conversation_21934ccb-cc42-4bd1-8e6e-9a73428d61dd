# Base Filament Plugin

## Installation

### Step 1: Install the package via composer

```bash
composer require stephenchenorg/base-filament-plugin
```

### Step 2: Modify auth configuration

```php
// config/auth.php

'guards' => [
    'web' => [
        'driver' => 'session',
        // Change from 'users' to 'admins'
        'provider' => 'admins',
    ],

    // ... other guards
],

'providers' => [
    // Add the admins provider
    'admins' => [
        'driver' => 'eloquent',
        'model' => Stephenchenorg\BaseFilamentPlugin\Models\Admin::class,
    ],
    // ... other providers
],

'passwords' => [
    'admins' => [
        'provider' => 'admins',
        'table' => 'password_reset_tokens',
        'expire' => 60,
        'throttle' => 60,
    ],
    // ... other password configurations
],
```

### Step 3: Publish and run the migrations

```bash
php artisan vendor:publish --tag="base-filament-plugin-migrations"
php artisan migrate
```

### Step 4: Publish the config file

```bash
php artisan vendor:publish --tag="base-filament-plugin-config"
```

### Step 5 (Optional): Publish the views

```bash
php artisan vendor:publish --tag="base-filament-plugin-views"
```
