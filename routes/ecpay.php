<?php

use Illuminate\Support\Facades\Route;
use Stephenchenorg\BaseFilamentPlugin\Http\Controllers\ECPayController;

/*
|--------------------------------------------------------------------------
| ECPay Routes
|--------------------------------------------------------------------------
|
| 這裡定義 ECPay 相關的路由 放到 /api/ecpay 下

|
*/

Route::middleware('api')->prefix('api/ecpay')->group(function () {
    // 綠界向後端通知結果
    Route::post('/notify', [ECPayController::class, 'notifyUrl'])->name('ecpay.notify');
    // 綠界重導向前端 會經過此路由
    Route::post('/return', [ECPayController::class, 'returnUrl'])->name('ecpay.return');
    // 綠界向後端通知取號結果
    Route::post('/info', [ECPayController::class, 'paymentInfoUrl'])->name('ecpay.paymentInfo');
    // 結帳
    Route::post('/checkout', [ECPayController::class, 'checkout'])->name('checkout');
});
