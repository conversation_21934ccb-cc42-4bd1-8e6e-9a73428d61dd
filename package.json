{"private": true, "type": "module", "scripts": {"dev:styles": "npx tailwindcss -i resources/css/index.css -o resources/dist/base-filament-plugin.css --postcss --watch", "dev:scripts": "node bin/build.js --dev", "build:styles": "npx tailwindcss -i resources/css/index.css -o resources/dist/base-filament-plugin.css --postcss --minify && npm run purge", "build:scripts": "node bin/build.js", "purge": "filament-purge -i resources/dist/base-filament-plugin.css -o resources/dist/base-filament-plugin.css -v 3.x", "dev": "npm-run-all --parallel dev:*", "build": "npm-run-all build:*"}, "devDependencies": {"@awcodes/filament-plugin-purge": "^1.1.1", "@tailwindcss/forms": "^0.5.4", "@tailwindcss/typography": "^0.5.9", "autoprefixer": "^10.4.14", "esbuild": "^0.19.2", "npm-run-all": "^4.1.5", "postcss": "^8.4.26", "postcss-import": "^15.1.0", "prettier": "^2.7.1", "prettier-plugin-tailwindcss": "^0.1.13", "tailwindcss": "^3.3.3"}}