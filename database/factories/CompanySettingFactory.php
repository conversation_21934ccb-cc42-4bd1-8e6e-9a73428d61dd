<?php

namespace <PERSON>org\BaseFilamentPlugin\Database\Factories;

use Stephenchenorg\BaseFilamentPlugin\Models\CompanySetting;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceLanguage;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceStorage;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<CompanySetting>
 */
class CompanySettingFactory extends Factory
{
    protected $model = CompanySetting::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'lang'         => $this->faker->randomElement(ServiceLanguage::getLanguages()),
            'logo'         => ServiceStorage::getDefaultLogoPath(),
            'name'         => $this->faker->company,
            'description'  => $this->faker->paragraph,
            'address_1'    => $this->faker->address,
            'address_2'    => $this->faker->address,
            'phone_1'      => $this->faker->phoneNumber,
            'phone_2'      => $this->faker->phoneNumber,
            'email_1'      => $this->faker->unique()->safeEmail,
            'email_2'      => $this->faker->unique()->safeEmail,
            'line_link'    => $this->faker->url,
            'fb_link'      => $this->faker->url,
            'ig_link'      => $this->faker->url,
            'twitter_link' => $this->faker->url,
            'threads_link' => $this->faker->url,
        ];
    }
}
