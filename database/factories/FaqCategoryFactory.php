<?php

namespace Stephenchenorg\BaseFilamentPlugin\Database\Factories;

use Stephenchenorg\BaseFilamentPlugin\Models\FaqCategory;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceLanguage;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<FaqCategory>
 */
class FaqCategoryFactory extends Factory
{
    protected $model = FaqCategory::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'key'    => $this->faker->unique()->word(), // 唯一鍵
            'status' => $this->faker->randomElement([0, 1]), // 狀態 0: 關閉, 1: 開啟
            'sort'   => $this->faker->numberBetween(0, 100), // 排序
            'slug'   => $this->faker->slug(), // 唯一的slug
        ];
    }

    /**
     * @return $this
     */
    public function withTranslations(): self
    {
        return $this->afterCreating(function (FaqCategory $faqCategory)
        {

            foreach (ServiceLanguage::getLanguages() as $lang) {
                $faqCategory->translations()->create([
                    'title'           => $this->faker->word(),
                    'content'         => $this->faker->text(),
                    'lang'            => $lang,
                    'faq_category_id' => $faqCategory->id,
                ]);
            }
        });
    }


    /**
     * @return array
     */
    public function getTranslations(): array
    {
        $translations = [];

        foreach (ServiceLanguage::getLanguages() as $lang) {
            $translations[$lang][] = [
                'title'           => $this->faker->word(),
                'content'         => $this->faker->text(),
                'lang'            => $lang,
            ];
        }

        return $translations;

    }
}
