<?php

namespace Stephenchenorg\BaseFilamentPlugin\Database\Factories;

use Stephenchenorg\BaseFilamentPlugin\Enum\EnumStoreType;
use Stephenchenorg\BaseFilamentPlugin\Models\StoreAddress;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<StoreAddress>
 */
class StoreAddressFactory extends Factory
{
    protected $model = StoreAddress::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'store_type'    => EnumStoreType::SEVEN_ELEVEN,
            'store_number'  => $this->faker->unique()->numerify('CS####'),
            'mail_name'    => $this->faker->company,
            'store_address' => $this->faker->address,
        ];
    }
}
