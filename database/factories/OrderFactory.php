<?php

namespace <PERSON>g\BaseFilamentPlugin\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Stephen<PERSON>org\BaseFilamentPlugin\Models\Order;
use Stephenchenorg\BaseFilamentPlugin\Models\Client;
use Stephenchenorg\BaseFilamentPlugin\Models\Customer;
use Stephenchenorg\BaseFilamentPlugin\Models\OrderItem;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductSpecificationReservingItem;
use Stephenchenorg\BaseFilamentPlugin\Enum\EnumInvoiceMethod;
use Stephen<PERSON>org\BaseFilamentPlugin\Enum\EnumPaymentMethod;
use Stephenchenorg\BaseFilamentPlugin\Enum\EnumPaymentStatus;
use Stephenchenorg\BaseFilamentPlugin\Enum\EnumShippingMethod;
use Stephenchenorg\BaseFilamentPlugin\Enum\EnumShippingStatus;


/**
 * @extends Factory<Order>
 */
class OrderFactory extends Factory
{
    protected $model = Order::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $shippingCost = $this->faker->randomFloat(2, 0, 50);
        $itemAmount = $this->faker->randomFloat(2, 10, 1000);
        $totalAmountUntaxed = $itemAmount + $shippingCost;

        // 檢查是否為三聯發票且需要加稅
        $invoiceMethod = $this->faker->randomElement(EnumInvoiceMethod::cases());
        $shouldApplyTax = $invoiceMethod->isTriplicate() && config('cs.triplicate_tax', true);
        $totalAmountTaxed = $shouldApplyTax ? round($totalAmountUntaxed * 1.05, 2) : $totalAmountUntaxed;

        $orderableType = $this->faker->randomElement([
            Client::class,
            Customer::class,
        ]);

        // 根據選擇的類型隨機選擇一個 ID
        if ($orderableType === Client::class) {
            $orderable = Client::query()->inRandomOrder()->first();
            $orderableId = $orderable ? $orderable->id : null;
        } else {
            $orderable = Customer::query()->inRandomOrder()->first();
            $orderableId = $orderable ? $orderable->id : null;
        }

        $invoiceMethod = $this->faker->randomElement(EnumInvoiceMethod::getAvailableMethods());
        $paymentMethod = $this->faker->randomElement(EnumPaymentMethod::getAvailableMethods());
        $shippingMethod = $this->faker->randomElement(EnumShippingMethod::getAvailableMethods());


        return [

            // 訂單基本資訊
            'orderable_type' => $orderableType,
            'orderable_id' => $orderableId,
            'order_key' => $this->faker->unique()->numerify('ORD-#####'),
            'total_amount_untaxed' => $totalAmountUntaxed,
            'total_amount_taxed' => $totalAmountTaxed,
            'shipping_cost' => $shippingCost,
            'item_amount' => $itemAmount,

            // 付款和配送相關
            'payment_method' => $paymentMethod,
            'payment_status' => EnumPaymentStatus::UNPAID->value,
            'shipping_method' => $shippingMethod,
            'shipping_status' => EnumShippingStatus::UNSHIPPED->value,

            // 訂購人資訊
            'name' => $this->faker->name,
            'phone' => $this->faker->phoneNumber,
            'email' => $this->faker->email,

            // 地址資訊
            'store_address_id' => null,
            'country_code' => 'TWN',
            'state' => '台灣',
            'city' => $this->faker->city(),
            'district' => $this->faker->word(),
            'postal_code' => $this->faker->postcode(),
            'address_line1' => $this->faker->streetAddress(),
            'address_line2' => $this->faker->optional(0.4)->secondaryAddress(),

            // 發票資訊
            'invoice_method' => $invoiceMethod,
            'carrier_value' => $invoiceMethod->requiresCarrierValue() ? $this->getCarrierValue($invoiceMethod) : null,
            'invoice_address' => $invoiceMethod->requiresInvoiceAddress() ? $this->faker->address() : null,
            'love_code' => $invoiceMethod->requiresLoveCode() ? $this->getLoveCode() : null,
            'vat' => $this->faker->optional(0.3)->numerify('########'),
            'invoice_title' => $this->faker->optional(0.3)->company(),

            'payload' => null,
            'remark' => $this->faker->optional(0.3)->sentence(),
        ];
    }



    /**
     * 根據發票方式產生載具號碼
     */
    private function getCarrierValue(EnumInvoiceMethod $invoiceMethod): string
    {
        return match ($invoiceMethod) {
            EnumInvoiceMethod::MOBILE_BARCODE => '/' . $this->faker->regexify('[A-Z0-9]{7}'),
            EnumInvoiceMethod::CITIZEN_CARD => $this->faker->regexify('[A-Z]{2}[0-9]{14}'),
            default => '',
        };
    }

    /**
     * 產生愛心碼
     */
    private function getLoveCode(): string
    {
        // 愛心碼通常是3-7位數字
        return $this->faker->numerify('###');
    }

    public function withItems(int $count = 1): OrderFactory|Factory
    {
        return $this->afterCreating(function (Order $order) use ($count) {

            $items = OrderItem::factory()->count($count)->make([
                'order_id' => $order->id,
            ]);

            $order->items()->saveMany($items);

            // 處理產品預留量
            $this->handleProductReservations($items);
        });
    }

    /**
     * 處理產品預留量
     *
     * @param \Illuminate\Database\Eloquent\Collection $items
     */
    private function handleProductReservations($items): void
    {
        foreach ($items as $item) {
            $productSpecificationId = $item->product_specification_id;
            $quantity = $item->quantity;

            // 查找現有的預留記錄
            $reservingItem = ProductSpecificationReservingItem::query()
                ->where('product_specification_id', $productSpecificationId)
                ->first();

            if ($reservingItem) {
                // 如果已存在，增加預留量
                $reservingItem->increment('quantity', $quantity);
            } else {
                // 如果不存在，建立新記錄
                ProductSpecificationReservingItem::query()->create([
                    'product_specification_id' => $productSpecificationId,
                    'quantity' => $quantity
                ]);
            }
        }
    }
}
