<?php

namespace Stephenchenorg\BaseFilamentPlugin\Database\Factories;

use Stephenchenorg\BaseFilamentPlugin\Enum\EnumTagType;
use Stephenchenorg\BaseFilamentPlugin\Models\Tag;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceLanguage;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<Tag>
 */
class TagFactory extends Factory
{
    protected $model = Tag::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'key'  => $this->faker->unique()->word(),
            'slug' => $this->faker->slug(),
            'type' => EnumTagType::SHARED,
        ];
    }

    /**
     * @return $this
     */
    public function withTranslations(): self
    {
        return $this->afterCreating(function (Tag $tag)
        {
            foreach (ServiceLanguage::getLanguages() as $lang) {
                $tag->translations()->create([
                    'title' => $this->faker->word(),
                    'lang'  => $lang,
                ]);
            }
        });
    }

}
