<?php

namespace Stephenchenorg\BaseFilamentPlugin\Database\Factories;

use Stephenchenorg\BaseFilamentPlugin\Models\Brand;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceLanguage;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<Brand>
 */
class BrandFactory extends Factory
{
    protected $model = Brand::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'logo' => 'images/logo/microsoft.png',
            'key'  => $this->faker->unique()->word(),
            'sort' => '1',
        ];

    }

    /**
     * @return $this
     */
    public function withTranslations(): self
    {
        return $this->afterCreating(function (Brand $faq)
        {
            foreach (ServiceLanguage::getLanguages() as $lang) {
                $faq->translations()->create([
                    'title'       => $this->faker->word(),
                    'description' => $this->faker->text(),
                    'lang'        => $lang,
                ]);
            }
        });
    }
}
