<?php

namespace Stephenchenorg\BaseFilamentPlugin\Database\Factories;

use Stephenchenorg\BaseFilamentPlugin\Models\Faq;
use Stephenchenorg\BaseFilamentPlugin\Models\FaqCategory;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceLanguage;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<Faq>
 */
class FaqFactory extends Factory
{
    protected $model = Faq::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'key'             => $this->faker->unique()->word(),
            'faq_category_id' => FaqCategory::query()->inRandomOrder()->first()->id,
            'sort'            => $this->faker->numberBetween(0, 100),
            'status'          => $this->faker->randomElement([0, 1]), // 0 = 關閉, 1 = 開啟
        ];
    }

    /**
     * @return $this
     */
    public function withTranslations(): self
    {
        return $this->afterCreating(function (Faq $faq)
        {

            foreach (ServiceLanguage::getLanguages() as $lang) {
                $faq->translations()->create([
                    'title'           => $this->faker->word(),
                    'content'         => $this->faker->text(),
                    'lang'            => $lang,
                    'faq_id'          => $faq->id,
                ]);
            }
        });
    }

    public function getTranslations(): array
    {
        $translations = [];

        foreach (ServiceLanguage::getLanguages() as $lang) {
            $translations[$lang][] = [
                'title'           => $this->faker->word(),
                'content'         => $this->faker->text(),
                'lang'            => $lang,
            ];
        }

        return $translations;
    }
}
