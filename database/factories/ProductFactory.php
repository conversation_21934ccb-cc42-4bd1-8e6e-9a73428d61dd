<?php

namespace <PERSON>org\BaseFilamentPlugin\Database\Factories;

use Stephenchenorg\BaseFilamentPlugin\Enum\EnumProductType;
use Stephen<PERSON>org\BaseFilamentPlugin\Enum\EnumTagType;
use Stephenchenorg\BaseFilamentPlugin\Models\Product;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductAttribute;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductCategory;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductImage;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductSpecification;
use Stephen<PERSON>org\BaseFilamentPlugin\Models\Tag;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceLanguage;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceStorage;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Http\UploadedFile;

/**
 * @extends Factory<Product>
 */
class ProductFactory extends Factory
{
    protected $model = Product::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $this->faker = \Faker\Factory::create('zh_TW');

        $startedAt = Carbon::parse($this->faker->dateTimeBetween('-1 year', 'now'))->toDateTimeString();
        $endedAt = Carbon::parse($this->faker->dateTimeBetween($startedAt, '+1 year'))->toDateTimeString();

        return [
            'type'                => EnumProductType::Product->value,
            'part_number'         => $this->faker->unique()->numerify('PN-####'),
            'product_category_id' => ProductCategory::query()->inRandomOrder()->first()->id,
            'is_hottest'          => $this->faker->boolean(),
            'is_newest'           => $this->faker->boolean(),
            'sort'                => $this->faker->numberBetween(0, 100),
            'status'              => $this->faker->randomElement([0, 1]), // 0 = 關閉, 1 = 開啟
            'started_at'          => $startedAt,
            'created_at'          => now(),
            'ended_at'            => $endedAt,
        ];
    }

    /**
     * @return $this
     */
    public function withAttributesAndSpecifications(): self
    {
        return $this->afterCreating(function (Product $product)
        {

            $attributes_1 = ProductAttribute::factory()
                ->withTranslations()
                ->withItems()
                ->create([
                    'product_id' => $product->id,
                ]);

            $attributes_2 = ProductAttribute::factory()
                ->withTranslations()
                ->withItems()
                ->create([
                    'product_id' => $product->id,
                ]);


            $item_1 = $attributes_1->items->random();
            $item_2 = $attributes_2->items->random();


            $combination_key = implode('-', [$item_1->id, $item_2->id]);

            ProductSpecification::factory()
                ->withTranslations()
                ->create([
                    'combination_key' => $combination_key,
                    'product_id'      => $product->id,
                ]);

        });
    }

    /**
     * @return $this
     */
    public function withTranslations(): self
    {
        return $this->afterCreating(function (Product $product)
        {

            foreach (ServiceLanguage::getLanguages() as $lang) {
                $product->translations()->create([
                    'title'           => $this->faker->word(),
                    'content_1'       => $this->faker->paragraph(),
                    'content_2'       => $this->faker->paragraph(),
                    'lang'            => $lang,
                    'product_id'      => $product->id,
                ]);
            }
        });

    }

    /**
     * @return array
     */
    public function getTestData(): array
    {
        $data = $this
            ->withTags()
            ->withImages()
            ->make()->toArray();

        return [
            'form' => $this->getFormData($data),
            'db'   => $this->getDbData($data),
        ];
    }

    /**
     * @return $this
     */
    public function withImages(): self
    {
        return $this->afterCreating(function (Product $product)
        {
            for ($i = 0; $i < 3; $i++) { // 假設生成 3 張圖片
                $product->images()->create([
                    'is_default'   => $i === 0, // 第一張圖片設置為默認
                    'sort'         => $i + 1,
                    'image'        => ServiceStorage::getDefaultImagePath(),
                    'image_mobile' => ServiceStorage::getDefaultImagePath(),
                    'product_id'   => $product->id,
                ]);
            }
        })->afterMaking(function (Product $product)
        {
            $images = collect();

            for ($i = 0; $i < 3; $i++) { // 假設生成 3 張圖片

                $images->push(new ProductImage([
                    'is_default'   => $i === 0,
                    'sort'         => $i + 1,
                    'image'        => ServiceStorage::getDefaultImagePath(),
                    'image_mobile' => ServiceStorage::getDefaultImagePath(),
                    'product_id'   => $product->id,
                ]));
            }

            // 使用 `setRelation` 方法將關聯設置在 `Product` 模型上
            $product->setRelation('images', $images);
        });
    }

    /**
     * @return $this
     */
    public function withTags(): self
    {
        return $this->afterCreating(function (Product $product)
        {
            $tags = Tag::query()->whereIn('type', [
                EnumTagType::SHARED,
                EnumTagType::PRODUCT,
            ])->inRandomOrder()->take(2)->get();
            $product->tags()->attach($tags->pluck('id')->toArray());
        });
    }

    function getFormData(array $data): array
    {
        foreach ($data as $key => $value) {

            if ($key === 'image' || $key === 'image_mobile') {
                $data[$key] = [UploadedFile::fake()->image('test-image.jpg')];
            } else {
                if (is_array($value)) {
                    $data[$key] = $this->getFormData($value);
                }
            }

        }

        return $data;
    }

    function getDbData(array $data): array
    {
        foreach ($data as $key => $value) {

            $list = ['tags', 'images', 'translations', 'started_at', 'ended_at', 'created_at', 'image', 'image_mobile'];

            if (in_array($key, $list)) {
                unset($data[$key]);
            } else {
                if (is_array($value)) {
                    $data[$key] = $this->getDbData($value);
                }
            }
        }

        return $data;
    }

    public function getTranslations(): array
    {
        $translations = [];
        foreach (ServiceLanguage::getLanguages() as $lang) {

            $translations[$lang][] = [
                'title'           => $this->faker->word(),
                'content_1'       => $this->faker->word(),
                'content_2'       => $this->faker->word(),
                'description'     => $this->faker->paragraph(),
                'lang'            => $lang,
            ];
        }

        return $translations;
    }


}
