<?php

namespace Stephenchenorg\BaseFilamentPlugin\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Stephenchenorg\BaseFilamentPlugin\Models\Cities;
use Stephenchenorg\BaseFilamentPlugin\Models\Zone;

class ZoneFactory extends Factory
{
    protected $model = Zone::class;

    public function definition(): array
    {
        return [
            'name'              => fake()->name(),
            'postal_code'       => fake()->text(10),
            'key'               => fake()->text(10),
            'city_id'           => Cities::factory(),
        ];
    }
}
