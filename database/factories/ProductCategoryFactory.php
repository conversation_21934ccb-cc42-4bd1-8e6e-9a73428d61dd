<?php

namespace Stephenchenorg\BaseFilamentPlugin\Database\Factories;

use Stephenchenorg\BaseFilamentPlugin\Enum\EnumProductType;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductCategory;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceLanguage;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceStorage;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<ProductCategory>
 */
class ProductCategoryFactory extends Factory
{
    protected $model = ProductCategory::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {

        return [
            'type'         => EnumProductType::Product,
            'image_mobile' => ServiceStorage::getDefaultImagePath(),
            'image'        => ServiceStorage::getDefaultImagePath(),
            'is_hottest'   => $this->faker->boolean(),
            'status'       => $this->faker->boolean(),
            'sort'         => $this->faker->numberBetween(1, 100),
            'count_total'  => 0,
        ];
    }

    /**
     * 定義子分類狀態，讓子分類可以指定父分類
     *
     * @param  ProductCategory  $parent
     * @return $this
     */
    public function withParent(ProductCategory $parent): self
    {
        return $this->state(function () use ($parent)
        {
            return [
                'type'      => $parent->type,
                'parent_id' => $parent->id,
            ];
        });
    }

    /**
     * 定義翻譯數據
     *
     * @return $this
     */
    public function withTranslations(): self
    {
        return $this
            ->afterCreating(function (ProductCategory $productCategory)
            {
                foreach (ServiceLanguage::getLanguages() as $lang) {
                    $productCategory->translations()->create([
                        'title'               => $this->faker->word(),
                        'lang'                => $lang,
                        'product_category_id' => $productCategory->id,
                    ]);
                }
            });
    }

    public function getTranslations(): array
    {
        $translations = [];
        foreach (ServiceLanguage::getLanguages() as $lang) {
            $translations[$lang][] = [
                'title'           => $this->faker->word(),
                'content_1'       => $this->faker->paragraph(),
                'content_2'       => $this->faker->paragraph(),
                'lang'            => $lang,
            ];
        }

        return $translations;
    }
}
