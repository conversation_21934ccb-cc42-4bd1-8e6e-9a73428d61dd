<?php

namespace Stephenchenorg\BaseFilamentPlugin\Database\Factories;

use Stephenchenorg\BaseFilamentPlugin\Models\Customer;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends Factory<Customer>
 */
class CustomerFactory extends Factory
{
    protected $model = Customer::class;
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->name(),
            'email' => $this->faker->unique()->safeEmail(),
            'password' => 'password',
            'phone' => $this->faker->phoneNumber(),
            'email_verified_at' => now(),
            'remember_token' => Str::random(10),
        ];
    }

    /**
     * Indicate that the model's email address should be unverified.
     */
    public function unverified(): static
    {
        return $this->state(fn(array $attributes) => [
            'email_verified_at' => null,
        ]);
    }

    /**
     * Add shipping addresses to the customer
     */
    public function withShippingAddresses(int $count = 2): static
    {
        return $this->afterCreating(function (Customer $customer) use ($count) {
            for ($i = 0; $i < $count; $i++) {
                $customer->shippingAddresses()->create([
                    'is_default' => $i === 0,
                    'country_code' => 'TWN',
                    'state' => '台灣',
                    'city' => $this->faker->city(),
                    'district' => $this->faker->word(),
                    'postal_code' => $this->faker->postcode(),
                    'address_line1' => $this->faker->streetAddress(),
                    'address_line2' => $this->faker->secondaryAddress(),
                ]);
            }
        });
    }

    /**
     * Add subscriptions to the customer
     */
    public function withSubscription(): static
    {
        return $this->afterCreating(function (Customer $customer) {
            $customer->subscriptions()->create([
                'email' => $customer->email,
            ]);
        });
    }
}
