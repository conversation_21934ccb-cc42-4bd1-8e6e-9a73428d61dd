<?php

namespace Stephenchenorg\BaseFilamentPlugin\Database\Factories;

use Stephenchenorg\BaseFilamentPlugin\Models\Product;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductAttribute;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductAttributeItem;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceLanguage;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<ProductAttribute>
 */
class ProductAttributeFactory extends Factory
{
    protected $model = ProductAttribute::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'product_id' => Product::query()->inRandomOrder()->first()->id,
        ];
    }

    /**
     * Add translations if needed.
     *
     * @return $this
     */
    public function withItems(): self
    {
        return $this->afterCreating(function (ProductAttribute $productAttribute)
        {
            ProductAttributeItem::factory()
                ->count(3)
                ->withTranslations()
                ->create([
                    'product_attribute_id' => $productAttribute->id,
                ]);
        });
    }

    /**
     * Add translations if needed.
     *
     * @return $this
     */
    public function withTranslations(): self
    {
        return $this->afterCreating(function (ProductAttribute $productAttribute)
        {
            foreach (ServiceLanguage::getLanguages() as $lang) {
                $productAttribute->translations()->create([
                    'lang'                 => $lang,
                    'title'                => $this->faker->word(),
                    'product_attribute_id' => $productAttribute->id,
                ]);
            }
        });
    }


}
