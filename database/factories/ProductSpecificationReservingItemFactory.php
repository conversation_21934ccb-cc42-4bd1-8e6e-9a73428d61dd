<?php

namespace Stephenchenorg\BaseFilamentPlugin\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductSpecification;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductSpecificationReservingItem;

/**
 * @extends Factory<ProductSpecificationReservingItem>
 */
class ProductSpecificationReservingItemFactory extends Factory
{
    protected $model = ProductSpecificationReservingItem::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'product_specification_id' => ProductSpecification::query()->inRandomOrder()->first()->id,
            'quantity' => $this->faker->numberBetween(1, 10),
        ];
    }
}
