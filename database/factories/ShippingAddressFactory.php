<?php

namespace Stephen<PERSON>org\BaseFilamentPlugin\Database\Factories;

use Stephen<PERSON>org\BaseFilamentPlugin\Enum\EnumAddressType;
use <PERSON><PERSON>org\BaseFilamentPlugin\Models\CustomAddress;
use <PERSON><PERSON>org\BaseFilamentPlugin\Models\ShippingAddress;
use Stephen<PERSON>org\BaseFilamentPlugin\Models\StoreAddress;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<ShippingAddress>
 */
class ShippingAddressFactory extends Factory
{

    protected $model = ShippingAddress::class;

    public function definition(): array
    {
        $type = $this->faker->randomElement(EnumAddressType::cases())->value;

        if($type === EnumAddressType::STORE->value)
        {
            $data = [
                'store_address_id' => $this->getStoreAddress()->id,
            ];
        }
        else
        {
            $data =  $this->getCustomAddress();
        }

        return [
            'address_type' => EnumAddressType::STORE,
            'is_default'   => $this->faker->boolean(50),
            ...$data,
        ];
    }

    public function getStoreAddress(): StoreAddress
    {
        return StoreAddress::factory()->create();
    }

    public function getCustomAddress(): array
    {
        return [
            'country_code'  => 'TWN',
            'state'         => '台灣',
            'city'          => $this->faker->city,
            'district'      => $this->faker->citySuffix,
            'postal_code'   => $this->faker->postcode,
            'address_line1' => $this->faker->address,
            'address_line2' => $this->faker->optional()->address,
        ];
    }


}
