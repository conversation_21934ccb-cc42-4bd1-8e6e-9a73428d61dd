<?php

namespace Stephenchenorg\BaseFilamentPlugin\Database\Factories;

use Stephenchenorg\BaseFilamentPlugin\Models\Product;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductSpecification;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceLanguage;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<ProductSpecification>
 */
class ProductSpecificationFactory extends Factory
{
    protected $model = ProductSpecification::class;

    public function definition(): array
    {
        return [
            'listing_price' => $this->faker->randomFloat(2, 100, 1000),
            'selling_price' => $this->faker->randomFloat(2, 80, 900),
            'inventory'     => $this->faker->numberBetween(0, 1000),
            'type'          => $this->faker->word(),
            'sku'           => "SKU-{$this->faker->unique()->word()}",
            'product_id'    => Product::query()->inRandomOrder()->first()->id,
            'status'        => $this->faker->randomElement([0, 1]), // 0 = 關閉, 1 = 開啟
            'sort'          => $this->faker->numberBetween(0, 100),
        ];
    }

    public function withTranslations(): self
    {
        return $this->afterCreating(function (ProductSpecification $productSpecification)
        {
            // 使用 ServiceLanguage 來獲取語言列表並創建翻譯
            foreach (ServiceLanguage::getLanguages() as $lang) {
                $productSpecification->translations()->create([
                    'title'   => $this->faker->word(),
                    'content' => $this->faker->paragraph(),
                    'lang'    => $lang,  // 使用 ServiceLanguage 中的語言值
                ]);
            }
        });
    }

    public function getTranslations(): array
    {
        $translations = [];

        // 使用 ServiceLanguage 來獲取語言列表
        foreach (ServiceLanguage::getLanguages() as $lang) {
            $translations[] = [
                'title'   => $this->faker->word(),
                'content' => $this->faker->paragraph(),
                'lang'    => $lang,  // 使用 ServiceLanguage 中的語言值
            ];
        }

        return $translations;
    }
}
