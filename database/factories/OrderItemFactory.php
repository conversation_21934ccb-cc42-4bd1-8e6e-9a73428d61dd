<?php

namespace Stephen<PERSON>org\BaseFilamentPlugin\Database\Factories;

use Stephenchenorg\BaseFilamentPlugin\Models\OrderItem;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductSpecification;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<OrderItem>
 */
class OrderItemFactory extends Factory
{
    protected $model = OrderItem::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $spec = ProductSpecification::query()->inRandomOrder()->first();
        return [
            'product_specification_id' => $spec->id,
            'title'                    => $spec->translations->first()->title,
            'quantity'                 => $this->faker->numberBetween(1, 10),
            'unit_price'               => $this->faker->randomFloat(2, 5, 500),
            'total_amount'              => function (array $attributes)
            {
                return $attributes['unit_price'] * $attributes['quantity'];
            },
        ];
    }
}
