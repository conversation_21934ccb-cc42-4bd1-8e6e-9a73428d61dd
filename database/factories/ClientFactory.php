<?php

namespace Stephenchenorg\BaseFilamentPlugin\Database\Factories;

use Stephenchenorg\BaseFilamentPlugin\Models\Client;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends Factory<Client>
 */
class ClientFactory extends Factory
{
    protected $model = Client::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'email' => $this->faker->unique()->safeEmail(),
            'password' => 'password',
            'code' => $this->faker->unique()->regexify('[A-Z]{3}[0-9]{4}'),
            'name' => $this->faker->company(),
            'invoice_title' => $this->faker->company(),
            'address' => $this->faker->address(),
            'invoice_address' => $this->faker->address(),
            'phone1' => $this->faker->phoneNumber(),
            'phone2' => $this->faker->optional(0.5)->phoneNumber(),
            'phone1_extension' => $this->faker->optional(0.7)->numerify('###'),
            'phone2_extension' => $this->faker->optional(0.3)->numerify('###'),
            'fax' => $this->faker->optional(0.6)->phoneNumber(),
            'mobile' => $this->faker->phoneNumber(),
            'contact_person' => $this->faker->name(),
        ];
    }

    /**
     * Indicate that the model's email address should be unverified.
     */
    public function unverified(): static
    {
        return $this->state(fn(array $attributes) => [
            'email_verified_at' => null,
        ]);
    }

    /**
     * Add shipping addresses to the client
     */
    public function withShippingAddresses(int $count = 2): static
    {
        return $this->afterCreating(function (Client $client) use ($count) {
            for ($i = 0; $i < $count; $i++) {
                $client->shippingAddresses()->create([
                    'is_default' => $i === 0,
                    'country_code' => 'TWN',
                    'state' => '台灣',
                    'city' => $this->faker->city(),
                    'district' => $this->faker->word(),
                    'postal_code' => $this->faker->postcode(),
                    'address_line1' => $this->faker->streetAddress(),
                    'address_line2' => $this->faker->secondaryAddress(),
                ]);
            }
        });
    }

    /**
     * Add subscriptions to the client
     */
    public function withSubscription(): static
    {
        return $this->afterCreating(function (Client $client) {
            $client->subscriptions()->create([
                'email' => $client->email,
            ]);
        });
    }
}
