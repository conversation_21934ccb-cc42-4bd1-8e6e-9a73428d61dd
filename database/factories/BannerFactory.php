<?php

namespace Stephenchenorg\BaseFilamentPlugin\Database\Factories;

use Stephenchenorg\BaseFilamentPlugin\Models\Banner;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceStorage;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Facades\Storage;

/**
 * @extends Factory<Banner>
 */
class BannerFactory extends Factory
{
    protected $model = Banner::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {

        return [
            'cta_title'    => $this->faker->words(2, true), // CTA 標題
            'cta_link'     => $this->faker->url(), // CTA 連結
            'title'        => $this->faker->words(2, true), // 標題
            'status'       => $this->faker->randomElement([0, 1]), // 狀態，0: 關閉，1: 開啟
            'sort'         => $this->faker->numberBetween(0, 100), // 排序
            'image'        => ServiceStorage::getDefaultImagePath(), // 圖片
            'image_mobile' => ServiceStorage::getDefaultImagePath(), // 圖標
        ];
    }

    /**
     * @return void
     */
    public function createProductionData(): void
    {
        Banner::query()->insert([
            [
                'status' => 1,
                'sort'   => 6,
                'image'  => 'image/橫幅1.jpg',
            ],
            [
                'status' => 1,
                'sort'   => 5,
                'image'  => 'image/橫幅2.jpg',
            ],
            [
                'status' => 1,
                'sort'   => 4,
                'image'  => 'image/橫幅3.jpg',
            ],
            [
                'status' => 1,
                'sort'   => 3,
                'image'  => 'image/橫幅4.jpg',
            ],
            [
                'status' => 1,
                'sort'   => 2,
                'image'  => 'image/橫幅5.jpg',
            ],
            [
                'status' => 1,
                'sort'   => 1,
                'image'  => 'image/橫幅6.jpg',
            ],
        ]);
    }
}
