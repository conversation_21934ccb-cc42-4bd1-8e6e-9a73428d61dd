<?php

namespace Stephenchenorg\BaseFilamentPlugin\Database\Factories;

use Stephenchenorg\BaseFilamentPlugin\Models\ArticleCategory;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceLanguage;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceStorage;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<ArticleCategory>
 */
class ArticleCategoryFactory extends Factory
{
    protected $model = ArticleCategory::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'key'          => $this->faker->unique()->word(), // 唯一鍵
            'status'       => $this->faker->randomElement([0, 1]), // 狀態 0: 關閉, 1: 開啟
            'sort'         => $this->faker->numberBetween(0, 100), // 排序
            'image'        => ServiceStorage::getDefaultImagePath(),
            'image_mobile' => ServiceStorage::getDefaultImagePath(),
            'slug'         => $this->faker->slug(), // 唯一的slug
        ];
    }

    /**
     * @return $this
     */
    public function withTranslations(): self
    {
        return $this->afterCreating(function (ArticleCategory $articleCategory)
        {

            foreach (ServiceLanguage::getLanguages() as $lang) {
                $articleCategory->translations()->create([
                    'title'               => $this->faker->word(),
                    'content'             => $this->faker->text(),
                    'lang'                => $lang,
                    'article_category_id' => $articleCategory->id,
                ]);
            }
        });
    }

    public function getTranslations(): array
    {
        $translations = [];

        foreach (ServiceLanguage::getLanguages() as $lang) {
            $translations[$lang][] = [
                'title'           => $this->faker->word(),
                'content'         => $this->faker->text(),
                'lang'            => $lang,
            ];
        }

        return $translations;
    }
}
