<?php

namespace Stephenchenorg\BaseFilamentPlugin\Database\Factories;

use Stephenchenorg\BaseFilamentPlugin\Enum\EnumFieldType;
use Stephenchenorg\BaseFilamentPlugin\Models\Page;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceLanguage;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<Page>
 */
class PageFactory extends Factory
{
    protected $model = Page::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'key'        => $this->faker->unique()->word(),
            'slug'       => $this->faker->slug(),
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }

    /**
     * @return $this
     */
    public function withTranslations(): self
    {
        return $this->afterCreating(function (Page $page)
        {
            foreach (ServiceLanguage::getLanguages() as $lang) {
                $page->translations()->create([
                    'title' => $this->faker->word(),
                    'lang'  => $lang,
                ]);
            }
        })->afterMaking(function (Page $page)
        {
            $translations = [];
            foreach (ServiceLanguage::getLanguages() as $lang) {
                $translations[] = [
                    'title' => $this->faker->word(),
                    'lang'  => $lang,
                ];
            }
            $page->setRelation('translations', $translations);
        });
    }

    /**
     * 為 Page 新增指定數量的 Fields 和翻譯
     *
     * @param  int  $num
     * @return $this
     */
    public function withFields(int $num): self
    {
        return $this->afterCreating(function (Page $page) use ($num)
        {
            for ($i = 0; $i < $num; $i++) {
                $pageField = $page->fields()->create([
                    'key'         => $this->faker->unique()->word(),
                    'type'        => EnumFieldType::TEXT->value,
                    'helper_text' => $this->faker->word(),
                ]);

                foreach (ServiceLanguage::getLanguages() as $lang) {
                    $pageField->translations()->create([
                        'content' => $this->faker->word(),
                        'lang'    => $lang,
                    ]);
                }
            }
        })
            ->afterMaking(function (Page $page) use ($num)
            {
                for ($i = 0; $i < $num; $i++) {
                    $fields = [
                        'key'         => $this->faker->unique()->word(),
                        'type'        => EnumFieldType::TEXT->value,
                        'helper_text' => $this->faker->word(),
                    ];
                }
                $page->setRelation('fields', $fields);
            });
    }

    /**
     * 預設資料
     */
    public function createProductionData(): void
    {
        $this->createHomePage();
        $this->createConsultancyPage();
        $this->createAboutUsPage();
        $this->createExhibitionPage();
        $this->createArtistPage();

    }


    public function createHomePage(): void
    {
        $page = Page::query()->create([
            'key'        => 'home',
            'slug'       => 'home',
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        $page->translations()->createMany([
            [
                'lang'  => 'zh_TW',
                'title' => '首頁',
            ],
            [
                'lang'  => 'en',
                'title' => 'Home',
            ],
        ]);

        $fields = [
            'section_1_title'       => [
                'type'         => EnumFieldType::HTML->value,
                'helper_text'  => 'Section 1 Title',
                'translations' => [
                    'zh_TW' => ['content' => '<h2 class="section-title margin-b32">藝術：『不是表面所見，而是無限的可能』。</h2>'],
                    'en'    => ['content' => '<h2 class="section-title margin-b32">Art: "Not What It Appears, But Infinite Possibilities."</h2>'],
                ],
            ],
            'section_1_description' => [
                'type'         => EnumFieldType::HTML->value,
                'helper_text'  => 'Section 1 Description',
                'translations' => [
                    'zh_TW' => ['content' => '<h1 style="margin-left:0px;"><span style="background-color:rgb(255,255,255);color:rgb(133,133,133);font-family:&quot;Open Sans&quot;, sans-serif;font-size:16px;"><span style="-webkit-text-stroke-width:0px;caret-color:rgb(133, 133, 133);display:inline !important;float:none;font-style:normal;font-variant-caps:normal;font-weight:400;letter-spacing:normal;orphans:auto;text-align:left;text-decoration:none;text-indent:0px;text-transform:none;white-space:normal;widows:auto;word-spacing:0px;">Art is not what is seen on the surface, but infinite possibilities.</span></span></h1>'],
                    'en'    => ['content' => '<h1 style="margin-left:0px;"><span style="background-color:rgb(255,255,255);color:rgb(133,133,133);font-family:&quot;Open Sans&quot;, sans-serif;font-size:16px;"><span style="-webkit-text-stroke-width:0px;caret-color:rgb(133, 133, 133);display:inline !important;float:none;font-style:normal;font-variant-caps:normal;font-weight:400;letter-spacing:normal;orphans:auto;text-align:left;text-decoration:none;text-indent:0px;text-transform:none;white-space:normal;widows:auto;word-spacing:0px;">Art is not what is seen on the surface, but infinite possibilities.</span></span></h1>'],
                ],
            ],
            'section_1_image'       => [
                'type'         => EnumFieldType::IMAGE->value,
                'helper_text'  => 'Section 1 Image',
                'translations' => [
                    'zh_TW' => ['content' => 'images/中間.jpg'],
                    'en'    => ['content' => 'images/中間.jpg'],
                ],
            ],

            'section_2_title' => [
                'type'         => EnumFieldType::TEXT->value,
                'helper_text'  => 'Section 2 Title',
                'translations' => [
                    'zh_TW' => ['content' => '<h3 class="section-title margin-b32">Pablo Picasso: &rdquo;All children are artists. The problem is how to remain an artist once he grows up.&rdquo;</h3>'],
                    'en'    => ['content' => '<h3 class="section-title margin-b32">Pablo Picasso: "All children are artists. The problem is how to remain an artist once he grows up."</h3>'],
                ],
            ],

            'section_2_description' => [
                'type'            => EnumFieldType::TEXTAREA->value,
                'helper_text'     => 'Section 2 Description',
                'translations'    => [
                    'zh_TW' => [
                        'content' => '畢卡索：每一個人生來都是藝術家，但有多少人能維持「藝術」特質到長大。 一旦我們社會化，受到許多制約及洗腦，體內的藝術因子就逐漸消失。 我們可從接觸藝術的過程中，找回感覺，並藉此療癒自己，活出高度價值的生命。

Once we are socialized and subjected to many restrictions and brainwashing, the artistic factor in our body will gradually disappear. Through the process of encountering art, we can regain our senses, and use them to heal ourselves and live a life of high value.',
                    ],
                    'en'    => [
                        'content' => '畢卡索：每一個人生來都是藝術家，但有多少人能維持「藝術」特質到長大。 一旦我們社會化，受到許多制約及洗腦，體內的藝術因子就逐漸消失。 我們可從接觸藝術的過程中，找回感覺，並藉此療癒自己，活出高度價值的生命。

Once we are socialized and subjected to many restrictions and brainwashing, the artistic factor in our body will gradually disappear. Through the process of encountering art, we can regain our senses, and use them to heal ourselves and live a life of high value.',
                    ],
                ],
                'section_2_image' => [
                    'type'         => EnumFieldType::IMAGE->value,
                    'helper_text'  => 'Section 2 Image',
                    'translations' => [
                        'zh_TW' => ['content' => 'images/Picasso.jpg'],
                        'en'    => ['content' => 'images/Picasso.jpg'],
                    ],
                ],
            ],
        ];

        foreach ($fields as $key => $field) {
            $pageField = $page->fields()->create([
                'key'         => $key,
                'helper_text' => $field['helper_text'],
                'type'        => $field['type'],
            ]);

            foreach ($field['translations'] as $lang => $translation) {
                $pageField->translations()->create([
                    'lang'    => $lang,
                    'content' => $translation['content'],
                ]);
            }
        }
    }

    public function createConsultancyPage(): void
    {
        $page = Page::query()->create([
            'key'        => 'consultancy',
            'slug'       => 'consultancy',
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        $page->translations()->createMany([
            [
                'lang'  => 'zh_TW',
                'title' => '藝術諮詢',
            ],
            [
                'lang'  => 'en',
                'title' => 'Art Consultancy',
            ],
        ]);

        $fields = [
            'section_1_title'       => [
                'type'         => EnumFieldType::TEXT->value,
                'helper_text'  => 'section_1 Title',
                'translations' => [
                    'zh_TW' => ['content' => '藝術諮詢'],
                    'en'    => ['content' => 'Art Consultancy'],
                ],
            ],
            'section_1_image'       => [
                'type'         => EnumFieldType::IMAGE->value,
                'helper_text'  => 'section_1 image',
                'translations' => [
                    'zh_TW' => ['content' => 'images/top-consultancies.jpg'],
                    'en'    => ['content' => 'images/top-consultancies.jpg'],
                ],
            ],
            'section_1_description' => [
                'type'         => EnumFieldType::TEXTAREA->value,
                'helper_text'  => 'section_1 Description',
                'translations' => [
                    'zh_TW' => [
                        'content' => '
我們會不定期發布藝術文章，若您有藝術相關問題，亦歡迎詢問。
華人藝術市場發展僅30年，仍在起步。人類在衣食無虞後，自然會找尋生命缺少的一塊，並走向心靈精神層次，從聰明進化到智慧。',
                    ],
                    'en'    => [
                        'content' => 'We regularly publish art articles, and if you have any art-related questions, please feel free to inquire.
The Chinese art market has only been developing for 30 years and is still in its early stages. When people have satisfied their basic needs, they naturally seek to fill the missing piece in life and progress towards a higher level of spiritual and mental fulfillment, evolving from cleverness to wisdom.',
                    ],
                ],
            ],
        ];

        foreach ($fields as $key => $field) {
            $pageField = $page->fields()->create([
                'key'         => $key,
                'helper_text' => $field['helper_text'],
                'type'        => $field['type'],
            ]);

            foreach ($field['translations'] as $lang => $translation) {
                $pageField->translations()->create([
                    'lang'    => $lang,
                    'content' => $translation['content'],
                ]);
            }
        }
    }

    public function createAboutUsPage(): void
    {
        $page = Page::query()->create([
            'key'        => 'about-us',
            'slug'       => 'about-us',
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        $page->translations()->createMany([
            [
                'lang'  => 'zh_TW',
                'title' => '關於我們',
            ],
            [
                'lang'  => 'en',
                'title' => 'About Us',
            ],
        ]);

        $fields = [
            'section_1_title'       => [
                'type'         => EnumFieldType::TEXT->value,
                'helper_text'  => 'section_1 Title',
                'translations' => [
                    'zh_TW' => ['content' => '關於我們'],
                    'en'    => ['content' => 'About Us'],
                ],
            ],
            'section_1_image'       => [
                'type'         => EnumFieldType::IMAGE->value,
                'helper_text'  => 'section_1 image',
                'translations' => [
                    'zh_TW' => ['content' => 'images/top-about.jpg'],
                    'en'    => ['content' => 'images/top-about.jpg'],
                ],
            ],
            'section_1_description' => [
                'type'         => EnumFieldType::TEXTAREA->value,
                'helper_text'  => 'section_1 Description',
                'translations' => [
                    'zh_TW' => ['content' => 'WHAT IS ART?'],
                    'en'    => ['content' => 'WHAT IS ART?'],
                ],
            ],
            'section_2_content'     => [
                'type'         => EnumFieldType::HTML->value,
                'helper_text'  => 'section_2 Content',
                'translations' => [
                    'zh_TW' => [
                        'content' => '<h2>什麼是藝術？</h2>
<p><span style="color: #808080;">藝術在歐洲已普及與成熟，華人也正在快速發展，無論上海、北京、香港、新加坡各地，藝術活動愈來愈多，也是熱鬧。</span></p>
<p><span style="color: #808080;">然而，真正懂得藝術本質定義的人卻相當少；而能從藝術中體會無價的心靈精神價值並應用者，更是屈指可數。</span></p>
<p><span style="color: #808080;">整個華人藝術市場不缺賣作品的人，但民眾非常需要有藝術專業的人，將他們帶入藝術核心。這樣的人我們稱之為「藝術大使」。</span></p>
<p><span style="color: #808080;">藝術的定義是什麼？對人類的真正價值呢？為什麼歐洲先進各國，到處都是美術館？</span></p>
<p><span style="color: #808080;">一個人擁有正確的藝術觀點，再去擁有藝術品，作品才對他們有價值及作用，帶來正向之生活；若尚無，可以先去體認藝術，與我們每一個細胞產生作用後，有緣分再去收藏吧。</span></p>',
                    ],
                    'en'    => [
                        'content' => '<h2>What is Art?</h2>
<p><span style="color: #808080;">Art is already widespread and mature in Europe, and it is also developing rapidly among Chinese communities. Whether in Shanghai, Beijing, Hong Kong, or Singapore, art activities are becoming more frequent and vibrant.</span></p>
<p><span style="color: #808080;">However, there are very few people who truly understand the essence of art; even fewer are those who can experience its invaluable spiritual value and apply it to life.</span></p>
<p><span style="color: #808080;">The entire Chinese art market lacks professionals to guide people into the core of art, though there is no shortage of those selling art pieces. We call such professionals "Art Ambassadors."</span></p>
<p><span style="color: #808080;">What is the definition of art? What is its true value to humanity? Why are art museums everywhere in advanced European countries?</span></p>
<p><span style="color: #808080;">When a person has a correct perspective on art and then acquires art pieces, these pieces have value and meaning in their life, bringing positive impacts. If not, one can first understand art, experience its influence, and then consider collecting when the connection is right.</span></p>',
                    ],
                ],
            ],
            'section_2_footer'      => [
                'type'         => EnumFieldType::TEXTAREA->value,
                'helper_text'  => 'section_2_footer Description',
                'translations' => [
                    'zh_TW' => ['content' => '我們擁有藝術顧問（大使），遍及海內外，亦是一個顧問諮詢單位，歡迎聯絡我們。'],
                    'en'    => ['content' => 'We have art consultants (ambassadors) around the world and act as an advisory unit. Feel free to contact us.'],
                ],
            ],

            'section_2_footer_cta_title' => [
                'type'         => EnumFieldType::TEXT->value,
                'helper_text'  => 'section_2_footer_cta Title',
                'translations' => [
                    'zh_TW' => ['content' => '聯絡我們。'],
                    'en'    => ['content' => 'Contact Us'],
                ],
            ],

            'section_2_footer_cta_link' => [
                'type'         => EnumFieldType::TEXT->value,
                'helper_text'  => 'section_2_footer_cta Link',
                'translations' => [
                    'zh_TW' => ['content' => 'https://www.powen.art/about/'],
                    'en'    => ['content' => 'https://www.powen.art/about/'],
                ],
            ],
        ];

        foreach ($fields as $key => $field) {
            $pageField = $page->fields()->create([
                'key'         => $key,
                'helper_text' => $field['helper_text'],
                'type'        => $field['type'],
            ]);

            foreach ($field['translations'] as $lang => $translation) {
                $pageField->translations()->create([
                    'lang'    => $lang,
                    'content' => $translation['content'],
                ]);
            }
        }
    }

    public function createExhibitionPage(): void
    {
        $page = Page::query()->create([
            'key'        => 'exhibitions',
            'slug'       => 'exhibitions',
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        $page->translations()->createMany([
            [
                'lang'  => 'zh_TW',
                'title' => '展覽',
            ],
            [
                'lang'  => 'en',
                'title' => 'Exhibitions',
            ],
        ]);

        $fields = [
            'section_1_title'       => [
                'type'         => EnumFieldType::TEXT->value,
                'helper_text'  => 'section_1 Title',
                'translations' => [
                    'zh_TW' => ['content' => '展覽'],
                    'en'    => ['content' => 'Exhibitions'],
                ],
            ],
            'section_1_image'       => [
                'type'         => EnumFieldType::IMAGE->value,
                'helper_text'  => 'section_1 image',
                'translations' => [
                    'zh_TW' => ['content' => 'images/top-galleries.jpg'],
                    'en'    => ['content' => 'images/top-galleries.jpg'],
                ],
            ],
            'section_1_description' => [
                'type'         => EnumFieldType::TEXTAREA->value,
                'helper_text'  => 'section_1 Description',
                'translations' => [
                    'zh_TW' => [
                        'content' => '我們過去以來舉辦百場以上展覽，裝置、新媒體、雕塑、繪畫皆有，讓許多人藉此進入藝術，自我體會藝術本質與價值。',
                    ],
                    'en'    => [
                        'content' => "Over the years, we have held over a hundred exhibitions, featuring installations, new media, sculpture, and painting. These events have allowed many people to experience art and gain a personal understanding of its essence and value.",
                    ],
                ],
            ],
        ];

        foreach ($fields as $key => $field) {
            $pageField = $page->fields()->create([
                'key'         => $key,
                'helper_text' => $field['helper_text'],
                'type'        => $field['type'],
            ]);

            foreach ($field['translations'] as $lang => $translation) {
                $pageField->translations()->create([
                    'lang'    => $lang,
                    'content' => $translation['content'],
                ]);
            }
        }
    }

    public function createArtistPage(): void
    {
        $page = Page::query()->create([
            'key'        => 'artists',
            'slug'       => 'artists',
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        $page->translations()->createMany([
            [
                'lang'  => 'zh_TW',
                'title' => '藝術家',
            ],
            [
                'lang'  => 'en',
                'title' => 'Artists',
            ],
        ]);

        $fields = [
            'section_1_title'       => [
                'type'         => EnumFieldType::TEXT->value,
                'helper_text'  => 'section_1 Title',
                'translations' => [
                    'zh_TW' => ['content' => '藝術家'],
                    'en'    => ['content' => 'Artists'],
                ],
            ],
            'section_1_image'       => [
                'type'         => EnumFieldType::IMAGE->value,
                'helper_text'  => 'section_1 image',
                'translations' => [
                    'zh_TW' => ['content' => 'images/top-artists.jpg'],
                    'en'    => ['content' => 'images/top-artists.jpg'],
                ],
            ],
            'section_1_description' => [
                'type'         => EnumFieldType::TEXT->value,
                'helper_text'  => 'section_1 Description',
                'translations' => [
                    'zh_TW' => [
                        'content' => '這裡有我們合作、代理的藝術家。若您有興趣深入了解，歡迎聯絡我們。',
                    ],
                    'en'    => [
                        'content' => "Here are the artists we collaborate with and represent. If you're interested in learning more, feel free to contact us.",
                    ],
                ],
            ],
        ];

        foreach ($fields as $key => $field) {
            $pageField = $page->fields()->create([
                'key'         => $key,
                'helper_text' => $field['helper_text'],
                'type'        => $field['type'],
            ]);

            foreach ($field['translations'] as $lang => $translation) {
                $pageField->translations()->create([
                    'lang'    => $lang,
                    'content' => $translation['content'],
                ]);
            }
        }
    }


}
