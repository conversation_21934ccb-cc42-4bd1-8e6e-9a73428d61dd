<?php

namespace Stephenchenorg\BaseFilamentPlugin\Database\Factories;

use Stephenchenorg\BaseFilamentPlugin\Models\ProductAttribute;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductAttributeItem;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceLanguage;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<ProductAttributeItem>
 */
class ProductAttributeItemFactory extends Factory
{
    protected $model = ProductAttributeItem::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'product_attribute_id' => ProductAttribute::query()->inRandomOrder()->first()->id,
        ];
    }


    /**
     * Add translations if needed.
     *
     * @return $this
     */
    public function withTranslations(): self
    {
        return $this->afterCreating(function (ProductAttributeItem $item)
        {
            foreach (ServiceLanguage::getLanguages() as $lang) {
                $item->translations()->create([
                    'lang'    => $lang,
                    'title'   => $this->faker->word(),
                    'item_id' => $item->id,
                ]);
            }
        });
    }
}
