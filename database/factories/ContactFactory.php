<?php

namespace <PERSON><PERSON>org\BaseFilamentPlugin\Database\Factories;

use Stephenchenorg\BaseFilamentPlugin\Enum\EnumContactStatus;
use Stephen<PERSON>org\BaseFilamentPlugin\Enum\EnumContactType;
use Stephenchenorg\BaseFilamentPlugin\Models\Contact;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<Contact>
 */
class ContactFactory extends Factory
{
    protected $model = Contact::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [

            'title'   => $this->faker->title(),
            'name'    => $this->faker->name(),
            'phone'   => '+886 912345678',
            'email'   => $this->faker->safeEmail(),
            'line'    => $this->faker->optional()->userName(), // Line ID 是可選的
            'address' => $this->faker->optional()->address(), // 地址是可選的
            'content' => $this->faker->text(200), // 內容是可選的，限制 200 字符
            'status'  => $this->faker->randomElement(EnumContactStatus::cases()), // 狀態欄位：未讀、已讀、已回覆
            'type'    => $this->faker->randomElement(EnumContactType::cases()), // 表單類型

        ];
    }
}
