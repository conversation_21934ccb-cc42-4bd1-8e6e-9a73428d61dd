<?php

use Stephenchenorg\BaseFilamentPlugin\Traits\CCTraitDatabaseContent;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\Database\CCTraitDatabaseAdmin;

class CreateShippingAddressesTable extends Migration
{

    use CCTraitDatabaseContent;

    use CCTraitDatabaseAdmin;

    public function up(): void
    {
        $table = 'shipping_addresses';

        Schema::create('shipping_addresses', function (Blueprint $table) {
            $table->mediumInteger('id')->unsigned()->autoIncrement();
            $table->morphs('addressable');
            $table->boolean('is_default')->default(false);

            $table->smallInteger('store_address_id')->unsigned()->nullable();
            $table->foreign('store_address_id')->references('id')->on('store_addresses')->onDelete('cascade');
            $table->string('country_code',3)->nullable()->default('TWN')->comment('iso 三位國碼');
            $table->string('state',30)->nullable()->default('台灣');
            $table->string('city',30)->nullable();
            $table->string('district',30)->nullable();
            $table->string('postal_code',10)->nullable();
            $table->string('address_line1',100)->nullable();
            $table->string('address_line2',100)->nullable();

            self::getDatabaseColumnsEditor($table);
            $table->timestamps();
        });

        DB::statement("ALTER TABLE `$table` COMMENT = '配送地址資料表'");
    }

    public function down(): void
    {
        Schema::dropIfExists('shipping_addresses');
    }
}
