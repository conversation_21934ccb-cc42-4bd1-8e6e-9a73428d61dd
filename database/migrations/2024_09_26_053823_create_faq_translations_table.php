<?php

use Stephenchenorg\BaseFilamentPlugin\Traits\CCTraitDatabaseContent;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\Database\CCTraitDatabaseAdmin;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\Database\CCTraitDatabaseOG;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\Database\CCTraitDatabaseSEO;

return new class extends Migration {

    use CCTraitDatabaseContent;
    use CCTraitDatabaseAdmin;
    use CCTraitDatabaseSEO;
    use CCTraitDatabaseOG;


    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $table = 'faq_translations';

        if (!config('cs.faq_visible')) {
            Log::info("Skipping creation of $table table as CS_FAQ_VISIBLE is set to false");
            return;
        }

        Schema::create($table, function (Blueprint $table) {
            $table->smallInteger('id')->unsigned()->autoIncrement();
            $table->smallInteger('faq_id')->unsigned();
            $table->foreign('faq_id')->references('id')->on('faqs')->onDelete('cascade');
            self::getDatabaseColumnsSEO($table);
            self::getDatabaseColumnsOG($table);
            $this->addTitleField($table);
            $this->addContentField($table);
            $this->addLanguageField($table);
            self::getDatabaseColumnsEditor($table);
            $table->timestamps();
        });
        DB::statement("ALTER TABLE $table COMMENT = '常見問與答翻譯內容'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('faq_translations');
    }
};
