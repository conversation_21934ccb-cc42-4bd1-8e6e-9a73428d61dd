<?php

use Stephenchenorg\BaseFilamentPlugin\Traits\CCTraitDatabaseContent;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\Database\CCTraitDatabaseAdmin;

class CreatePaymentsTable extends Migration
{

    use CCTraitDatabaseContent;

    use CCTraitDatabaseAdmin;

    public function up(): void
    {
        $table = 'payments';

        Schema::create('payments', function (Blueprint $table) {
            $table->id();
            $table->mediumInteger('order_id')->unsigned()->nullable();
            $table->foreign('order_id')->references('id')->on('orders')->onDelete('CASCADE');
            $table->decimal('amount', 10, 2)->comment('支付金額');
            $table->string('method', 50)->comment('支付方式');
            $table->string('status', 20)->default('pending')->comment('支付狀態');
            $table->string('transaction_id', 100)->nullable()->comment('第三方交易ID');
            self::getDatabaseColumnsEditor($table);
            $table->timestamps();
        });

        DB::statement("ALTER TABLE `$table` COMMENT = '付款資料表'");
    }

    public function down(): void
    {
        Schema::dropIfExists('payments');
    }
}
