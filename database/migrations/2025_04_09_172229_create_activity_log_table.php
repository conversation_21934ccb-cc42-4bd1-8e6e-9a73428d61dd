<?php

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateActivityLogTable extends Migration
{
    public function up(): void
    {
        Schema::connection(config('activitylog.database_connection'))->create(config('activitylog.table_name'), function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('log_name')->nullable();
            $table->text('description');
            $table->nullableMorphs('subject', 'subject');
            $table->nullableMorphs('causer', 'causer');
            $table->json('properties')->nullable();
            $table->unsignedTinyInteger('month');
            $table->timestamps();
            $table->primary(['id', 'month']);
            $table->index('log_name');
        });

        DB::connection(config('activitylog.database_connection'))->statement("
            ALTER TABLE " . config('activitylog.table_name') . "
            PARTITION BY RANGE (month) (
                PARTITION p01 VALUES LESS THAN (2),
                PARTITION p02 VALUES LESS THAN (3),
                PARTITION p03 VALUES LESS THAN (4),
                PARTITION p04 VALUES LESS THAN (5),
                PARTITION p05 VALUES LESS THAN (6),
                PARTITION p06 VALUES LESS THAN (7),
                PARTITION p07 VALUES LESS THAN (8),
                PARTITION p08 VALUES LESS THAN (9),
                PARTITION p09 VALUES LESS THAN (10),
                PARTITION p10 VALUES LESS THAN (11),
                PARTITION p11 VALUES LESS THAN (12),
                PARTITION p12 VALUES LESS THAN (13)
            );
        ");
    }

    public function down(): void
    {
        Schema::connection(config('activitylog.database_connection'))->dropIfExists(config('activitylog.table_name'));
    }
}
