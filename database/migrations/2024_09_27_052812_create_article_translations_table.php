<?php

use Stephenchenorg\BaseFilamentPlugin\Traits\CCTraitDatabaseContent;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Log;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\Database\CCTraitDatabaseAdmin;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\Database\CCTraitDatabaseOG;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\Database\CCTraitDatabaseSEO;

return new class extends Migration {

    use CCTraitDatabaseContent;
    use CCTraitDatabaseAdmin;
    use CCTraitDatabaseSEO;
    use CCTraitDatabaseOG;


    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $table = 'article_translations';

        if (!config('cs.article_visible')) {
            Log::info("Skipping creation of $table table as CS_ARTICLE_VISIBLE is set to false");
            return;
        }

        Schema::create($table, function (Blueprint $table) {
            $table->mediumInteger('id')->unsigned()->autoIncrement();
            $table->mediumInteger('article_id')->unsigned()->nullable();
            $table->foreign('article_id')->references('id')->on('articles')->onDelete('cascade');
            $table->string('author', 100)->nullable()->comment('作者名稱');
            self::getDatabaseColumnsSEO($table);
            self::getDatabaseColumnsOG($table);
            $this->addTitleField($table);
            $this->addDescriptionField($table);
            $this->addContentField($table);
            $this->addLanguageField($table);
            self::getDatabaseColumnsEditor($table);
            $table->timestamps();
        });
        DB::statement("ALTER TABLE $table COMMENT = '文章翻譯內容'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('article_translations');
    }
};
