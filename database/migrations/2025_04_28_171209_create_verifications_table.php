<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('verifications', function (Blueprint $table)
        {
            $table->id();
            $table->string('target');
            $table->string('code', 30);
            $table->unsignedTinyInteger('type')->comment('驗證類型，目前有 email, phone');

            // 多型關聯欄位
            $table->unsignedBigInteger('verifiable_id');
            $table->string('verifiable_type');
            $table->timestamp('verified_at')->nullable()->comment('驗證碼使用時間');
            $table->timestamp('expired_at')->nullable()->comment('驗證碼過期時間');
            $table->timestamps();

            $table->index(['verifiable_id', 'verifiable_type']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('verifications');
    }
};
