<?php

use Stephenchenorg\BaseFilamentPlugin\Traits\CCTraitDatabaseContent;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\Database\CCTraitDatabaseAdmin;

return new class extends Migration
{
    use CCTraitDatabaseAdmin;
    use CCTraitDatabaseContent;

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $table = 'contacts';

        if (!config('cs.contact_visible')) {
            Log::info("Skipping creation of $table table as CS_CONTACT_VISIBLE is set to false");
            return;
        }

        Schema::create($table, function (Blueprint $table)
        {
            $table->mediumInteger('id')->unsigned()->autoIncrement();
            $table->string('type', 20)->default('contact_us')->comment('表單類型');
            $table->string('name', 100)->comment('姓名');
            $table->string('phone', 20)->nullable()->comment('電話'); // 電話號碼，允許空值
            $table->string('email', 70)->nullable()->comment('電子信箱'); // 電子郵件，允許空值
            $table->string('line', 30)->nullable()->comment('Line ID'); // Line ID，允許空值
            $table->string('address', 200)->nullable()->comment('地址'); // 地址，允許空值
            $table->string('title', 80);
            $table->text('content')->nullable()->comment('內容'); // 內容，允許空值
            $table->unsignedTinyInteger('status')->default(0)->comment('狀態，0 未讀，1 已讀 2已回覆');
            self::getDatabaseColumnsEditor($table);
            $table->softDeletes();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('contacts');
    }
};
