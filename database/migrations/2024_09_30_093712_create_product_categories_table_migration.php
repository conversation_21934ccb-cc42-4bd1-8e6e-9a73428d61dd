<?php

use Stephenchenorg\BaseFilamentPlugin\Traits\CCTraitDatabaseContent;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\Database\CCTraitDatabaseAdmin;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\Database\CCTraitDatabaseSort;

class CreateProductCategoriesTableMigration extends Migration
{

    use CCTraitDatabaseContent;
    use CCTraitDatabaseAdmin;
    use CCTraitDatabaseSort;

    public function up(): void
    {
        Schema::create('product_categories', function (Blueprint $table) {

            $table->smallInteger('id')->unsigned()->autoIncrement();
            $table->string('sequence', 1024)->nullable();
            $table->string('code',64)->nullable()->comment('配合廠商的代號');
            $table->integer('parent_id')->default(-1);
            $table->integer('position', false, true)->default(0);
            $table->softDeletes();
            $table->integer('order')->default(0)->index();
            $table->string('type',20)->default('product');
            $this->addStatusField($table);
            self::getDatabaseColumnsSort($table);
            $this->addImageFields($table);
            $this->addCountField($table);
            $table->unsignedTinyInteger('is_hottest')->default(false)->comment('是否最熱門');
            $table->unsignedTinyInteger('is_newest')->default(false); // 是否最新
            self::getDatabaseColumnsEditor($table);

            $table->timestamps();
        });


        Schema::create('product_category_closure', function (Blueprint $table) {
            $table->smallInteger('closure_id')->unsigned()->autoIncrement();

            $table->smallInteger('ancestor')->unsigned();
            $table->smallInteger('descendant')->unsigned();
            $table->integer('depth', false, true);

            $table->foreign('ancestor')
                ->references('id')
                ->on('product_categories')
                ->onDelete('cascade');

            $table->foreign('descendant')
                ->references('id')
                ->on('product_categories')
                ->onDelete('cascade');

        });

        DB::statement("ALTER TABLE `product_categories` COMMENT = '產品類別表'");
        DB::statement("ALTER TABLE `product_category_closure` COMMENT = '產品類別Closure表'");

    }

    public function down()
    {
        DB::statement('SET FOREIGN_KEY_CHECKS = 0');
        Schema::dropIfExists('product_categories');
        Schema::dropIfExists('product_category_closure');
        DB::statement('SET FOREIGN_KEY_CHECKS = 1');
    }
}
