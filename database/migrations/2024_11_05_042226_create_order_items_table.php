<?php

use Stephenchenorg\BaseFilamentPlugin\Traits\CCTraitDatabaseContent;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\Database\CCTraitDatabaseAdmin;

class CreateOrderItemsTable extends Migration
{

    use CCTraitDatabaseContent;
    use CCTraitDatabaseAdmin;

    public function up(): void
    {
        $table = 'order_items';

        Schema::create('order_items', function (Blueprint $table) {
            $table->mediumInteger('id')->unsigned()->autoIncrement();
            $table->mediumInteger('order_id')->unsigned()->nullable();
            $table->foreign('order_id')->references('id')->on('orders')->onDelete('CASCADE');
            $table->mediumInteger('product_specification_id')->unsigned();
            $table->foreign('product_specification_id')->references('id')->on('product_specifications')->onDelete('set null');
            $table->string('title', 100)->comment('商品規格名稱');
            $table->unsignedInteger('quantity')->comment('購買數量');
            $table->decimal('unit_price', 8, 2)->comment('單價');
            $table->decimal('total_amount', 10, 2)->comment('項目總價');
            self::getDatabaseColumnsEditor($table);
            $table->timestamps();
        });

        DB::statement("ALTER TABLE `$table` COMMENT = '訂單物品表'");

    }

    public function down(): void
    {
        Schema::dropIfExists('order_items');
    }
}
