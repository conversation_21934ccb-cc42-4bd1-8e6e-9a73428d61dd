<?php

use Stephenchenorg\BaseFilamentPlugin\Traits\CCTraitDatabaseContent;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\Database\CCTraitDatabaseAdmin;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\Database\CCTraitDatabaseSort;

return new class extends Migration {

    use CCTraitDatabaseContent;
    use CCTraitDatabaseAdmin;
    use CCTraitDatabaseSort;

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $table = 'banners';

        if (!config('cs.banner_visible')) {
            Log::info("Skipping creation of $table table as CS_BANNER_VISIBLE is set to false");
            return;
        }

        Schema::create($table, function (Blueprint $table) {
            $table->smallInteger('id')->unsigned()->autoIncrement();
            $this->addCtaFields($table);
            $this->addTitleField($table);
            $this->addStatusField($table);
            self::getDatabaseColumnsSort($table);
            $this->addImageFields($table);
            self::getDatabaseColumnsEditor($table);
            $table->timestamps();
        });
    }


    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('banners');
    }
};
