<?php

use Stephenchenorg\BaseFilamentPlugin\Traits\CCTraitDatabaseContent;


use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\Database\CCTraitDatabaseAdmin;

return new class extends Migration
{

    use CCTraitDatabaseContent;
    use CCTraitDatabaseAdmin;


    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('tags')) {
            return;
        }

        Schema::create('tags', function (Blueprint $table)
        {
            $table->smallInteger('id')->unsigned()->autoIncrement();
            $table->string('type',20)->comment('標籤分類');
            $this->addKeyField($table);
            $this->addSlugField($table);
            $table->unsignedInteger('article_count')->comment('文章總數')->default(0);
            $table->unsignedInteger('product_count')->comment('產品總數')->default(0);
            self::getDatabaseColumnsEditor($table);
            $table->timestamps();
        });

        DB::statement("ALTER TABLE `tags` COMMENT = '標籤表'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tags');
    }
};
