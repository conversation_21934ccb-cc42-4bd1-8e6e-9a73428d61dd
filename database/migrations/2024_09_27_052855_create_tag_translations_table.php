<?php

use Stephenchenorg\BaseFilamentPlugin\Traits\CCTraitDatabaseContent;

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\Database\CCTraitDatabaseAdmin;

return new class extends Migration
{

    use CCTraitDatabaseContent;
    use CCTraitDatabaseAdmin;

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $table = 'tag_translations';

        if (Schema::hasTable($table)) {
            return;
        }

        Schema::create($table, function (Blueprint $table)
        {
            $table->smallInteger('id')->unsigned()->autoIncrement();
            $table->smallInteger('tag_id')->unsigned()->nullable();
            $table->foreign('tag_id')->references('id')->on('tags')->onDelete('CASCADE');
            $this->addTitleField($table);
            $this->addLanguageField($table);
            self::getDatabaseColumnsEditor($table);
            $table->timestamps();
        });
        DB::statement("ALTER TABLE $table COMMENT = '標籤翻譯內容'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tag_translations');
    }
};
