<?php

use Stephenchenorg\BaseFilamentPlugin\Traits\CCTraitDatabaseContent;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\Database\CCTraitDatabaseAdmin;

return new class extends Migration {

    use CCTraitDatabaseContent;

    use CCTraitDatabaseAdmin;

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $table = 'brand_translations';

        if (!config('cs.brand_visible')) {
            Log::info("Skipping creation of $table table as CS_BRAND_VISIBLE is set to false");
            return;
        }

        Schema::create($table, function (Blueprint $table) {
            $table->smallInteger('id')->unsigned()->autoIncrement();
            $table->smallInteger('brand_id')->unsigned();
            $table->foreign('brand_id')->references('id')->on('brands')->onDelete('cascade');
            $this->addTitleField($table);
            $this->addDescriptionField($table);
            $this->addLanguageField($table);
            self::getDatabaseColumnsEditor($table);
            $table->timestamps();
        });
        DB::statement("ALTER TABLE `$table` COMMENT = '合作品牌翻譯表'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('brand_translations');
    }
};
