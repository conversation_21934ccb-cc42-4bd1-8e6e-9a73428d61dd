<?php

use Stephenchenorg\BaseFilamentPlugin\Traits\CCTraitColumn;
use Stephenchenorg\BaseFilamentPlugin\Traits\CCTraitDatabaseContent;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\Database\CCTraitDatabaseAdmin;

return new class extends Migration {

    use CCTraitDatabaseContent;

    use CCTraitDatabaseAdmin;
    use  CCTraitColumn;

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_specification_translations', function (Blueprint $table) {
            $table->mediumInteger('id')->unsigned()->autoIncrement();
            $table->mediumInteger('product_specification_id')->unsigned();
            $table->foreign('product_specification_id', 'ps_trans_spec_id_foreign')
                ->references('id')
                ->on('product_specifications')
                ->onDelete('cascade');

            $this->addTitleField($table);
            $this->addContentField($table);
            $this->addLanguageField($table);

            self::getDatabaseColumnsEditor($table);
            $table->timestamps();
        });

        DB::statement("ALTER TABLE `product_specification_translations` COMMENT = '產品規格翻譯表'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_specification_translations');
    }
};
