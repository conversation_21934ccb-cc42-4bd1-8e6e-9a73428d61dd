<?php

use Illuminate\Support\Facades\Log;
use Stephenchenorg\BaseFilamentPlugin\Traits\CCTraitDatabaseContent;

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\Database\CCTraitDatabaseAdmin;

return new class extends Migration
{

    use CCTraitDatabaseContent;
    use CCTraitDatabaseAdmin;

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!config('cs.contact_visible')) {
            Log::info("Skipping creation of contact_files table as CS_CONTACT_VISIBLE is set to false");
            return;
        }

        Schema::create('contact_files', function (Blueprint $table)
        {
            $table->mediumInteger('id')->unsigned()->autoIncrement();
            $table->mediumInteger('contact_id')->unsigned();
            $table->foreign('contact_id')->references('id')->on('contacts')->onDelete('cascade');
            $table->string('file');
            self::getDatabaseColumnsEditor($table);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('contact_files');
    }
};
