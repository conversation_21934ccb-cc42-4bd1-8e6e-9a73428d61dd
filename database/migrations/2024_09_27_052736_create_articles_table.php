<?php

use Stephenchenorg\BaseFilamentPlugin\Traits\CCTraitDatabaseContent;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Log;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\Database\CCTraitDatabaseAdmin;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\Database\CCTraitDatabasePeriod;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\Database\CCTraitDatabaseSort;

return new class extends Migration
{

    use CCTraitDatabaseContent;
    use CCTraitDatabaseAdmin;
    use CCTraitDatabaseSort;
    use CCTraitDatabasePeriod;

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $table = 'articles';

        if (!config('cs.article_visible')) {
            Log::info("Skipping creation of $table table as CS_ARTICLE_VISIBLE is set to false");
            return;
        }

        Schema::create($table, function (Blueprint $table)
        {
            $table->mediumInteger('id')->unsigned()->autoIncrement();
            $table->mediumInteger('article_category_id')->unsigned()->nullable();
            $table->foreign('article_category_id')->references('id')->on('article_categories')->onDelete('cascade');
            $this->addKeyField($table);
            $this->addStatusField($table);
            self::getDatabaseColumnsSort($table);
            $this->addSlugField($table);
            $this->addIsHottestField($table);
            $table->unsignedTinyInteger('is_newest')->default(false); // 是否最新
            $this->addBackgroundField($table);
            $this->addCoverField($table);
            self::getDatabaseColumnsPeriod($table);
            self::getDatabaseColumnsEditor($table);
            $table->timestamps();


        });
        DB::statement("ALTER TABLE $table COMMENT = '文章'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('articles');
    }
};
