<?php

use Stephenchenorg\BaseFilamentPlugin\Traits\CCTraitColumn;
use Stephenchenorg\BaseFilamentPlugin\Traits\CCTraitDatabaseContent;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\Database\CCTraitDatabaseAdmin;

return new class extends Migration {

    use CCTraitDatabaseContent;

    use CCTraitDatabaseAdmin;
    use  CCTraitColumn;

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_detail_translations', function (Blueprint $table) {
            $table->mediumInteger('id')->unsigned()->autoIncrement();
            $table->mediumInteger('product_detail_id')->unsigned();
            $table->foreign('product_detail_id')->references('id')->on('product_details')->onDelete('cascade');
            $this->addContentField($table);
            $this->addLanguageField($table);
            self::getDatabaseColumnsEditor($table);
            $table->timestamps();
        });

        DB::statement("ALTER TABLE `product_detail_translations` COMMENT = '產品規格組別表'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_detail_translations');
    }
};
