<?php

use Stephenchenorg\BaseFilamentPlugin\Traits\CCTraitDatabaseContent;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\Database\CCTraitDatabaseAdmin;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\Database\CCTraitDatabaseSort;

return new class extends Migration {

    use CCTraitDatabaseContent;
    use CCTraitDatabaseAdmin;
    use CCTraitDatabaseSort;

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_specifications', function (Blueprint $table) {
            $table->mediumInteger('id')->unsigned()->autoIncrement();
            $table->string('combination_key',40)->nullable();  // attribute_item的id組合
            $table->decimal('listing_price', 8, 2); // 列表價
            $table->decimal('selling_price', 8, 2); // 銷售價
            $table->mediumInteger('inventory')->default(0); // 庫存數量
            $table->string('type', 20)->nullable(); // 規格類型
            $table->string('sku', 64); // SKU (Stock Keeping Unit)
            $table->string('ean',16)->nullable();
            $table->mediumInteger('product_id')->unsigned();
            $table->foreign('product_id')->references('id')->on('products')->onDelete('cascade');
            $this->addStatusField($table);
            self::getDatabaseColumnsSort($table);
            self::getDatabaseColumnsEditor($table);

            $table->softDeletes();
            $table->timestamps();
        });

        DB::statement("ALTER TABLE `product_specifications` COMMENT = '產品規格表'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_specifications');
    }
};
