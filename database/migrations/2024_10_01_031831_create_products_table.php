<?php

use Stephenchenorg\BaseFilamentPlugin\Traits\CCTraitDatabaseContent;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\Database\CCTraitDatabaseAdmin;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\Database\CCTraitDatabasePeriod;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\Database\CCTraitDatabaseSort;

return new class extends Migration {

    use CCTraitDatabaseContent;
    use CCTraitDatabaseAdmin;
    use CCTraitDatabaseSort;
    use CCTraitDatabasePeriod;

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('products', function (Blueprint $table) {

            $table->mediumInteger('id')->unsigned()->autoIncrement();
            $table->string('part_number', 100); // 部件號碼
            $table->unsignedTinyInteger('is_hottest')->default(false); // 是否最熱門
            $table->unsignedTinyInteger('is_newest')->default(false); // 是否最新
            $this->addStatusField($table);
            $table->unsignedTinyInteger('is_all_ancestors_enabled')->default(1)->comment('是否所有祖先狀態都是開啟，0 是關閉，1 是開啟');
            self::getDatabaseColumnsSort($table);
            $table->string('type', 20)->default('product'); // 產品類型
            self::getDatabaseColumnsPeriod($table);

            $table->smallInteger('product_category_id')->unsigned();
            $table->foreign('product_category_id')
                ->references('id')
                ->on('product_categories')
                ->onDelete('cascade');

            self::getDatabaseColumnsEditor($table);

            $table->softDeletes();
            $table->timestamps();
        });

        DB::statement("ALTER TABLE `products` COMMENT = '產品表'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('products');
    }
};
