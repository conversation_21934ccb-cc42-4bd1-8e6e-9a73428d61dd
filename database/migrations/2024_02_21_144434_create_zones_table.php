<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\Database\CCTraitDatabaseIsEnabled;

return new class extends Migration
{
    use CCTraitDatabaseIsEnabled;

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('zones', function (Blueprint $table) {
            $table->smallIncrements('id');
            $table->boolean('is_enabled')->default(true)->comment('是否啟用');
            $table->string('postal_code', 10)->comment('郵遞區號');
            $table->string('name', 10)->comment('區域名稱');
            $table->string('key', 10);
            $table->unsignedTinyInteger('city_id')->comment('縣市id');
            $table->timestamps();

            $table
                ->foreign('city_id')
                ->references('id')
                ->on('cities')
                ->onDelete('cascade')
                ->onUpdate('cascade');

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('zones');
    }
};
