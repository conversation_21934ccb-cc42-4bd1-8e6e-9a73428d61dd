<?php

namespace Stephenchenorg\BaseFilamentPlugin\Database\Seeders;

use Stephenchenorg\BaseFilamentPlugin\Models\Brand;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Log;
use Stephenchenorg\CsCoreFilamentPlugin\CCUtility;

class BrandSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        if (!CCUtility::isProduction() && config('cs.brand_visible')) {
            Brand::factory()->withTranslations()->create();
        } elseif (!CCUtility::isProduction() && !config('cs.brand_visible')) {
            Log::info('Skipping brand seeding as CS_BRAND_VISIBLE is set to false');
        }
    }
}
