<?php

namespace Stephen<PERSON>org\BaseFilamentPlugin\Database\Seeders;

use Stephenchenorg\BaseFilamentPlugin\Models\Admin;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;

class AdminSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->seedingSystemAdmin();
        $this->seedingAdmin();
    }

    /**
     * @return void
     */
    public function seedingSystemAdmin(): void
    {
        $role = Role::query()->firstOrCreate(['name' => 'Super Admin']);

        $email = config('cs.admin.app_hidden_super_admin_account');
        $email = blank($email) ? '<EMAIL>' : $email;

        $password = config('cs.admin.app_hidden_super_admin_password');
        $password = blank($password) ? 'admin' : $password;

        $admin = Admin::factory()->create([
            'name'     => '系統最高隱藏管理員',
            'email'    => $email,
            'password' => $password,
        ]);

        $admin->assignRole($role);
    }

    public function seedingAdmin(): void
    {
        $role = Role::query()->firstOrCreate(['name' => 'admin']);

        $email = config('cs.admin.app_super_admin_account');
        $email = blank($email) ? '<EMAIL>' : $email;

        $password = config('cs.admin.app_super_admin_password');
        $password = blank($password) ? 'admin' : $password;

        $user = Admin::factory()->create([
            'name'     => '管理員',
            'email'    => $email,
            'password' => $password,
        ]);

        $user->assignRole($role);
    }
}
