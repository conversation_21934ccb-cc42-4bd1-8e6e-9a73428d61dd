<?php

namespace <PERSON><PERSON>org\BaseFilamentPlugin\Database\Seeders;

use Illuminate\Database\Seeder;
use Stephen<PERSON>org\BaseFilamentPlugin\Enum\EnumShippingMethod;
use Stephenchenorg\BaseFilamentPlugin\Models\ShippingMethod;

class ShippingMethodSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        foreach (EnumShippingMethod::cases() as $enumCase) {

            // 檢查運送方式是否已存在
            $shippingMethod = ShippingMethod::query()->where('type', $enumCase->value)->exists();

            if (!$shippingMethod) {
                // 如果不存在，建立新的運送方式
                $shippingMethod = ShippingMethod::query()->create([
                    'type' => $enumCase->value,
                    'default_amount' => $this->getDefaultAmount($enumCase),
                ]);
            }

        }
    }

    /**
     * 根據運送方式類型取得預設金額
     */
    private function getDefaultAmount(EnumShippingMethod $enumCase): int
    {
        return match ($enumCase) {
            EnumShippingMethod::NONE => 0,
            EnumShippingMethod::DELIVERY => 100,      // 宅配 100 元
            EnumShippingMethod::OK_MART => 60,        // OK 超商 60 元
            EnumShippingMethod::SEVEN_ELEVEN => 60,   // 7-11 超商 60 元
            EnumShippingMethod::FAMILY_MART => 60,    // 全家超商 60 元
            EnumShippingMethod::HI_LIFE => 60,        // 萊爾富超商 60 元
        };
    }
}
