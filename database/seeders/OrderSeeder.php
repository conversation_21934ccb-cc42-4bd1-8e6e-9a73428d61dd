<?php

namespace Stephenchenorg\BaseFilamentPlugin\Database\Seeders;

use Stephenchenorg\BaseFilamentPlugin\Models\Order;
use Illuminate\Database\Seeder;
use Stephenchenorg\CsCoreFilamentPlugin\CCUtility;

class OrderSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        if (!CCUtility::isProduction()) {
            Order::factory()
                ->count(3)
                ->withItems(3)
                ->create();
        }
    }
}
