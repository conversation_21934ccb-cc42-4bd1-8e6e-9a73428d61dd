<?php

namespace Stephen<PERSON>org\BaseFilamentPlugin\Database\Seeders;

use Stephenchenorg\BaseFilamentPlugin\Service\ServiceStorage;
use Illuminate\Database\Seeder;
use Stephenchenorg\CsCoreFilamentPlugin\CCUtility;
use function Safe\eio_event_loop;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;

class BaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        if (CCUtility::isLocal()) {
            ServiceStorage::buildDefaultImage();
        }

        // 從配置中讀取要執行的 seeders
        $configSeeders = config('cs.seeders', [
            'Permission' => \Stephenchenorg\BaseFilamentPlugin\Database\Seeders\PermissionSeeder::class,
            'Role' => \Stephenchenorg\BaseFilamentPlugin\Database\Seeders\RoleSeeder::class,
            'Cities' => \Stephenchenorg\BaseFilamentPlugin\Database\Seeders\CitiesSeeder::class,
            'Zone' => \Stephenchenorg\BaseFilamentPlugin\Database\Seeders\ZoneSeeder::class,
            'Admin' => \Stephenchenorg\BaseFilamentPlugin\Database\Seeders\AdminSeeder::class,
            'Customer' => \Stephenchenorg\BaseFilamentPlugin\Database\Seeders\CustomerSeeder::class,
            'Client' => \Stephenchenorg\BaseFilamentPlugin\Database\Seeders\ClientSeeder::class,
            'Tag' => \Stephenchenorg\BaseFilamentPlugin\Database\Seeders\TagSeeder::class,
            'Article' => \Stephenchenorg\BaseFilamentPlugin\Database\Seeders\ArticleSeeder::class,
            'Banner' => \Stephenchenorg\BaseFilamentPlugin\Database\Seeders\BannerSeeder::class,
            'Company' => \Stephenchenorg\BaseFilamentPlugin\Database\Seeders\CompanySeeder::class,
            'Contact' => \Stephenchenorg\BaseFilamentPlugin\Database\Seeders\ContactSeeder::class,
            'Faq' => \Stephenchenorg\BaseFilamentPlugin\Database\Seeders\FaqSeeder::class,
            'Page' => \Stephenchenorg\BaseFilamentPlugin\Database\Seeders\PageSeeder::class,
            'Product' => \Stephenchenorg\BaseFilamentPlugin\Database\Seeders\ProductSeeder::class,
            'System' => \Stephenchenorg\BaseFilamentPlugin\Database\Seeders\SystemSeeder::class,
        ]);

        // 執行每個 seeder，只取值不取鍵
        foreach ($configSeeders as $key => $seeder) {
            $this->call($seeder);
        }
    }


}
