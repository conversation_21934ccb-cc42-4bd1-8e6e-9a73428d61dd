<?php

namespace Stephenchenorg\BaseFilamentPlugin\Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class RoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $admin = Role::create(['name' => 'admin', 'display_name' => '管理員']);
        $superAdmin = Role::create(['name' => 'Super Admin', 'display_name' => '系統最高隱藏管理員']);

        $superAdmin->permissions()->sync(Permission::all());
        $admin->permissions()->sync(Permission::all());
    }
}
