<?php

namespace Stephenchenorg\BaseFilamentPlugin\Database\Seeders;

use Stephenchenorg\BaseFilamentPlugin\Models\RewardCategory;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceLanguage;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Log;
use Stephenchenorg\CsCoreFilamentPlugin\CCUtility;

class RewardCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        if (!CCUtility::isProduction()) {
            $this->createRewardCategories();
        } else {
            Log::info('Skipping reward category seeding in production environment');
        }
    }

    /**
     * 創建獎勵類別
     */
    private function createRewardCategories(): void
    {
        $categories = [
            [
                'key' => 'order_completed',
                'translations' => [
                    'zh_TW' => '訂單完成',
                    'en' => 'Order Completed',
                ]
            ],
            [
                'key' => 'order_discount',
                'translations' => [
                    'zh_TW' => '訂單抵免',
                    'en' => 'Order Discount',
                ]
            ],
        ];

        foreach ($categories as $index => $categoryData) {
            // 創建獎勵類別
            $rewardCategory = RewardCategory::create([
                'key' => $categoryData['key'],
                'sort' => $index + 1,
            ]);

            // 創建翻譯
            foreach ($categoryData['translations'] as $lang => $title) {
                $rewardCategory->translations()->create([
                    'reward_category_id' => $rewardCategory->id,
                    'lang' => $lang,
                    'title' => $title,
                ]);
            }

            Log::info("Created reward category: {$categoryData['translations']['zh_TW']} with ID: {$rewardCategory->id}");
        }
    }
}
