<?php

namespace <PERSON>org\BaseFilamentPlugin\Database\Seeders;

use Stephenchenorg\BaseFilamentPlugin\Models\Customer;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Log;
use Stephenchenorg\CsCoreFilamentPlugin\CCUtility;

class CustomerSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        if (!config('cs.customer_visible')) {
            Log::info("Skipping customer seeding as CS_CUSTOMER_VISIBLE is set to false");
            return;
        }

        $this->seedingDefaultCustomer();

        if (!CCUtility::isProduction()) {
            $this->seedingTestCustomers();
        }
    }

    /**
     * Seed the default customer account
     *
     * @return void
     */
    public function seedingDefaultCustomer(): void
    {
        $account = config('cs.customer.app_super_customer_account');
        $password = config('cs.customer.app_super_customer_password');

        Customer::factory()->create([
            'email' => $account,
            'password' => $password,
        ]);
    }

    /**
     * Seed test customer accounts
     *
     * @return void
     */
    public function seedingTestCustomers(): void
    {
        // Create 5 random customers
        Customer::factory()
            ->count(5)
            ->create();

        // Create 2 customers with shipping addresses
        Customer::factory()
            ->withShippingAddresses(2)
            ->count(2)
            ->create();

        // Create 1 customer with subscription
        Customer::factory()
            ->withSubscription()
            ->create();
    }
}
