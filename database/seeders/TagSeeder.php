<?php

namespace <PERSON>g\BaseFilamentPlugin\Database\Seeders;

use Stephenchenorg\BaseFilamentPlugin\Enum\EnumTagType;
use Stephenchenorg\BaseFilamentPlugin\Models\Tag;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Log;
use Stephenchenorg\CsCoreFilamentPlugin\CCUtility;

class TagSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        if (!CCUtility::isProduction()) {

            if (config('cs.share_tag')) {
                Tag::factory()->withTranslations()->count(2)->create([
                    'type' => EnumTagType::SHARED,
                ]);
            }

            if (config('cs.article_visible')) {
                Tag::factory()->withTranslations()->count(2)->create([
                    'type' => EnumTagType::ARTICLE,
                ]);
            }

            if (config('cs.product_visible')) {
                Tag::factory()->withTranslations()->count(2)->create([
                    'type' => EnumTagType::PRODUCT,
                ]);
            }

        }
    }
}
