<?php

namespace Stephenchenorg\BaseFilamentPlugin\Database\Seeders;

use Stephenchenorg\BaseFilamentPlugin\Models\CompanySetting;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceLanguage;
use Illuminate\Database\Seeder;
use Stephenchenorg\CsCoreFilamentPlugin\CCUtility;

class CompanySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        if (!CCUtility::isProduction()) {

            foreach (ServiceLanguage::getLanguages() as $lang) {
                CompanySetting::factory()->create([
                    'lang' => $lang,
                ]);
            }

        }
    }
}
