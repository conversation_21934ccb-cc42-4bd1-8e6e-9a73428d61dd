<?php

namespace Stephenchenorg\BaseFilamentPlugin\Database\Seeders;

use Stephenchenorg\BaseFilamentPlugin\Service\ServiceConfig;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Gate;
use Spatie\Permission\Models\Permission;
use Stephenchenorg\CsCoreFilamentPlugin\Services\CCServicePermissionSeeder;

class PermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $permissions = CCServicePermissionSeeder::getPermissions();

        Permission::query()->insert($permissions);
    }
}
