<?php

namespace Stephenchenorg\BaseFilamentPlugin\Database\Seeders;

use Stephenchenorg\BaseFilamentPlugin\Models\Page;
use Illuminate\Database\Seeder;
use Stephenchenorg\CsCoreFilamentPlugin\CCUtility;

class PageSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        if (!CCUtility::isProduction()) {
            Page::factory()->withTranslations()->withFields(3)->create();
        }
//        else
//        {
//            Page::factory()->createProductionData();
//        }
    }
}
