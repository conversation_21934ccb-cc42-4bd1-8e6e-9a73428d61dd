<?php

namespace Stephenchenorg\BaseFilamentPlugin\Database\Seeders;

use Illuminate\Database\Seeder;
use Stephenchenorg\BaseFilamentPlugin\Models\Cities;

final class CitiesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $items = $this->items();
        foreach ($items as $value) {
            Cities::query()->updateOrCreate(['key' => $value['key']], $value);
        }
    }

    /**
     * @return array[]
     */
    public function items(): array
    {
        return [
            ['id' =>  1, 'name' => '臺北市', 'key' => 'TPE'],
            ['id' =>  2, 'name' => '基隆市', 'key' => 'KEE'],
            ['id' =>  3, 'name' => '新北市', 'key' => 'NTPC'],
            ['id' =>  4, 'name' => '宜蘭縣', 'key' => 'ILA'],
            ['id' =>  5, 'name' => '新竹市', 'key' => 'HSZ'],
            ['id' =>  6, 'name' => '新竹縣', 'key' => 'HSC'],
            ['id' =>  7, 'name' => '桃園市', 'key' => 'TYC'],
            ['id' =>  8, 'name' => '苗栗縣', 'key' => 'MIA'],
            ['id' =>  9, 'name' => '臺中市', 'key' => 'TXG'],
            ['id' => 10, 'name' => '彰化縣', 'key' => 'CHA'],
            ['id' => 11, 'name' => '南投縣', 'key' => 'NAN'],
            ['id' => 12, 'name' => '嘉義市', 'key' => 'CYI'],
            ['id' => 13, 'name' => '嘉義縣', 'key' => 'CYC'],
            ['id' => 14, 'name' => '雲林縣', 'key' => 'YUN'],
            ['id' => 15, 'name' => '臺南市', 'key' => 'TNN'],
            ['id' => 16, 'name' => '高雄市', 'key' => 'KHH'],
            ['id' => 17, 'name' => '南海諸島', 'key' => ''],   // 沒對應代碼可先留 null
            ['id' => 18, 'name' => '澎湖縣', 'key' => 'PEN'],
            ['id' => 19, 'name' => '屏東縣', 'key' => 'PIF'],
            ['id' => 20, 'name' => '臺東縣', 'key' => 'TTT'],
            ['id' => 21, 'name' => '花蓮縣', 'key' => 'HUA'],
            ['id' => 22, 'name' => '金門縣', 'key' => 'KIN'],
            ['id' => 23, 'name' => '連江縣', 'key' => 'LIE'],
        ];
    }
}
