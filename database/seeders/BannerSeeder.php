<?php

namespace Stephenchenorg\BaseFilamentPlugin\Database\Seeders;

use Stephenchenorg\BaseFilamentPlugin\Models\Banner;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Log;
use Stephenchenorg\CsCoreFilamentPlugin\CCUtility;

class BannerSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        if (!CCUtility::isProduction() && config('cs.banner_visible')) {
            Banner::factory()->create();
        } elseif (!CCUtility::isProduction() && !config('cs.banner_visible')) {
            Log::info('Skipping banner seeding as CS_BANNER_VISIBLE is set to false');
        }
    }
}
