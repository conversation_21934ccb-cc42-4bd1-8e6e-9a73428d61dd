<?php

namespace Stephenchenorg\BaseFilamentPlugin\Database\Seeders;

use Stephenchenorg\BaseFilamentPlugin\Models\Contact;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Log;
use Stephenchenorg\CsCoreFilamentPlugin\CCUtility;

class ContactSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        if (!CCUtility::isProduction() && config('cs.contact_visible')) {
            Contact::factory()->count(5)->create();
        } elseif (!CCUtility::isProduction() && !config('cs.contact_visible')) {
            Log::info('Skipping contact seeding as CS_CONTACT_VISIBLE is set to false');
        }
    }
}
