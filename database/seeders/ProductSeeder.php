<?php

namespace Stephenchenorg\BaseFilamentPlugin\Database\Seeders;

use Stephenchenorg\BaseFilamentPlugin\Enum\EnumProductType;
use Stephenchenorg\BaseFilamentPlugin\Models\Product;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductCategory;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceLanguage;
use Illuminate\Database\Seeder;
use Stephenchenorg\CsCoreFilamentPlugin\CCUtility;

class ProductSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {


        if (!CCUtility::isProduction() && !config('cs.product_has_plugin_seeder')) {

            $this->createProductCategories();

            Product::factory()
                ->withTranslations()
                ->withTags()
                ->withImages()
                ->withAttributesAndSpecifications()
                ->count(4)->create();
        }

    }

    public function createProductCategories(): void
    {
        foreach (EnumProductType::getOptions() as $option) {
            $root = $this->createRoot($option->getLabel(), $option->value);

            if (app()->environment() === 'local') {
                $n = 2;
                for ($i = 0; $i < $n; $i++) {
                    $rootCategory = ProductCategory::factory()->withTranslations()->withParent($root)->create();
                    for ($j = 0; $j < $n; $j++) {
                        $secondCategory = ProductCategory::factory()->withTranslations()->withParent($rootCategory)->create();
                        for ($z = 0; $z < $n; $z++) {
                            $thirdCategory = ProductCategory::factory()->withTranslations()->withParent($secondCategory)->create();
                        }
                    }
                }
            }
        }
    }

    public function createRoot($title, $key): ProductCategory
    {
        $root = ProductCategory::query()->create([
            'type' => $key,
            'is_hottest' => false,
            'status' => true,
            'sort' => 0,
            'count_total' => 0,
        ]);

        foreach (ServiceLanguage::getLanguages() as $lang) {
            $root->translations()->create([
                'title' => $title,
                'lang' => $lang,
                'product_category_id' => $root->id,
            ]);
        }

        return $root;
    }
}
