<?php

namespace <PERSON><PERSON>org\BaseFilamentPlugin\Database\Seeders;

use Stephenchenorg\BaseFilamentPlugin\Models\Client;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Log;
use Stephenchenorg\CsCoreFilamentPlugin\CCUtility;

class ClientSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        if (!config('cs.client_visible')) {
            Log::info("Skipping client seeding as CS_CLIENT_VISIBLE is set to false");
            return;
        }

        $this->seedingDefaultClient();

        if (!CCUtility::isProduction()) {
            $this->seedingTestClients();
        }
    }

    /**
     * Seed the default client account
     *
     * @return void
     */
    public function seedingDefaultClient(): void
    {
        $email = config('cs.client.app_super_client_account');
        $password = config('cs.client.app_super_client_password');

        Client::factory()->create([
            'email' => $email,
            'password' => $password,
        ]);
    }

    /**
     * Seed test client accounts
     *
     * @return void
     */
    public function seedingTestClients(): void
    {
        // Create 3 random clients
        Client::factory()
            ->count(3)
            ->create();

        // Create 2 clients with shipping addresses
        Client::factory()
            ->withShippingAddresses(2)
            ->count(2)
            ->create();

        // Create 1 client with subscription
        Client::factory()
            ->withSubscription()
            ->create();
    }
}
