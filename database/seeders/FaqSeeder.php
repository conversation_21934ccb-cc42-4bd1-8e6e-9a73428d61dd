<?php

namespace Stephenchenorg\BaseFilamentPlugin\Database\Seeders;

use Stephenchenorg\BaseFilamentPlugin\Models\Faq;
use Stephenchenorg\BaseFilamentPlugin\Models\FaqCategory;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Log;
use Stephenchenorg\CsCoreFilamentPlugin\CCUtility;

class FaqSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        if (!CCUtility::isProduction() && config('cs.faq_visible')) {
            FaqCategory::factory()->withTranslations()->count(5)->create();
            Faq::factory()->withTranslations()->count(5)->create();
        } elseif (!CCUtility::isProduction() && !config('cs.faq_visible')) {
            Log::info('Skipping FAQ seeding as CS_FAQ_VISIBLE is set to false');
        }
    }
}
