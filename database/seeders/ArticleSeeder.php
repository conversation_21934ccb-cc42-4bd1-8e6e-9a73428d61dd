<?php

namespace Stephenchenorg\BaseFilamentPlugin\Database\Seeders;

use Stephenchenorg\BaseFilamentPlugin\Models\Article;
use Stephenchenorg\BaseFilamentPlugin\Models\ArticleCategory;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Log;
use Stephenchenorg\CsCoreFilamentPlugin\CCUtility;

class ArticleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        if (!CCUtility::isProduction() && config('cs.article_visible')) {
            ArticleCategory::factory()->withTranslations()->count(10)->create();
            Article::factory()
                ->withTags()
                ->withTranslations()->count(5)->create();
        } elseif (!CCUtility::isProduction() && !config('cs.article_visible')) {
            Log::info('Skipping article seeding as CS_ARTICLE_VISIBLE is set to false');
        }

    }
}
