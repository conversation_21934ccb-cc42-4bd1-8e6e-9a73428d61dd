<?php

namespace Stephen<PERSON>org\BaseFilamentPlugin;

use Filament\Contracts\Plugin;
use Filament\Panel;
use Stephenchenorg\BaseFilamentPlugin\Filament\Pages\CompanySettingPage;
use Stephenchenorg\BaseFilamentPlugin\Filament\Pages\MyPage;
use Stephenchenorg\BaseFilamentPlugin\Filament\Pages\ShippingMethodSettingPage;
use Stephenchenorg\BaseFilamentPlugin\Filament\Pages\SystemSettingPage;
use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ActivityLogResource;
use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\AdminResource;
use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ArticleCategoryResource;
use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ArticleResource;
use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\BannerResource;
use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\BrandResource;
use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ClientResource;
use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ContactResource;
use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\CustomerResource;
use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\FaqCategoryResource;
use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\FaqResource;
use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\PageResource;
use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\PermissionResource;
use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ProductCategoryResource;
use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ProductResource;
use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\RoleResource;
use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\TagResource;
use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\OrderResource;
use Stephenchenorg\BaseFilamentPlugin\Filament\Widgets\ArticleWidget;
use Stephenchenorg\BaseFilamentPlugin\Filament\Widgets\ContactWidget;
use Stephenchenorg\BaseFilamentPlugin\Filament\Widgets\ProductWidget;


class BaseFilamentPluginPlugin implements Plugin
{
    public function getId(): string
    {
        return 'base-filament-plugin';
    }

    public function register(Panel $panel): void
    {
        // 從配置中讀取 resources、pages 和 widgets，只取值不取鍵
        $configResources = config('cs.resources', []);
        $resources = array_values($configResources);

        $configPages = config('cs.pages');
        $pages = array_values($configPages);

        $configWidgets = config('cs.widgets');
        $widgets = array_values($configWidgets);

        $panel
            // Resources --------------------------------------------------------------
            ->resources($resources)
            ->pages($pages)
            ->widgets($widgets);
    }

    public function boot(Panel $panel): void
    {
    }

    public static function make(): static
    {
        return app(static::class);
    }

    public static function get(): static
    {
        /** @var static $plugin */
        $plugin = filament(app(static::class)->getId());

        return $plugin;
    }
}
