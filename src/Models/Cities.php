<?php

namespace Stephenchenorg\BaseFilamentPlugin\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

final class Cities extends Model
{
    use HasFactory;

    /**
     * @var array
     */
    protected $guarded = [];

    /**
     * @return HasMany
     */
    public function zones(): HasMany
    {
        return $this->hasMany(Zone::class, 'city_id', 'id');
    }
}
