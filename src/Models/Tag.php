<?php

namespace Stephen<PERSON>org\BaseFilamentPlugin\Models;

use <PERSON><PERSON>org\BaseFilamentPlugin\Enum\EnumTagType;
use Stephen<PERSON>org\BaseFilamentPlugin\Events\TagCreated;
use Stephenchenorg\BaseFilamentPlugin\Events\TagDeleted;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceLanguage;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceLogActivity;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Stephenchenorg\BaseFilamentPlugin\Database\Factories\TagFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\Model\CCTraitModelAdmin;


class Tag extends Model
{
    use HasFactory;
    use CCTraitModelAdmin;
    use LogsActivity;

    /**
     * @var array
     */
    protected $casts = [
        'type' => EnumTagType::class,
    ];

    /**
     * @var array
     */
    protected $guarded = [];

    /**
     * @return void
     */
    protected static function booted(): void
    {
        static::created(function ($tag)
        {
            TagCreated::dispatch($tag);
        });

        static::deleted(function ($tag)
        {
            TagDeleted::dispatch($tag);
        });
    }

    public function articles(): MorphToMany
    {
        return $this->morphedByMany(Article::class, 'taggable', 'tag_pivot')->using(TagPivot::class);
    }

    public function products(): MorphToMany
    {
        return $this->morphedByMany(Product::class, 'taggable', 'tag_pivot')->using(TagPivot::class);
    }

    /**
     * Define the relationship with Article Category Lang Relations.
     */
    public function translations(): HasMany
    {
        return $this->hasMany(TagTranslation::class);
    }

    public function getNameAttribute(): string
    {
        return $this->translations()->where('lang', ServiceLanguage::getDefaultLanguage())->first()?->title;
    }

    /**
     * @return LogOptions
     */
    public function getActivitylogOptions(): LogOptions
    {
        return ServiceLogActivity::getDefaultOptions()
            ->useLogName('標籤');
    }

    /**
     * Create a new factory instance for the model.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    protected static function newFactory(): \Stephenchenorg\BaseFilamentPlugin\Database\Factories\TagFactory
    {
        return TagFactory::new();
    }
}
