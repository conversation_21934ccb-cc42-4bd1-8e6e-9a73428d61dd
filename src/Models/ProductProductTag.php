<?php

namespace Stephenchenorg\BaseFilamentPlugin\Models;

use Stephenchenorg\BaseFilamentPlugin\Service\ServiceLogActivity;
use Illuminate\Database\Eloquent\Relations\Pivot;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\Model\CCTraitModelAdmin;

class ProductProductTag extends Pivot
{
    use CCTraitModelAdmin;
    use LogsActivity;

    /**
     * @var string
     */
    protected $table = 'product_product_tag';

    /**
     * @return void
     */
    protected static function booted(): void
    {
        parent::boot();

        static::created(function ($pivot)
        {

        });

        static::deleted(function ($pivot)
        {

        });

    }


    /**
     * @return LogOptions
     */
    public function getActivitylogOptions(): LogOptions
    {
        return ServiceLogActivity::getDefaultOptions()
            ->useLogName('產品與標籤');
    }
}
