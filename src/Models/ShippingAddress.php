<?php

namespace Stephen<PERSON>org\BaseFilamentPlugin\Models;

use Stephenchenorg\BaseFilamentPlugin\Service\ServiceLogActivity;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Stephenchenorg\BaseFilamentPlugin\Database\Factories\ShippingAddressFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\Model\CCTraitModelAdmin;

class ShippingAddress extends Model
{
    use HasFactory;
    use CCTraitModelAdmin;
    use LogsActivity;



    /**
     * @return void
     */
    protected static function boot(): void
    {
        parent::boot();

        static::saving(function ($address)
        {
            if ($address->is_default && $address->addressable_id && $address->addressable_type) {
                self::query()
                    ->where('addressable_id', $address->addressable_id)
                    ->where('addressable_type', $address->addressable_type)
                    ->where('id', '!=', $address->id)
                    ->update(['is_default' => false]);
            }
        });
    }

    /**
     * 地址所有者關聯 - 使用 morphTo 關聯
     */
    public function addressable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Define the relationship.
     */
    public function member(): BelongsTo
    {
        return $this->belongsTo(Member::class);
    }

    public function ad(): HasOne
    {
        return $this->hasOne(CustomAddress::class);
    }

    /**
     * @return LogOptions
     */
    public function getActivitylogOptions(): LogOptions
    {
        return ServiceLogActivity::getDefaultOptions()
            ->useLogName('訂單地址');
    }



    /**
     * Create a new factory instance for the model.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    protected static function newFactory(): \Stephenchenorg\BaseFilamentPlugin\Database\Factories\ShippingAddressFactory
    {
        return ShippingAddressFactory::new();
    }
}
