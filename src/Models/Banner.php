<?php

namespace <PERSON>g\BaseFilamentPlugin\Models;

use <PERSON><PERSON>org\BaseFilamentPlugin\Events\BannerCreated;
use <PERSON><PERSON>org\BaseFilamentPlugin\Events\BannerDeleted;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceLogActivity;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Stephenchenorg\BaseFilamentPlugin\Database\Factories\BannerFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\Model\CCTraitModelAdmin;

class Banner extends Model
{
    use HasFactory;
    use CCTraitModelAdmin;
    use LogsActivity;

    /**
     * @var array
     */
    protected $guarded = [];


    protected static function booted(): void
    {
        static::created(function ($banner)
        {
            BannerCreated::dispatch($banner);
        });

        static::deleted(function ($banner)
        {
            BannerDeleted::dispatch($banner);
        });
    }

    /**
     * @return LogOptions
     */
    public function getActivitylogOptions(): LogOptions
    {
        return ServiceLogActivity::getDefaultOptions()
            ->useLogName('橫幅廣告');
    }


    /**
     * Create a new factory instance for the model.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    protected static function newFactory(): \Stephenchenorg\BaseFilamentPlugin\Database\Factories\BannerFactory
    {
        return BannerFactory::new();
    }
}
