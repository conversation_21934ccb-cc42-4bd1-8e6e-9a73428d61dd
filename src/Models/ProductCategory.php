<?php

namespace Stephenchenorg\BaseFilamentPlugin\Models;

use Illuminate\Database\Eloquent\SoftDeletes;
use Stephenchenorg\BaseFilamentPlugin\Events\ProductCategoryCreated;
use Stephenchenorg\BaseFilamentPlugin\Events\ProductCategoryDeleted;
use Stephenchenorg\BaseFilamentPlugin\Events\ProductCategorySaved;
use Stephenchenorg\BaseFilamentPlugin\Events\ProductCategoryUpdated;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceLanguage;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceLogActivity;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceProductCategory;
use Franzose\ClosureTable\Models\Entity;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Stephenchenorg\BaseFilamentPlugin\Database\Factories\ProductCategoryFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\Model\CCTraitModelAdmin;

class ProductCategory extends Entity
{
    use HasFactory;
    use CCTraitModelAdmin;
    use LogsActivity;
    use softDeletes;

    public $timestamps = true;


    /**
     * ClosureTable model instance.
     *
     * @var ProductCategoryClosure
     */
    protected $closure = 'Stephenchenorg\BaseFilamentPlugin\Models\ProductCategoryClosure';

    /**
     * 可批量賦值的欄位
     * @var array
     */
    protected $fillable = [
        'parent_id',
        'position',
        'order',
        'type',
        'key',
        'status',
        'sort',
        'image',
        'image_mobile',
        'slug',
        'count_total',
        'is_hottest',
        'code',
    ];

    protected static function booted(): void
    {
        static::created(function ($category)
        {
            ProductCategoryCreated::dispatch($category);
        });

        static::deleted(function ($category)
        {
            ProductCategoryDeleted::dispatch($category);
        });

        static::updated(function ($category)
        {
            ProductCategoryUpdated::dispatch($category);
        });

        static::saved(function ($category)
        {
            ProductCategorySaved::dispatch($category);
        });

    }

    /**
     * Define the relationship with Parent.
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(self::class, 'parent_id');
    }

    /**
     * Define the relationship.
     */
    public function products(): HasMany
    {
        return $this->hasMany(Product::class);
    }

    public function getNameAttribute(): string
    {
        if (empty($this->translations()->where('lang', ServiceLanguage::getDefaultLanguage())->first())) {
            return '';
        }
        return $this->translations()->where('lang', ServiceLanguage::getDefaultLanguage())->first()->title;
    }

    /**
     * Define the relationship with.
     */
    public function translations(): HasMany
    {
        return $this->hasMany(ProductCategoryTranslation::class);
    }

    public function parentTranslations(): HasManyThrough
    {
        return $this->hasManyThrough(ProductCategoryTranslation::class, ProductCategory::class,'id','product_category_id','parent_id','id');
    }

    /**
     * @return LogOptions
     */
    public function getActivitylogOptions(): LogOptions
    {
        return ServiceLogActivity::getDefaultOptions()
            ->useLogName('產品分類');

    }


    /**
     * Create a new factory instance for the model.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    protected static function newFactory(): \Stephenchenorg\BaseFilamentPlugin\Database\Factories\ProductCategoryFactory
    {
        return ProductCategoryFactory::new();
    }
}
