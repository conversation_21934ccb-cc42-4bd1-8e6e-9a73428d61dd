<?php

namespace Stephen<PERSON>org\BaseFilamentPlugin\Models;

use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Stephenchenorg\BaseFilamentPlugin\Events\ProductCreated;
use Stephenchenorg\BaseFilamentPlugin\Events\ProductDeleting;
use Stephenchenorg\BaseFilamentPlugin\Events\ProductSaved;
use Stephenchenorg\BaseFilamentPlugin\Events\ProductUpdated;

use Stephenchenorg\BaseFilamentPlugin\Models\Traits\DateTimeTrait;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceLogActivity;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Stephenchenorg\BaseFilamentPlugin\Database\Factories\ProductFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\Model\CCTraitModelAdmin;

class Product extends Model
{
    use HasFactory;
    use CCTraitModelAdmin;
    use LogsActivity;
    use DateTimeTrait;
    use SoftDeletes;


    /**
     * @var array
     */
    protected $guarded = [];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'started_at' => 'date',
        'ended_at' => 'date',
    ];

    /**
     * @return void
     */
    protected static function boot(): void
    {
        parent::boot();


        static::created(function ($product)
        {
            ProductCreated::dispatch($product);
        });

        static::deleting(function ($product)
        {
            ProductDeleting::dispatch($product);
        });

        static::updated(function ($product)
        {
            ProductUpdated::dispatch($product);
        });

        static::saved(function ($product)
        {
            ProductSaved::dispatch($product);
        });

    }

    /**
     * Define the relationship.
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(ProductCategory::class, 'product_category_id');
    }

    /**
     * Define the relationship.
     */
    public function translations(): HasMany
    {
        return $this->hasMany(ProductTranslation::class);
    }

    /**
     * Define the relationship.
     */
    public function specifications(): HasMany
    {
        return $this->hasMany(ProductSpecification::class);
    }

    /**
     * Define the relationship.
     */
    public function tags(): MorphToMany
    {
        return $this->morphToMany(Tag::class, 'taggable', 'tag_pivot')->using(TagPivot::class);
    }

    /**
     * Define the relationship.
     */
    public function images(): HasMany
    {
        return $this->hasMany(ProductImage::class);
    }

    /**
     * Define the relationship.
     */
    public function attributes(): HasMany
    {
        return $this->hasMany(ProductAttribute::class);
    }

    /**
     * Define the relationship.
     */
    public function details(): HasMany
    {
        return $this->hasMany(ProductDetail::class);
    }


    public function shippingMethods():BelongsToMany
    {
        return $this->belongsToMany(ShippingMethod::class, 'product_shipping_method')
            ->using(ProductShippingMethod::class)
            ->withPivot([
                'status',
                'combine_same_product',
                'combine_different_product',
                'amount',
            ]);
    }

    /**
     * @return LogOptions
     */
    public function getActivitylogOptions(): LogOptions
    {
        return ServiceLogActivity::getDefaultOptions()
            ->useLogName('產品');
    }


    /**
     * Create a new factory instance for the model.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    protected static function newFactory(): \Stephenchenorg\BaseFilamentPlugin\Database\Factories\ProductFactory
    {
        return ProductFactory::new();
    }
}
