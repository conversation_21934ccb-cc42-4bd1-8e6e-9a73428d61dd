<?php

namespace Stephen<PERSON>g\BaseFilamentPlugin\Models;

use Stephenchenorg\BaseFilamentPlugin\Service\ServiceLogActivity;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Stephenchenorg\BaseFilamentPlugin\Database\Factories\PageFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\Model\CCTraitModelAdmin;

class Page extends Model
{
    use HasFactory;
    use CCTraitModelAdmin;
    use LogsActivity;

    /**
     * @var array
     */
    protected $guarded = [];


    /**
     * Define the relationship with Fields.
     */
    public function fields(): HasMany
    {
        return $this->hasMany(PageField::class);
    }


    /**
     * Define the relationship with Fields.
     */
    public function translations(): HasMany
    {
        return $this->hasMany(PageTranslation::class);
    }

    /**
     * @return LogOptions
     */
    public function getActivitylogOptions(): LogOptions
    {
        return ServiceLogActivity::getDefaultOptions()
            ->useLogName('靜態頁面');
    }


    /**
     * Create a new factory instance for the model.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    protected static function newFactory(): \Stephenchenorg\BaseFilamentPlugin\Database\Factories\PageFactory
    {
        return PageFactory::new();
    }
}
