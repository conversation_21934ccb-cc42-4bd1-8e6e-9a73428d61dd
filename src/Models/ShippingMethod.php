<?php

namespace Stephen<PERSON>org\BaseFilamentPlugin\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;
use Stephenchenorg\BaseFilamentPlugin\Enum\EnumShippingMethod;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceLogActivity;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\Model\CCTraitModelAdmin;

class ShippingMethod extends Model
{
    use HasFactory;
    use CCTraitModelAdmin;
    use LogsActivity;

    /**
     * @var array
     */
    protected $guarded = [];

    /**
     * @var array
     */
    protected $casts = [
        'type' => EnumShippingMethod::class,
    ];


    public function products():BelongsToMany
    {
        return $this->belongsToMany(Product::class, 'product_shipping_method')
            ->using(ProductShippingMethod::class)
            ->withPivot([
                'status',
                'combine_same_product',
                'combine_different_product',
                'amount',
            ]);
    }

    /**
     * @return LogOptions
     */
    public function getActivitylogOptions(): LogOptions
    {
        return ServiceLogActivity::getDefaultOptions()
            ->useLogName('運送方式');
    }
}
