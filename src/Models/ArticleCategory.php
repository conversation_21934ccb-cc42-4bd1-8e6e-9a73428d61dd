<?php

namespace <PERSON>org\BaseFilamentPlugin\Models;

use <PERSON><PERSON>org\BaseFilamentPlugin\Events\ArticleCategoryCreated;
use Stephenchenorg\BaseFilamentPlugin\Events\ArticleCategoryDeleted;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceLanguage;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceLogActivity;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Stephenchenorg\BaseFilamentPlugin\Database\Factories\ArticleCategoryFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\Model\CCTraitModelAdmin;

class ArticleCategory extends Model
{
    use HasFactory;
    use CCTraitModelAdmin;
    use LogsActivity;



    /**
     * @var array
     */
    protected $guarded = [];

    protected static function booted(): void
    {
        static::created(function ($category)
        {
            ArticleCategoryCreated::dispatch($category);
        });

        static::deleted(function ($category)
        {
            ArticleCategoryDeleted::dispatch($category);
        });
    }

    /**
     * Define the relationship with Articles.
     */
    public function articles(): HasMany
    {
        return $this->hasMany(Article::class);
    }

    public function getNameAttribute(): string
    {
        return $this->translations()->where('lang', ServiceLanguage::getDefaultLanguage())->first()->title;
    }

    /**
     * Define the relationship with Article Category Lang Relations.
     */
    public function translations(): HasMany
    {
        return $this->hasMany(ArticleCategoryTranslation::class);
    }

    /**
     * @return LogOptions
     */
    public function getActivitylogOptions(): LogOptions
    {
        return ServiceLogActivity::getDefaultOptions()
            ->useLogName('文章分類');
    }



    /**
     * Create a new factory instance for the model.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    protected static function newFactory(): \Stephenchenorg\BaseFilamentPlugin\Database\Factories\ArticleCategoryFactory
    {
        return ArticleCategoryFactory::new();
    }
}
