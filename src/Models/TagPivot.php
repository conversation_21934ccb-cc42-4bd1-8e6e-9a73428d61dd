<?php

namespace <PERSON><PERSON>org\BaseFilamentPlugin\Models;

use Stephen<PERSON>org\BaseFilamentPlugin\Events\TagAttached;
use <PERSON><PERSON>org\BaseFilamentPlugin\Events\TagDetached;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceLogActivity;
use Illuminate\Database\Eloquent\Relations\MorphPivot;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\Model\CCTraitModelAdmin;

class TagPivot extends MorphPivot
{
    use CCTraitModelAdmin;
    use LogsActivity;

    protected $table = 'tag_pivot';

    /**
     * @var array
     */
    protected $guarded = [];

    /**
     * @return void
     */
    protected static function booted(): void
    {
        parent::boot();

        static::created(function ($pivot)
        {
            TagAttached::dispatch($pivot);
        });

        static::deleted(function ($pivot)
        {
            TagDetached::dispatch($pivot);
        });
    }

    public function taggable(): \Illuminate\Database\Eloquent\Relations\MorphTo
    {
        return $this->morphTo();
    }

    /**
     * @return LogOptions
     */
    public function getActivitylogOptions(): LogOptions
    {
        return ServiceLogActivity::getDefaultOptions()
            ->useLogName('標籤關係');
    }

}
