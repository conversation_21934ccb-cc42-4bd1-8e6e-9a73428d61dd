<?php

namespace <PERSON><PERSON><PERSON><PERSON>\BaseFilamentPlugin\Models;

use <PERSON><PERSON>org\BaseFilamentPlugin\Enum\EnumContactStatus;
use Stephen<PERSON>org\BaseFilamentPlugin\Enum\EnumContactType;
use <PERSON><PERSON>org\BaseFilamentPlugin\Events\ContactMessageCreated;
use Stephenchenorg\BaseFilamentPlugin\Events\ContactMessageDelete;
use Stephenchenorg\BaseFilamentPlugin\Events\ContactMessageRead;
use Stephen<PERSON>org\BaseFilamentPlugin\Events\ContactMessageReplied;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Stephenchenorg\BaseFilamentPlugin\Database\Factories\ContactFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\Traits\LogsActivity;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\Model\CCTraitModelAdmin;

class Contact extends Model
{
    use HasFactory;
    use SoftDeletes;
    use CCTraitModelAdmin;

    /**
     * @var array
     */
    protected $guarded = [];

    /**
     * @var array
     */
    protected $casts = [
        'type'   => EnumContactType::class,
        'status' => EnumContactStatus::class,
    ];


    /**
     * Boot the model and dispatch events.
     *
     * @return void
     */
    protected static function boot(): void
    {
        parent::boot();

        static::created(function ($contact)
        {
            ContactMessageCreated::dispatch($contact);
        });

        static::deleted(function ($contact)
        {
            ContactMessageDelete::dispatch($contact);
        });

        static::updated(function ($contact)
        {
            if ($contact->status === EnumContactStatus::READ) {
                ContactMessageRead::dispatch($contact);
            } elseif ($contact->status === EnumContactStatus::REPLIED) {
                ContactMessageReplied::dispatch($contact);
            }
        });
    }

    /**
     * Define the relationship.
     */
    public function files(): HasMany
    {
        return $this->hasMany(ContactFile::class);
    }



    /**
     * Create a new factory instance for the model.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    protected static function newFactory(): \Stephenchenorg\BaseFilamentPlugin\Database\Factories\ContactFactory
    {
        return ContactFactory::new();
    }
}
