<?php

namespace Stephenchenorg\BaseFilamentPlugin\Models;

use Stephenchenorg\BaseFilamentPlugin\Service\ServiceLanguage;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceLogActivity;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\Model\CCTraitModelAdmin;

class RewardCategory extends Model
{
    use HasFactory;
    use CCTraitModelAdmin;
    use LogsActivity;

    /**
     * @var array
     */
    protected $guarded = [];

    /**
     * Define the relationship with reward records.
     */
    public function rewardRecords(): HasMany
    {
        return $this->hasMany(RewardRecord::class);
    }

    /**
     * Define the relationship with reward category translations.
     */
    public function translations(): HasMany
    {
        return $this->hasMany(RewardCategoryTranslation::class);
    }

    public function getNameAttribute(): string
    {
        return $this->translations()->where('lang', ServiceLanguage::getDefaultLanguage())->first()?->title;
    }

    /**
     * @return LogOptions
     */
    public function getActivitylogOptions(): LogOptions
    {
        return ServiceLogActivity::getDefaultOptions()
            ->useLogName('獎勵類別');
    }
}
