<?php

namespace Stephen<PERSON>org\BaseFilamentPlugin\Models;

use Stephenchenorg\BaseFilamentPlugin\Service\ServiceLogActivity;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Stephenchenorg\BaseFilamentPlugin\Database\Factories\ProductAttributeFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\Model\CCTraitModelAdmin;

class ProductAttribute extends Model
{
    use HasFactory;
    use CCTraitModelAdmin;
    use LogsActivity;


    /**
     * @var array
     */
    protected $guarded = [];

    /**
     * Define the relationship.
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Define the relationship.
     */
    public function translations(): HasMany
    {
        return $this->hasMany(ProductAttributeTranslation::class);
    }


    /**
     * Define the relationship.
     */
    public function items(): HasMany
    {
        return $this->hasMany(ProductAttributeItem::class);
    }

    /**
     * @return LogOptions
     */
    public function getActivitylogOptions(): LogOptions
    {
        return ServiceLogActivity::getDefaultOptions()
            ->useLogName('產品屬性');
    }


    /**
     * Create a new factory instance for the model.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    protected static function newFactory(): \Stephenchenorg\BaseFilamentPlugin\Database\Factories\ProductAttributeFactory
    {
        return ProductAttributeFactory::new();
    }
}
