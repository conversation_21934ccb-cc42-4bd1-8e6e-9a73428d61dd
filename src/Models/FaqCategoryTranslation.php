<?php

namespace Stephen<PERSON>org\BaseFilamentPlugin\Models;

use Stephenchenorg\BaseFilamentPlugin\Service\ServiceLogActivity;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\Model\CCTraitModelAdmin;

class FaqCategoryTranslation extends Model
{
    use HasFactory;
    use CCTraitModelAdmin;
    use LogsActivity;

    /**
     * @var array
     */
    protected $guarded = [];

    /**
     * Define the relationship with FAQ Category.
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(FaqCategory::class, 'faq_category_id', 'id');
    }

    /**
     * @return LogOptions
     */
    public function getActivitylogOptions(): LogOptions
    {
        return ServiceLogActivity::getDefaultOptions()
            ->useLogName('問與答分類的翻譯文字');
    }
}
