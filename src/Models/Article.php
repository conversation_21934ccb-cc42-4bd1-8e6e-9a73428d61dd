<?php

namespace <PERSON><PERSON>org\BaseFilamentPlugin\Models;

use <PERSON><PERSON>org\BaseFilamentPlugin\Events\ArticleCreated;
use <PERSON><PERSON>org\BaseFilamentPlugin\Events\ArticleDeleting;
use Stephenchenorg\BaseFilamentPlugin\Events\ArticleUpdated;

use Stephen<PERSON>org\BaseFilamentPlugin\Models\Traits\DateTimeTrait;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceLogActivity;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Stephenchenorg\BaseFilamentPlugin\Database\Factories\ArticleFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\Model\CCTraitModelAdmin;

class Article extends Model
{
    use HasFactory, DateTimeTrait;
    use CCTraitModelAdmin;
    use LogsActivity;

    /**
     * @var array
     */
    protected $guarded = [];

    /**
     * @return void
     */
    protected static function boot(): void
    {
        parent::boot();

        static::created(function ($article)
        {
            ArticleCreated::dispatch($article);
        });

        static::deleting(function ($article)
        {
            ArticleDeleting::dispatch($article);
        });

        static::updated(function ($article)
        {
            ArticleUpdated::dispatch($article);
        });

    }

    /**
     * Define the relationship with Article category.
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(ArticleCategory::class, 'article_category_id');
    }

    /**
     * Define the relationship with Article Lang Relations.
     */
    public function translations(): HasMany
    {
        return $this->hasMany(ArticleTranslation::class);
    }

    /**
     * Define the relationship.
     */
    public function tags(): MorphToMany
    {
        return $this->morphToMany(Tag::class, 'taggable', 'tag_pivot')->using(TagPivot::class);
    }

    /**
     * @return LogOptions
     */
    public function getActivitylogOptions(): LogOptions
    {
        return ServiceLogActivity::getDefaultOptions()
            ->useLogName('文章');
    }

    /**
     * Create a new factory instance for the model.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    protected static function newFactory(): \Stephenchenorg\BaseFilamentPlugin\Database\Factories\ArticleFactory
    {
        return ArticleFactory::new();
    }
}
