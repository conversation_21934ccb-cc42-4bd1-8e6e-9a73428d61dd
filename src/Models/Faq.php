<?php

namespace <PERSON>g\BaseFilamentPlugin\Models;

use <PERSON><PERSON>org\BaseFilamentPlugin\Events\FaqCreated;
use <PERSON><PERSON>org\BaseFilamentPlugin\Events\FaqDeleting;
use Stephenchenorg\BaseFilamentPlugin\Events\FaqUpdated;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceLogActivity;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Stephenchenorg\BaseFilamentPlugin\Database\Factories\FaqFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\Model\CCTraitModelAdmin;

class Faq extends Model
{
    use HasFactory;
    use CCTraitModelAdmin;
    use LogsActivity;

    /**
     * @var array
     */
    protected $guarded = [];

    /**
     * @return void
     */
    protected static function boot(): void
    {
        parent::boot();

        static::created(function ($faq)
        {
            FaqCreated::dispatch($faq);
        });

        static::deleting(function ($faq)
        {
            FaqDeleting::dispatch($faq);
        });

        static::updated(function ($faq)
        {
            FaqUpdated::dispatch($faq);
        });

    }

    /**
     * Define the relationship with FAQ category.
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(FaqCategory::class, 'faq_category_id');
    }

    /**
     * Define the relationship with FAQ Lang Relations.
     */
    public function translations(): HasMany
    {
        return $this->hasMany(FaqTranslation::class);
    }

    /**
     * @return LogOptions
     */
    public function getActivitylogOptions(): LogOptions
    {
        return ServiceLogActivity::getDefaultOptions()
            ->useLogName('問與答');
    }

    /**
     * Create a new factory instance for the model.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    protected static function newFactory(): \Stephenchenorg\BaseFilamentPlugin\Database\Factories\FaqFactory
    {
        return FaqFactory::new();
    }
}
