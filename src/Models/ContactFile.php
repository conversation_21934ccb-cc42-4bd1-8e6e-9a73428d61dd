<?php

namespace Stephenchenorg\BaseFilamentPlugin\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\Model\CCTraitModelAdmin;

class ContactFile extends Model
{
    use HasFactory;
    use CCTraitModelAdmin;


    /**
     * @var array
     */
    protected $guarded = [];


    /**
     * Define the relationship.
     */
    public function Contact(): BelongsTo
    {
        return $this->belongsTo(Contact::class);
    }

}
