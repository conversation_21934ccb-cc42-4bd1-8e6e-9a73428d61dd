<?php

namespace Stephenchenorg\BaseFilamentPlugin\Models;

use Stephenchenorg\BaseFilamentPlugin\Enum\EnumVerification;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

final class Verification extends Model
{
    use HasFactory;

    /**
     * @var string[]
     */
    protected $guarded = [

    ];

    /**
     * @var class-string[]
     */
    protected $casts = [
        'type' => EnumVerification::class,
    ];

    /**
     * @return MorphTo
     */
    public function verifiable(): MorphTo
    {
        return $this->morphTo();
    }
}
