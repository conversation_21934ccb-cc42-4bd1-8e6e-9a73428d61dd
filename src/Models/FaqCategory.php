<?php

namespace <PERSON><PERSON>g\BaseFilamentPlugin\Models;

use <PERSON><PERSON>org\BaseFilamentPlugin\Events\FaqCategoryCreated;
use Stephenchenorg\BaseFilamentPlugin\Events\FaqCategoryDeleted;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceLanguage;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceLogActivity;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Stephenchenorg\BaseFilamentPlugin\Database\Factories\FaqCategoryFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\Model\CCTraitModelAdmin;

class FaqCategory extends Model
{
    use HasFactory;
    use CCTraitModelAdmin;
    use LogsActivity;


    /**
     * @var array
     */
    protected $guarded = [];

    protected static function booted(): void
    {
        static::created(function ($category)
        {
            FaqCategoryCreated::dispatch($category);
        });

        static::deleted(function ($category)
        {
            FaqCategoryDeleted::dispatch($category);
        });
    }

    /**
     * Define the relationship with FAQs.
     */
    public function faqs(): HasMany
    {
        return $this->hasMany(Faq::class);
    }

    /**
     * Define the relationship with FAQ Category Lang Relations.
     */
    public function translations(): HasMany
    {
        return $this->hasMany(FaqCategoryTranslation::class);
    }


    public function getNameAttribute(): string
    {
        return $this->translations()->where('lang', ServiceLanguage::getDefaultLanguage())->first()?->title;
    }

    /**
     * @return LogOptions
     */
    public function getActivitylogOptions(): LogOptions
    {
        return ServiceLogActivity::getDefaultOptions()
            ->useLogName('問與答分類');
    }


    /**
     * Create a new factory instance for the model.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    protected static function newFactory(): \Stephenchenorg\BaseFilamentPlugin\Database\Factories\FaqCategoryFactory
    {
        return FaqCategoryFactory::new();
    }
}
