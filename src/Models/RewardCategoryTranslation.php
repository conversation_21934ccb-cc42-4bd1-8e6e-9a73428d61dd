<?php

namespace Stephenchenorg\BaseFilamentPlugin\Models;

use Stephenchenorg\BaseFilamentPlugin\Service\ServiceLogActivity;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\Model\CCTraitModelAdmin;

class RewardCategoryTranslation extends Model
{
    use HasFactory;
    use CCTraitModelAdmin;
    use LogsActivity;

    /**
     * @var array
     */
    protected $guarded = [];

    /**
     * Define the relationship with reward category.
     */
    public function rewardCategory(): BelongsTo
    {
        return $this->belongsTo(RewardCategory::class);
    }

    /**
     * @return LogOptions
     */
    public function getActivitylogOptions(): LogOptions
    {
        return ServiceLogActivity::getDefaultOptions()
            ->useLogName('獎勵類別翻譯');
    }
}
