<?php

namespace Stephenchenorg\BaseFilamentPlugin\Models\Traits;


use Carbon\Carbon;

trait DateTimeTrait
{


    /**
     * @param $value
     * @return string
     */
    public function getCreatedAtAttribute($value): string
    {
        return $this->convertToTimeZone(Carbon::parse($value));
    }

    /**
     * @param $date
     * @return string
     */
    public function convertToTimeZone($date): string
    {
        // 之後從資料庫拿
//        $timezone = config('app.timezone');
        $timezone = 'Asia/Taipei';
        return \Illuminate\Support\Carbon::createFromTimeString($date, 'UTC')
            ->setTimezone($timezone)
            ->toDateTimeString();
    }

    /**
     * @param $value
     * @return ?string
     */
    public function getUpdatedAtAttribute($value): ?string
    {
        if (empty($value)) {
            return null;
        }
        return $this->convertToTimeZone($value);
    }

    /**
     * Get the started_at attribute in Y-m-d format.
     */
    public function getStartedAtAttribute($value): ?string
    {
        if (empty($value)) {
            return null;
        }
        return $this->convertToTimeZone(Carbon::parse($value));
    }

    /**
     * Get the ended_at attribute in Y-m-d format.
     */
    public function getEndedAtAttribute($value): ?string
    {
        if (empty($value)) {
            return null;
        }
        return $this->convertToTimeZone(Carbon::parse($value));
    }


}
