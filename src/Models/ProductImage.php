<?php

namespace Stephenchenorg\BaseFilamentPlugin\Models;

use Stephenchenorg\BaseFilamentPlugin\Service\ServiceLogActivity;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\Model\CCTraitModelAdmin;

class ProductImage extends Model
{
    use HasFactory;
    use CCTraitModelAdmin;
    use LogsActivity;

    /**
     * @var array
     */
    protected $guarded = [];

    /**
     * @return void
     */
    public static function boot(): void
    {
        parent::boot();

        // 當模型保存後觸發的事件
        static::saved(function ($productImage)
        {
            // 如果當前圖片被設為默認
            if ($productImage->is_default) {
                // 把其他同一 product_id 的圖片的 is_default 設為 false
                static::query()->where('product_id', $productImage->product_id)
                    ->where('id', '!=', $productImage->id)
                    ->update(['is_default' => false]);
            }
        });
    }

    /**
     * Define the relationship.
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * @return LogOptions
     */
    public function getActivitylogOptions(): LogOptions
    {
        return ServiceLogActivity::getDefaultOptions()
            ->useLogName('產品圖片');
    }
}
