<?php

namespace Stephen<PERSON>org\BaseFilamentPlugin\Models;

use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Stephen<PERSON>org\BaseFilamentPlugin\Events\ProductSpecificationCreated;
use Stephenchenorg\BaseFilamentPlugin\Events\ProductSpecificationDeleted;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceLogActivity;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Stephenchenorg\BaseFilamentPlugin\Database\Factories\ProductSpecificationFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\Model\CCTraitModelAdmin;

class ProductSpecification extends Model
{
    use HasFactory;
    use CCTraitModelAdmin;
    use LogsActivity;
    use SoftDeletes;

    /**
     * @var array
     */
    protected $guarded = [];

    protected static function booted(): void
    {
        static::created(function ($specification)
        {
            ProductSpecificationCreated::dispatch($specification);
        });

        static::deleted(function ($specification)
        {
            ProductSpecificationDeleted::dispatch($specification);
        });
    }

    /**
     * Define the relationship.
     */
    public function translations(): HasMany
    {
        return $this->hasMany(ProductSpecificationTranslation::class);
    }

    /**
     * Define the relationship.
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class, 'product_id');
    }

    /**
     * Define the relationship to reserving items.
     */
    public function reservingItem(): HasOne
    {
        return $this->hasOne(ProductSpecificationReservingItem::class);
    }

    /**
     * @return LogOptions
     */
    public function getActivitylogOptions(): LogOptions
    {
        return ServiceLogActivity::getDefaultOptions()
            ->useLogName('產品規格');
    }

    /**
     * Create a new factory instance for the model.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    protected static function newFactory(): \Stephenchenorg\BaseFilamentPlugin\Database\Factories\ProductSpecificationFactory
    {
        return ProductSpecificationFactory::new();
    }
}
