<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Imports;

use Stephenchenorg\BaseFilamentPlugin\Enum\EnumProductType;
use Stephenchenorg\BaseFilamentPlugin\Enum\EnumTagType;
use Stephenchenorg\BaseFilamentPlugin\Models\Product;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductCategory;
use Stephenchenorg\BaseFilamentPlugin\Models\Tag;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceLanguage;
use Filament\Actions\Imports\ImportColumn;
use Filament\Actions\Imports\Importer;
use Filament\Actions\Imports\Models\Import;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;

class ProductImporter extends Importer
{

    protected static ?string $model = Product::class;

    public static function getColumns(): array
    {
        $languages = ServiceLanguage::getLanguages();
        $columns = [

            ImportColumn::make('type')
                ->label('種類')
                ->exampleHeader('種類')
                ->example([EnumProductType::Product->getLabel(), EnumProductType::Product->getLabel()])
                ->rules([
                    'in:' . implode(',', collect(EnumProductType::cases())
                        ->map(fn($case) => $case->value)
                        ->toArray()),
                ])
                ->requiredMapping()
                ->castStateUsing(function ($state) {

                    foreach (EnumProductType::cases() as $case) {
                        if ($case->getLabel() === $state) {
                            return $case->value;
                        }
                    }

                    return EnumProductType::Product->value;
                }),


            ImportColumn::make('product_category_id')
                ->label('產品類別')
                ->relationship(name: 'category', resolveUsing: function (string $state): ?ProductCategory {
                    return ProductCategory::query()
                        ->where('code', $state)
                        ->orWhere(function ($query) use ($state) {
                            return $query->whereHas('translations', function ($query) use ($state) {
                                return $query->where('title', '=', $state);
                            });
                        })
                        ->first();
                })
                ->exampleHeader('產品類別')
                ->example(function () {
                    $defaultLang = ServiceLanguage::getDefaultLanguage();
                    $category = ProductCategory::query()
                        ->with(['translations' => function ($query) use ($defaultLang) {
                            $query->where('lang', $defaultLang);
                        }])
                        ->first();

                    $categoryTitle = $category ? $category->translations->first()->title : null;

                    return [$categoryTitle, $categoryTitle];
                })
                ->rules(['required'])
                ->requiredMapping(),


            ImportColumn::make('part_number')
                ->label('產品型號')
                ->rules(['max:100', 'nullable'])
                ->exampleHeader('產品型號')
                ->example(['PN-100', 'PN-200']),

            ImportColumn::make('status')
                ->label('啟用/停用')
                ->boolean()
                ->exampleHeader('啟用/停用')
                ->example([1, 0]),

            ImportColumn::make('is_hottest')
                ->label('(是/否)熱門')
                ->exampleHeader('(是/否)熱門')
                ->example([1, 0]),

            ImportColumn::make('is_newest')
                ->label('(是/否)最新')
                ->boolean()
                ->exampleHeader('(是/否)最新')
                ->example([1, 0]),

            ImportColumn::make('sort')
                ->label('排序')
                ->numeric()
                ->exampleHeader('排序')
                ->example([2, 3])
                ->castStateUsing(function ($state) {
                    return $state === null ? 0 : (int)$state;
                }),

            ImportColumn::make('started_at')
                ->label('開始日期')
                ->exampleHeader('開始日期')
                ->example(['2024-12-01', null])
                ->rules(['nullable', 'date']),

            ImportColumn::make('ended_at')
                ->label('結束日期')
                ->exampleHeader('結束日期')
                ->example([null, '2025-12-31'])
                ->rules(['nullable', 'date', 'after_or_equal:started_at']),
        ];

        // 為每個語言添加翻譯欄位
        foreach ($languages as $lang) {
            // 標題欄位
            $columns[] = ImportColumn::make("title_{$lang}")
                ->label("產品名稱 ({$lang})")
                ->fillRecordUsing(function ($record) {
                    // 不要fill 到欄位 要儲存到relationship
                })
                ->exampleHeader("產品名稱 ({$lang})")
                ->example(['產品一', '產品二'])
                ->rules(['required', 'string'])
                ->requiredMapping();


            // 內容欄位 1
            $columns[] = ImportColumn::make("content_1_{$lang}")
                ->label("內容一 ({$lang})")
                ->fillRecordUsing(function ($record) {
                    // 不要fill 到欄位 要儲存到relationship
                })
                ->exampleHeader("內容一 ({$lang})")
                ->example(['內容一', '內容一'])
                ->rules(['nullable', 'string'])
                ->requiredMapping(false);

            // 內容欄位 2
            $columns[] = ImportColumn::make("content_2_{$lang}")
                ->label("內容二 ({$lang})")
                ->fillRecordUsing(function ($record) {
                    // 不要fill 到欄位 要儲存到relationship
                })
                ->exampleHeader("內容二 ({$lang})")
                ->example(['內容二', '內容二'])
                ->rules(['nullable', 'string'])
                ->requiredMapping(false);

            // 內容欄位 3
            $columns[] = ImportColumn::make("content_3_{$lang}")
                ->label("內容三 ({$lang})")
                ->fillRecordUsing(function ($record) {
                    // 不要fill 到欄位 要儲存到relationship
                })
                ->exampleHeader("內容三 ({$lang})")
                ->example(['內容三', '內容三'])
                ->rules(['nullable', 'string'])
                ->requiredMapping(false);


            // 添加標籤欄位
            $columns[] = ImportColumn::make("tags_{$lang}")
                ->label("標籤 ({$lang})")
                ->fillRecordUsing(function ($record) {
                    // 不要fill 到欄位 要儲存到relationship
                })
                ->exampleHeader("標籤 ({$lang})")
                ->example(['標籤1,標籤2,標籤3', '標籤1,標籤4'])
                ->helperText('多個標籤請用逗號分隔')
                ->rules(['nullable', 'string'])
                ->requiredMapping(false);
        }

        return $columns;
    }

    public static function getCompletedNotificationBody(Import $import): string
    {
        $body = 'Your product import has completed and ' . number_format($import->successful_rows) . ' ' . str('row')->plural($import->successful_rows) . ' imported.';

        if ($failedRowsCount = $import->getFailedRowsCount()) {
            $body .= ' ' . number_format($failedRowsCount) . ' ' . str('row')->plural($failedRowsCount) . ' failed to import.';
        }

        return $body;
    }


    public function resolveRecord(): ?Product
    {
        return Product::query()->firstOrNew([
            'part_number' => $this->data['part_number'],
        ]);
    }

    public function saveRecord(): void
    {
        $record = $this->getRecord();

        // Set started_at and ended_at if they exist in the data
        if (isset($this->data['started_at'])) {
            $record->started_at = $this->data['started_at'];
        }

        if (isset($this->data['ended_at'])) {
            $record->ended_at = $this->data['ended_at'];
        }

        $record->save();

        // 儲存標籤的ID數組
        $tagIds = [];

        $defaultLang = ServiceLanguage::getDefaultLanguage();
        $languages = ServiceLanguage::getLanguages();
        $otherLangs = array_filter($languages, fn($lang) => $lang !== $defaultLang);

        foreach ($languages as $lang) {
            $titleKey = "title_{$lang}";
            $content1Key = "content_1_{$lang}";
            $content2Key = "content_2_{$lang}";
            $content3Key = "content_3_{$lang}";


            $title = $this->data[$titleKey] ?? null;
            $content1 = $this->data[$content1Key] ?? null;
            $content2 = $this->data[$content2Key] ?? null;
            $content3 = $this->data[$content3Key] ?? null;


            if ($title) {
                $record->translations()->updateOrCreate([
                    'lang' => $lang,
                ], [
                    'title' => $title,
                    'content_1' => $content1,
                    'content_2' => $content2,
                    'content_3' => $content3,
                    'lang' => $lang,
                ]);
            }
        }


        // 先處理預設語言（中文）的標籤
        $defaultTagsKey = "tags_{$defaultLang}";
        $defaultTagNames = $this->data[$defaultTagsKey] ? explode(',', $this->data[$defaultTagsKey]) : [];
        $defaultTagNames = array_map('trim', $defaultTagNames);
        $defaultTagNames = array_filter($defaultTagNames, function($tag) { return !empty($tag); });

        // 定義其他語言
        $otherLangs = array_filter($languages, function($l) use ($defaultLang) { return $l !== $defaultLang; });

        // 儲存已創建的標籤，索引對應標籤的位置
        $createdTags = [];

        // 先創建預設語言的標籤
        foreach ($defaultTagNames as $index => $tagName) {
            // 查找或創建標籤
            $tag = Tag::query()
                ->whereHas('translations', function ($query) use ($tagName, $defaultLang) {
                    $query->where('title', $tagName)
                        ->where('lang', $defaultLang);
                })
                ->where('type', config('cs.share_tag') ? EnumTagType::SHARED : EnumTagType::PRODUCT)
                ->first();

            if (!$tag) {
                // 如果標籤不存在，則創建新標籤
                $tag = Tag::query()->create([
                    'key' => Str::slug($tagName),
                    'slug' => Str::slug($tagName),
                    'type' => config('cs.share_tag') ? EnumTagType::SHARED : EnumTagType::PRODUCT,
                ]);

                $tag->translations()->create([
                    'title' => $tagName,
                    'lang' => $defaultLang,
                ]);
            }

            $tagIds[] = $tag->id;
            $createdTags[$index] = $tag;
        }

        // 為其他語言添加翻譯
        foreach ($otherLangs as $lang) {

            $langTagsKey = "tags_{$lang}";
            $langTagNames = $this->data[$langTagsKey] ? explode(',', $this->data[$langTagsKey]) : [];
            $langTagNames = array_map('trim', $langTagNames);
            $langTagNames = array_filter($langTagNames, function($tag) { return !empty($tag); });

            // 確保標籤數量相同
            if (!empty($langTagNames) && count($langTagNames) !== count($defaultTagNames)) {
                // 如果標籤數量不一致，拋出驗證異常
                throw ValidationException::withMessages([
                    "tags_{$lang}" => "不同語言的標籤數量必須一致"
                ]);
            }

            // 為每個標籤添加翻譯
            if (count($langTagNames) === count($defaultTagNames)) {
                foreach ($langTagNames as $index => $tagName) {
                    // 取得對應的預設語言標籤
                    if (isset($createdTags[$index])) {
                        $tag = $createdTags[$index];

                        $tag->translations()->updateOrCreate([
                            'lang' => $lang,
                        ],[
                            'title' => $tagName,
                        ]);
                    }
                }
            }
        }

        // 更新產品的標籤
        if (!empty($tagIds)) {
            $record->tags()->sync($tagIds);
        }


    }
}
