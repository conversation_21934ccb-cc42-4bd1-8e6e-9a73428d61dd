<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Imports;

use Stephenchenorg\BaseFilamentPlugin\Enum\EnumProductType;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductCategory;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceLanguage;
use Filament\Actions\Imports\ImportColumn;
use Filament\Actions\Imports\Importer;
use Filament\Actions\Imports\Models\Import;

class ProductCategoryImporter extends Importer
{
    protected static ?string $model = ProductCategory::class;

    public static function getColumns(): array
    {
        $languages = ServiceLanguage::getLanguages();
        $columns = [

            ImportColumn::make('type')
                ->label('種類')
                ->exampleHeader('種類')
                ->example([EnumProductType::Product->getLabel(), EnumProductType::Product->getLabel()])
                ->rules([
                    'in:' . implode(',', collect(EnumProductType::cases())
                        ->map(fn($case) => $case->value)
                        ->toArray()),
                ])
                ->requiredMapping()
                ->castStateUsing(function ($state) {

                    foreach (EnumProductType::cases() as $case) {
                        if ($case->getLabel() === $state) {
                            return $case->value;
                        }
                    }

                    return EnumProductType::Product->value;
                }),

            ImportColumn::make('parent')
                ->label('父類別')
                ->exampleHeader('父類別')
                ->example([null, '類別一'])
                ->rules(['nullable'])
                ->relationship(
                    resolveUsing: function (?string $state, Import $import) {
                        if (empty($state)) {
                            return null;
                        }

                        return ProductCategory::query()
                            ->whereHas('translations', function ($query) use ($state) {
                                $query->where('title', '=', $state);
                            })
                            ->orWhere('code', $state)
                            ->first();
                    }
                )
                ->requiredMapping(),

            ImportColumn::make('is_hottest')
                ->label('是否熱門')
                ->exampleHeader('是否熱門')
                ->example(['1', '0'])
                ->rules(['nullable', 'boolean'])
                ->boolean()
                ->requiredMapping(),

            ImportColumn::make('is_newest')
                ->label('是否最新')
                ->exampleHeader('是否最新')
                ->example(['1', '0'])
                ->rules(['nullable', 'boolean'])
                ->boolean()
                ->requiredMapping(),

            ImportColumn::make('status')
                ->label('狀態')
                ->exampleHeader('狀態')
                ->example(['1', '0'])
                ->rules(['nullable', 'boolean'])
                ->boolean()
                ->requiredMapping(),

            ImportColumn::make('sort')
                ->label('排序')
                ->exampleHeader('排序')
                ->example(['1', '2'])
                ->rules(['nullable', 'integer'])
                ->castStateUsing(function ($state) {
                    return $state === null ? 0 : (int)$state;
                })
                ->requiredMapping(),
        ];

        // 為每個語言添加標題欄位
        foreach ($languages as $lang) {
            // 標題欄位
            $columns[] = ImportColumn::make("title_{$lang}")
                ->label("產品類別名稱 ({$lang})")
                ->fillRecordUsing(function ($record) {
                    // 不要fill 到欄位 要儲存到relationship
                })
                ->exampleHeader("產品類別名稱 ({$lang})")
                ->example(['類別一', '類別二'])
                ->rules(['required', 'string'])
                ->requiredMapping();
        }

        return $columns;
    }


    public function resolveRecord(): ?ProductCategory
    {
        return ProductCategory::query()
            ->where('code',$this->data['code'])
            ->first() ?? new ProductCategory();
    }

    public function saveRecord(): void
    {
        $record = $this->getRecord();
        $record->save();

        $languages = ServiceLanguage::getLanguages();
        foreach ($languages as $lang) {
            $titleKey = "title_{$lang}";
            $title = $this->data[$titleKey] ?? null;

            if ($title) {
                $record->translations()->updateOrCreate([
                    'lang' => $lang,
                ],[
                    'title' => $title,
                ]);
            }
        }
    }

    public static function getCompletedNotificationBody(Import $import): string
    {
        $body = '您的產品類別匯入已完成，';
        $body .= number_format($import->successful_rows) . ' 筆資料匯入成功。';

        if ($failedRowsCount = $import->getFailedRowsCount()) {
            $body .= ' ' . number_format($failedRowsCount) . ' 筆資料匯入失敗。';
        }

        return $body;
    }
}
