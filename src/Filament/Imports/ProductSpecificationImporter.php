<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Imports;

use Stephenchenorg\BaseFilamentPlugin\Enum\EnumProductType;
use Stephenchenorg\BaseFilamentPlugin\Models\Product;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductAttribute;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductAttributeItem;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductSpecification;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceLanguage;
use Filament\Actions\Imports\ImportColumn;
use Filament\Actions\Imports\Importer;
use Filament\Actions\Imports\Models\Import;

class ProductSpecificationImporter extends Importer
{
    protected static ?string $model = ProductSpecification::class;

    public static function getColumns(): array
    {
        $languages = ServiceLanguage::getLanguages();
        $columns = [

            ImportColumn::make('type')
                ->label('種類')
                ->exampleHeader('種類')
                ->example([EnumProductType::Product->getLabel(), EnumProductType::Product->getLabel()])
                ->rules([
                    'in:' . implode(',', collect(EnumProductType::cases())
                        ->map(fn($case) => $case->value)
                        ->toArray()),
                ])
                ->requiredMapping()
                ->castStateUsing(function ($state) {

                    foreach (EnumProductType::cases() as $case) {
                        if ($case->getLabel() === $state) {
                            return $case->value;
                        }
                    }

                    return EnumProductType::Product->value;
                }),


            ImportColumn::make('product_id')
                ->label('所屬產品')
                ->relationship(
                    name:'product',
                    resolveUsing: function (string $state): ?Product {
                        return Product::query()
                            ->where('part_number', $state)
                            ->orWhere(function ($query) use ($state) {
                                return $query->whereHas('translations', function ($query) use ($state) {
                                    return $query->where('title', '=', $state);
                                });
                            })
                            ->first();
                    }
                )
                ->exampleHeader('所屬產品')
                ->example(function () {
                    $defaultLang = ServiceLanguage::getDefaultLanguage();
                    $product = Product::query()
                        ->with(['translations' => function ($query) use ($defaultLang) {
                            $query->where('lang', $defaultLang);
                        }])
                        ->orderBy('id','desc')
                        ->first();

                    $productTitle = $product ? $product->translations->first()->title : null;
                    $productPartNumber = $product ? $product->part_number : 'PN-100';

                    return [$productTitle, $productPartNumber];
                })
                ->rules(['required'])
                ->requiredMapping(),

            ImportColumn::make('sku')
                ->label('SKU')
                ->exampleHeader('SKU')
                ->example(['SKU-001', 'SKU-002'])
                ->rules(['required', 'string', 'max:64'])
                ->requiredMapping(),

            ImportColumn::make('ean')
                ->label('EAN')
                ->exampleHeader('EAN')
                ->example(['1234567890123', '2345678901234'])
                ->rules(['nullable', 'string', 'max:16'])
                ->requiredMapping(false),


            ImportColumn::make('inventory')
                ->label('庫存數量')
                ->exampleHeader('庫存數量')
                ->example([100, 200])
                ->rules(['required', 'integer', 'min:0'])
                ->requiredMapping(),


            ImportColumn::make('status')
                ->label('啟用/停用')
                ->boolean()
                ->exampleHeader('啟用/停用')
                ->example([1, 0])
                ->rules(['boolean'])
                ->requiredMapping(),

            ImportColumn::make('sort')
                ->label('排序')
                ->numeric()
                ->exampleHeader('排序')
                ->example([1, 2])
                ->rules(['nullable', 'integer'])
                ->castStateUsing(function ($state) {
                    return $state === null ? 0 : (int) $state;
                }),

        ];

        // 為每個語言添加翻譯欄位
        foreach ($languages as $lang) {
            // 標題欄位
            $columns[] = ImportColumn::make("title_{$lang}")
                ->label("規格名稱 ({$lang})")
                ->fillRecordUsing(function ($record) {
                    // 不要fill 到欄位 要儲存到relationship
                })
                ->exampleHeader("規格名稱 ({$lang})")
                ->example(['規格一', '規格二'])
                ->rules(['required', 'string'])
                ->requiredMapping();

            $columns[] = ImportColumn::make("content_{$lang}")
                ->label("內容描述 ({$lang})")
                ->fillRecordUsing(function ($record) {
                    // 不要fill 到欄位 要儲存到relationship
                })
                ->exampleHeader("內容描述 ({$lang})")
                ->example(['內容一', '內容二'])
                ->rules(['nullable', 'string'])
                ->requiredMapping();

            // 屬性1
            $columns[] = ImportColumn::make("attr1_{$lang}")
                ->label("屬性1 ({$lang})")
                ->fillRecordUsing(function ($record) {
                    // 不要fill 到欄位 要儲存到relationship
                })
                ->exampleHeader("屬性1 ({$lang})")
                ->example(['顏色：紅色', '顏色:藍色'])
                ->rules(['nullable', 'string'])
                ->requiredMapping(false);

            // 屬性2
            $columns[] = ImportColumn::make("attr2_{$lang}")
                ->label("屬性2 ({$lang})")
                ->fillRecordUsing(function ($record) {
                    // 不要fill 到欄位 要儲存到relationship
                })
                ->exampleHeader("屬性2 ({$lang})")
                ->example(['尺寸：大', '尺寸:小'])
                ->rules(['nullable', 'string'])
                ->requiredMapping(false);

            $columns[] = ImportColumn::make("attr3_{$lang}")
                ->label("屬性3 ({$lang})")
                ->fillRecordUsing(function ($record) {
                    // 不要fill 到欄位 要儲存到relationship
                })
                ->exampleHeader("屬性3 ({$lang})")
                ->example(['型號：A33', '型號:A32'])
                ->rules(['nullable', 'string'])
                ->requiredMapping(false);
        }

        $columns = array_merge($columns,[

            ImportColumn::make('listing_price')
                ->label('建議售價')
                ->exampleHeader('建議售價')
                ->example([1000, 2000])
                ->rules(['required', 'numeric', 'min:0'])
                ->requiredMapping(),

            ImportColumn::make('selling_price')
                ->label('銷售價')
                ->exampleHeader('銷售價')
                ->example([900, 1800])
                ->rules(['required', 'numeric', 'min:0'])
                ->requiredMapping(),
        ]);

        return $columns;
    }

    public function resolveRecord(): ?ProductSpecification
    {
        // 嘗試根據 SKU 和 product_id 查找現有規格
        return ProductSpecification::query()
            ->where('sku', $this->data['sku'])
            ->first() ?? new ProductSpecification();
    }

    public function saveRecord(): void
    {
        $record = $this->getRecord();
        $record->save();

        // 處理翻譯
        $languages = ServiceLanguage::getLanguages();
        foreach ($languages as $lang) {
            $titleKey = "title_{$lang}";
            $contentKey = "content_{$lang}";
            $title = $this->data[$titleKey] ?? null;
            $content = $this->data[$contentKey] ?? null;

            if ($title) {
                $record->translations()->updateOrCreate(
                    ['lang' => $lang],
                    [
                        'title' => $title,
                        'content' => $content,
                        'lang' => $lang,
                    ]
                );
            }

            // 處理屬性
            $this->saveAttributes($record, $lang);
        }
    }


    public function saveAttributes($record, $lang): void
    {
        // 儲存屬性項目的ID
        $attributeItemIds = [];
        $defaultLang = ServiceLanguage::getDefaultLanguage();
        $allLangs = ServiceLanguage::getLanguages();
        $otherLangs = array_filter($allLangs, fn($lang) => $lang !== $defaultLang);

        foreach ([1, 2, 3] as $attrNum) {
            $defaultLangKey = "attr{$attrNum}_{$defaultLang}";
            $defaultLangValue = $this->data[$defaultLangKey] ?? null;

            if ($defaultLangValue) {
                // 處理第一語言，支援中英文冒號
                $normalizedValue = str_replace('：', ':', $defaultLangValue);
                $attrParts = explode(':', $normalizedValue);

                if (count($attrParts) === 2) {
                    $attrTitle = $attrParts[0];
                    $attrOption = $attrParts[1];

                    // 查找是否已有該屬性
                    $attribute = ProductAttribute::query()
                        ->where('product_id', $record->product_id)
                        ->whereHas('translations', function ($query) use ($attrTitle) {
                            $query->where('title', $attrTitle);
                        })
                        ->first();

                    $attributeWasCreated = false;

                    if (!$attribute) {
                        $attribute = ProductAttribute::create([
                            'product_id' => $record->product_id,
                        ]);

                        // 建立第一語言翻譯
                        $attribute->translations()->create([
                            'title' => $attrTitle,
                            'lang' => $defaultLang,
                        ]);

                        $attributeWasCreated = true;
                    }

                    if ($attributeWasCreated) {
                        foreach ($otherLangs as $lang) {
                            $langKey = "attr{$attrNum}_{$lang}";
                            $langValue = $this->data[$langKey] ?? null;

                            if ($langValue) {
                                $normalized = str_replace('：', ':', $langValue);
                                $parts = explode(':', $normalized);
                                if (count($parts) === 2) {
                                    $attribute->translations()->create([
                                        'title' => $parts[0],
                                        'lang' => $lang,
                                    ]);
                                }
                            }
                        }
                    }

                    // 處理屬性選項
                    $attributeItem = ProductAttributeItem::query()
                        ->where('product_attribute_id', $attribute->id)
                        ->whereHas('translations', function ($query) use ($attrOption) {
                            $query->where('title', $attrOption);
                        })
                        ->first();

                    $itemWasCreated = false;

                    if (!$attributeItem) {
                        $attributeItem = ProductAttributeItem::create([
                            'product_attribute_id' => $attribute->id,
                        ]);

                        $attributeItem->translations()->create([
                            'title' => $attrOption,
                            'lang' => $defaultLang,
                        ]);

                        $itemWasCreated = true;
                    }

                    $attributeItemIds[] = $attributeItem->id;

                    if ($itemWasCreated) {
                        foreach ($otherLangs as $lang) {
                            $langKey = "attr{$attrNum}_{$lang}";
                            $langValue = $this->data[$langKey] ?? null;

                            if ($langValue) {
                                $normalized = str_replace('：', ':', $langValue);
                                $parts = explode(':', $normalized);
                                if (count($parts) === 2) {
                                    $attributeItem->translations()->create([
                                        'title' => $parts[1],
                                        'lang' => $lang,
                                    ]);
                                }
                            }
                        }
                    }
                }
            }
        }

        if (!empty($attributeItemIds)) {
            // 按照升序排序
            sort($attributeItemIds);

            // 使用 '-' 連接成字串
            $combinationKey = implode('-', $attributeItemIds);

            // 更新記錄的 combination_key
            $record->combination_key = $combinationKey;
            $record->save();
        }
    }

    public static function getCompletedNotificationBody(Import $import): string
    {
        $body = '您的產品規格匯入已完成，';
        $body .= number_format($import->successful_rows) . ' 筆資料匯入成功。';

        if ($failedRowsCount = $import->getFailedRowsCount()) {
            $body .= ' ' . number_format($failedRowsCount) . ' 筆資料匯入失敗。';
        }

        return $body;
    }
}
