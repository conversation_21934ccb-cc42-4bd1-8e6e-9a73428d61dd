<?php

namespace <PERSON><PERSON>org\BaseFilamentPlugin\Filament\Pages;

use <PERSON><PERSON>org\BaseFilamentPlugin\Models\SystemSetting;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceSystem;
use Stephenchenorg\BaseFilamentPlugin\Traits\CCTraitFormContent;
use Exception;
use Filament\Actions\Action;
use Filament\Actions\Concerns\CanSubmitForm;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Pages\Concerns\CanUseDatabaseTransactions;
use Filament\Pages\Page;
use Stephenchenorg\CsCoreFilamentPlugin\CCUtility;


/**
 * @property Form $form
 */
class SystemSettingPage extends Page implements HasForms
{
    use CanSubmitForm;
    use InteractsWithForms;
    use CanUseDatabaseTransactions;
    use CCTraitFormContent;


    protected static ?string $navigationIcon = 'heroicon-o-cog-6-tooth';

    protected static string $view = 'base-filament-plugin::filament.pages.system-setting-page';

    protected static ?string $navigationGroup = '系統管理';
    protected static ?string $navigationLabel = '功能開關';

    protected static ?string $title = '功能開關';

    protected static ?int $navigationSort = 10;

    /**
     * @var array|null
     */
    public ?array $data = [];

    public static function canAccess(): bool
    {
        $user = auth()->user();
        return $user && $user->isSuperAdmin();
    }

    public function mount(): void
    {
        $this->data = SystemSetting::query()->firstOrFail()->toArray();
        $this->form->fill($this->data);
    }

    /**
     * @param Form $form
     * @return Form
     */
    public function form(Form $form): Form
    {
        return $form
            ->schema($this->getFormSchema())
            ->statePath('data');
    }

    protected function getFormSchema(): array
    {
        return [

            self::getFormToggle('seo_enabled')
                ->label('啟用 SEO')
                ->default(true),
        ];
    }

    protected function getActions(): array
    {
        return [
            Action::make('save')
                ->action(fn() => $this->save()),
        ];
    }

    public function save(): void
    {
        try {

            $this->beginDatabaseTransaction();

            $data = $this->form->getState();

            SystemSetting::query()->first()->update($data);

            $this->commitDatabaseTransaction();

            ServiceSystem::cacheSystemSettings($data);

            Notification::make()
                ->success()
                ->color('success')
                ->title('更新系統設定成功')
                ->send();

        } catch (Exception $e) {

            $this->rollBackDatabaseTransaction();

            $message = $e->getMessage();
            $message = CCUtility::isProduction() ? '更新系統設定失敗' : "更新系統設定失敗, 原因：$message";
            Notification::make()
                ->danger()
                ->color('danger')
                ->title($message)
                ->send();
        }
    }
}
