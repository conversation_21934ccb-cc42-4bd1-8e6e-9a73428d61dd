<?php

namespace Stephen<PERSON>org\BaseFilamentPlugin\Filament\Pages;

use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Pages\Page;
use Stephenchenorg\BaseFilamentPlugin\Enum\EnumShippingMethod;
use Stephenchenorg\BaseFilamentPlugin\Models\ShippingMethod;

class ShippingMethodSettingPage extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-truck';
    protected static string $view = 'base-filament-plugin::filament.pages.shipping-method-setting';
    protected static ?string $navigationGroup = '系統設定';
    protected static ?string $title = '物流設定';
    protected static ?string $navigationLabel = '物流設定';

    public ?array $data = [];

    public function mount(): void
    {
        $this->loadData();
        $this->form->fill($this->data);
    }

    public function form(Form $form): Form
    {
        $sections = [];

        foreach (EnumShippingMethod::getEditableMethods() as $enumCase) {

            $sections[] = Section::make($enumCase->getLabel())
                ->description("設定 {$enumCase->getLabel()} 的相關參數")
                ->schema([

                    TextInput::make("methods.{$enumCase->value}.default_amount")
                        ->label('預設運費金額')
                        ->numeric()
                        ->minValue(0)
                        ->suffix('元')
                        ->required(),

                ])
                ->columns(2);
        }

        return $form
            ->schema($sections)
            ->statePath('data');
    }


    public function submit(): void
    {
        $data = $this->form->getState();


        foreach ($data['methods'] as $type => $methodData) {

            // 更新或建立運送方式
            ShippingMethod::query()
                ->where('type', $type)
                ->updateOrCreate([
                    'type' => $type
                ], $methodData);

        }

        Notification::make()
            ->title('設定已儲存')
            ->success()
            ->send();
    }

    private function loadData(): void
    {
        $availableTypes = array_map(fn($method) => $method->value, EnumShippingMethod::getEditableMethods());
        $this->data['methods'] = ShippingMethod::query()
            ->whereIn('type', $availableTypes)
            ->get()
            ->keyBy(function ($shippingMethod) {
                return $shippingMethod->type->value;
            })
            ->toArray();
    }
}
