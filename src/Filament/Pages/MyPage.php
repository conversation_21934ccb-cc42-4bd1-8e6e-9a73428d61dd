<?php

namespace Stephen<PERSON>org\BaseFilamentPlugin\Filament\Pages;

use Exception;
use Filament\Actions\Concerns\CanSubmitForm;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Tabs\Tab;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Navigation\NavigationItem;
use Filament\Notifications\Notification;
use Filament\Pages\Concerns\CanUseDatabaseTransactions;
use Filament\Pages\Concerns\HasUnsavedDataChangesAlert;
use Filament\Pages\Page;
use Filament\Resources\Concerns\HasTabs;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Schema;
use Stephenchenorg\BaseFilamentPlugin\Enum\EnumFieldType;
use Stephenchenorg\BaseFilamentPlugin\Models\Page as PageModel;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceLanguage;
use Stephenchenorg\BaseFilamentPlugin\Traits\CCTraitColumn;
use Stephenchenorg\BaseFilamentPlugin\Traits\CCTraitFormContent;
use Stephenchenorg\CsCoreFilamentPlugin\CCUtility;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormAdmin;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormEditor;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormImage;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormOG;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormSEO;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormTimestamps;


/**
 * @property Form $form
 */
class MyPage extends Page implements HasForms
{
    use CanSubmitForm;
    use InteractsWithForms;
    use CanUseDatabaseTransactions;
    use HasUnsavedDataChangesAlert;
    use HasTabs;
    use CCTraitColumn;
    use CCTraitFormAdmin;
    use CCTraitFormTimestamps;
    use CCTraitFormImage;
    use CCTraitFormContent;
    use CCTraitFormEditor;
    use CCTraitFormSEO;
    use CCTraitFormOG;


    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static string $view = 'base-filament-plugin::filament.pages.my-page';

    protected static ?string $navigationLabel = '靜態頁面';

    protected static ?int $navigationSort = 40;


    /**
     * TODO 改成private會壞掉
     * @var PageModel
     */
    public PageModel $page;

    /**
     * @var array|null
     */
    public ?array $data = [];

    public static function getNavigationItems(): array
    {
        $pages = PageModel::all();

        $navigationItems = [];

        foreach ($pages as $page) {

            $pageName = $page->translations()->where('lang', ServiceLanguage::getDefaultLanguage())->first()->title;

            $navigationItems[] = NavigationItem::make()
                ->label($pageName)
                ->icon('heroicon-o-document')
                ->url(fn() => route('filament.admin.pages.my-page', ['id' => $page->id]))
                ->group('靜態頁面');
        }

        return $navigationItems;
    }

    public static function canAccess(): bool
    {
        return Gate::allows('update Page', Page::class);
    }

    /**
     * @param $id
     * @return void
     */
    public function mount(): void
    {
        $id = request()->query('id');

        $this->page = PageModel::query()
            ->findOrFail($id);

        $this->activeTab = ServiceLanguage::getDefaultLanguage();

        $this->form->fill($this->page->toArray());
    }

    /**
     * @return string
     */
    public function getTitle(): string
    {
        return $this->page->translations()->where('lang', ServiceLanguage::getDefaultLanguage())->first()->title;
    }

    /**
     * @param Form $form
     * @return Form
     */
    public function form(Form $form): Form
    {
        return $form
            ->model($this->page)
            ->schema($this->getFormSchema())
            ->statePath('data');
    }

    /**
     * @return array
     */
    public function getFormSchema(): array
    {
        $lang = $this->activeTab;

        return [

            Repeater::make("translations")
                ->relationship('translations', function ($query) use ($lang) {
                    $query->where('lang', $lang);
                })
                ->schema([
                    Hidden::make('lang')->default($lang),
                    Section::make()
                        ->heading('基本設定')
                        ->schema([
                            self::getFormTitle(),
                        ]),
                    self::getFormSectionSEO(),
                    self::getFormSectionOG(),
                ])
                ->afterStateHydrated(function ($state, callable $set) use ($lang) {
                    if (empty($state)) {
                        $set('translations', [
                            ['lang' => $lang],
                        ]);
                    }
                })
                ->defaultItems(1) // not working
                ->addable(false)
                ->deletable(false)
                ->label(''),

            Section::make()
                ->heading('欄位資料')
                ->schema([

                    Repeater::make("fields")
                        ->relationship('fields')
                        ->addable(false)
                        ->deletable(false)
                        ->schema([

                            Section::make('field')
                                ->heading(function ($state) {
                                    return $state['helper_text'];
                                })
                                ->collapsed()
                                ->schema([
                                    Repeater::make('translations')
                                        ->columnSpanFull()
                                        ->relationship('translations', function ($query) use ($lang) {
                                            $query->where('lang', $lang);
                                        })
                                        ->schema(function ($get) use ($lang) {
                                            return [
                                                Hidden::make('lang')->default($lang),
                                                $this->getContentElementForm(EnumFieldType::tryFrom($get('type'))),
                                            ];
                                        })
                                        ->afterStateHydrated(function ($state, callable $set) use ($lang) {
                                            if (empty($state)) {
                                                $set('translations', [
                                                    ['lang' => $lang],
                                                ]);
                                            }
                                        })
                                        ->defaultItems(1) // not working
                                        ->addable(false)
                                        ->deletable(false)
                                        ->label(''),
                                ]),

                        ])
                        ->label(''),
                ]),

            self::getFormTimestamps(),
            self::getFormSectionAdminId(),
        ];
    }

    /**
     * @param EnumFieldType $fieldType
     * @return mixed
     */
    public function getContentElementForm(EnumFieldType $fieldType): mixed
    {
        return match ($fieldType) {

            EnumFieldType::TEXTAREA => Textarea::make('content')
                ->label(EnumFieldType::TEXTAREA->getLabel())
                ->required(),

            EnumFieldType::IMAGE => self::getFormSectionImage()
                ->collapsed(false),

            EnumFieldType::TEXT => TextInput::make('content')
                ->label(EnumFieldType::TEXT->getLabel())
                ->required(),

            default => self::getFormEditor('content')
                ->label(EnumFieldType::HTML->getLabel() . ' - 圖文編輯器')
                ->required(),
        };
    }

    /**
     * @return array
     */
    public function getTabs(): array
    {
        $tabs = [];
        $languages = ServiceLanguage::getLanguages();

        if(count($languages) < 2) {
            return [];
        }

        foreach ($languages as $lang) {

            $tabs[$lang] = Tab::make($lang)
                ->label(ServiceLanguage::getLanguageNativeName($lang));
        }

        return $tabs;
    }

    /**
     * @return void
     */
    public function resetPage(): void
    {
        $this->form->fill([]);
        $columns = Schema::getColumnListing((new PageModel())->getTable());
        $this->data = array_fill_keys($columns, null);
        $this->form->fill($this->page->toArray());
    }

    public function submit(): void
    {

        try {

            $this->beginDatabaseTransaction();

            $data = $this->form->getState();

            $this->page->update($data);

            $this->form->saveRelationships();

            Notification::make()
                ->success()
                ->color('success')
                ->title('修改頁面欄位成功')
                ->send();

            $this->commitDatabaseTransaction();

        } catch (Exception $e) {

            $this->rollBackDatabaseTransaction();

            $message = $e->getMessage();
            $message = CCUtility::isProduction() ? '修改頁面欄位失敗' : "修改頁面欄位失敗, 原因：$message";
            Notification::make()
                ->danger()
                ->color('danger')
                ->title($message)
                ->send();
        }
    }


}
