<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Pages\Auth;


use Stephenchenorg\BaseFilamentPlugin\Service\ServicePassword;
use Exception;
use Filament\Forms\Components\Component;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Pages\Auth\EditProfile;

final class Profile extends EditProfile
{
    protected static string $view = 'base-filament-plugin::profile';

    /**
     * @param  Form  $form
     * @return Form
     * @throws Exception
     */
    public function form(Form $form): Form
    {
        return $form
            ->schema([

                TextInput::make('last_name')
                    ->label('姓氏')
                    ->maxLength(255)
                    ->unique(ignoreRecord: true),

                TextInput::make('first_name')
                    ->label('名字')
                    ->maxLength(255)
                    ->unique(ignoreRecord: true),

                TextInput::make('email')
                    ->label('電子郵件')
                    ->email()
                    ->disabled()
                    ->maxLength(255)
                    ->unique(ignoreRecord: true),

                TextInput::make('account')
                    ->label('帳號')
                    ->disabled()
                    ->maxLength(255)
                    ->unique(ignoreRecord: true),

                TextInput::make('password')
                    ->label('密碼')
                    ->password()
                    ->rule(ServicePassword::getPasswordRules())
                    ->revealable(filament()->arePasswordsRevealable())
                    ->autocomplete('new-password')
                    ->helperText(ServicePassword::getPasswordText())
                    ->dehydrated(fn($state): bool => filled($state))
                    ->live(debounce: 500)
                    ->same('passwordConfirmation'),

                TextInput::make('passwordConfirmation')
                    ->label('確認密碼')
                    ->password()
                    ->rule(ServicePassword::getPasswordRules())
                    ->revealable(filament()->arePasswordsRevealable())
                    ->helperText(ServicePassword::getPasswordText())
                    ->required()
                    ->visible(fn(Get $get): bool => filled($get('password')))
                    ->dehydrated(false),
            ])
            ->operation('edit')
            ->model($this->getUser())
            ->statePath('data')
            ->inlineLabel(!static::isSimple());
    }

    /**
     * @return Component
     */
    protected function getEmailFormComponent(): Component
    {
        return TextInput::make('email')
            ->label(__('filament-panels::pages/auth/edit-profile.form.email.label'))
            ->email()
            ->disabled()
            ->required()
            ->maxLength(255)
            ->unique(ignoreRecord: true);
    }
}
