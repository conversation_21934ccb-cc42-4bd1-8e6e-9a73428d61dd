<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Pages\Auth;

use Afatmustafa\FilamentTurnstile\Forms\Components\Turnstile;
use Dan<PERSON><PERSON>rin\LivewireRateLimiting\Exceptions\TooManyRequestsException;
use Filament\Facades\Filament;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Http\Responses\Auth\Contracts\LoginResponse;
use Filament\Models\Contracts\FilamentUser;
use Filament\Notifications\Notification;
use Illuminate\Validation\ValidationException;

/**
 *
 */
class Login extends \Filament\Pages\Auth\Login
{
    protected static string $view = 'base-filament-plugin::login';

    /**
     * @param  Form  $form
     * @return Form
     */
    public function form(Form $form): Form
    {
        $components = $this->getComponent();

        if (!app()->environment('local')) {
            $components[] = Turnstile::make('turnstile')
                ->size('normal')
                ->language(config('app.locale'));
        }

        $builder = $form
            ->schema($components);

        $builder->statePath('data');

        return $builder;
    }

    /**
     * @return array
     */
    public function getComponent(): array
    {
        return [
            TextInput::make('email')
                ->label(__('filament-panels::pages/auth/login.form.email.label'))
                ->required()
                ->autocomplete()
                ->default(fn() => app()->environment('local') ? '<EMAIL>' : null)
                ->autofocus(),
            $this->getPasswordFormComponent()
                ->default(fn() => app()->environment('local') ? 'admin' : null)
            ,
            $this->getRememberFormComponent(),
        ];
    }

    /**
     * @return LoginResponse|null
     * @throws ValidationException
     */
    public function authenticate(): ?LoginResponse
    {
        try {
            $this->rateLimit(5);
        } catch (TooManyRequestsException $exception) {
            throw ValidationException::withMessages([
                'email' => __('filament::login.messages.throttled', [
                    'seconds' => $exception->secondsUntilAvailable,
                    'minutes' => ceil($exception->secondsUntilAvailable / 60),
                ]),
            ]);
        }

        $data = $this->form->getState();

        if (!Filament::auth()->attempt([
            'email'    => $data['email'],
            'password' => $data['password'],
        ], $data['remember']) ?? false) {
            Notification::make()
                ->danger()
                ->title(__('base-filament-plugin::auth.failed'))
                ->send();
            return null;
        }

        $user = Filament::auth()->user();

        if (
            ($user instanceof FilamentUser) &&
            (!$user->canAccessPanel(Filament::getCurrentPanel()))
        ) {
            Filament::auth()->logout();

            $this->throwFailureValidationException();
        }

        session()->regenerate();

        return app(LoginResponse::class);
    }
}
