<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Pages;

use Stephenchenorg\BaseFilamentPlugin\Models\CompanySetting;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceLanguage;
use Stephenchenorg\BaseFilamentPlugin\Traits\CCTraitColumn;
use Exception;
use Filament\Actions\Concerns\CanSubmitForm;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Tabs\Tab;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Pages\Concerns\CanUseDatabaseTransactions;
use Filament\Pages\Concerns\HasUnsavedDataChangesAlert;
use Filament\Pages\Page;
use Filament\Resources\Concerns\HasTabs;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Schema;
use Stephenchenorg\CsCoreFilamentPlugin\CCUtility;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormAdmin;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormImage;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormTimestamps;


/**
 * @property Form $form
 */
class CompanySettingPage extends Page implements HasForms
{
    use CanSubmitForm;
    use InteractsWithForms;
    use CanUseDatabaseTransactions;
    use HasUnsavedDataChangesAlert;
    use HasTabs;
    use CCTraitColumn;
    use CCTraitFormAdmin;
    use CCTraitFormTimestamps;
    use CCTraitFormImage;


    /**
     * @var string|null
     */
    protected static ?string $navigationIcon = 'heroicon-o-cog-6-tooth';

    /**
     * @var string|null
     */
    protected static ?string $navigationGroup = '網站資料設定';

    /**
     * @var string
     */
    protected static string $view = 'base-filament-plugin::filament.pages.company-setting-page';

    /**
     * @var string|null
     */
    protected static ?string $navigationLabel = '基本資料設定';

    /**
     * @var string|null
     */
    protected static ?string $title = '基本資料設定';

    protected static ?int $navigationSort = 50;


    /**
     * @var CompanySetting
     */
    public CompanySetting $setting;

    /**
     * @var array|null
     */
    public ?array $data = [];

    /**
     * @return bool
     */
    public static function canAccess(): bool
    {
        return Gate::allows('update CompanySetting', CompanySetting::class);
    }

    /**
     * @return void
     */
    public function mount(): void
    {
        $this->activeTab =  ServiceLanguage::getDefaultLanguage();

        $this->setting = CompanySetting::query()
            ->where('lang', '=', $this->activeTab)
            ->firstOrNew();

        $this->form->fill($this->setting->toArray());
    }

    /**
     * @param  Form  $form
     * @return Form
     */
    public function form(Form $form): Form
    {
        return $form
            ->model($this->setting)
            ->schema($this->getFormSchema())
            ->statePath('data');
    }

    /**
     * @return array
     */
    public function getFormSchema(): array
    {

        return [

            Section::make()
                ->heading('公司基本設定')
                ->schema([

                    TextInput::make('name')
                        ->label('公司名稱')
                        ->required()
                        ->maxLength(80),

                    self::getFormImage('logo')
                        ->label('Logo')
                        ->required(),

                    TextInput::make('description')
                        ->label('公司簡介')
                        ->nullable(),
                ]),

            Section::make()
                ->heading('公司地址')
                ->schema([

                    TextInput::make('vat')
                        ->label('公司統編')
                        ->nullable()
                        ->maxLength(15),

                    TextInput::make('address_1')
                        ->label('公司地址_1')
                        ->nullable()
                        ->maxLength(100),

                    TextInput::make('address_2')
                        ->label('公司地址_2')
                        ->nullable()
                        ->maxLength(100),

                ]),

            Section::make()
                ->heading('公司電話')
                ->schema([

                    TextInput::make('phone_1')
                        ->label('公司電話_1')
                        ->nullable()
                        ->tel()
                        ->maxLength(20),

                    TextInput::make('phone_2')
                        ->label('公司電話_2')
                        ->nullable()
                        ->tel()
                        ->maxLength(20),

                ]),

            Section::make()
                ->heading('公司電子信箱')
                ->schema([


                    TextInput::make('email_1')
                        ->label('公司電子信箱_1')
                        ->nullable()
                        ->email()
                        ->maxLength(70),

                    TextInput::make('email_2')
                        ->label('公司電子信箱_2')
                        ->nullable()
                        ->email()
                        ->maxLength(70),

                ]),

            Section::make()
                ->heading('公司社群平台')
                ->schema([


                    TextInput::make('line_link')
                        ->label('公司 LINE 連結')
                        ->nullable()
                        ->url()
                        ->maxLength(1024),

                    TextInput::make('fb_link')
                        ->label('公司 FB 連結')
                        ->nullable()
                        ->url()
                        ->maxLength(1024),

                    TextInput::make('ig_link')
                        ->label('公司 IG 連結')
                        ->nullable()
                        ->url()
                        ->maxLength(1024),

                    TextInput::make('twitter_link')
                        ->label('公司 Twitter 連結')
                        ->nullable()
                        ->url()
                        ->maxLength(1024),

                    TextInput::make('threads_link')
                        ->label('公司 Threads 連結')
                        ->nullable()
                        ->url()
                        ->maxLength(1024),

                    TextInput::make('tg_link')
                        ->label('公司 Telegram 連結')
                        ->nullable()
                        ->url()
                        ->maxLength(1024),
                ]),

            self::getFormTimestamps(),
            self::getFormSectionAdminId(),
        ];
    }

    /**
     * @return array
     */
    public function getTabs(): array
    {
        $tabs = [];
        $languages = ServiceLanguage::getLanguages();

        if(count($languages) < 2) {
            return [];
        }

        foreach ($languages as $lang) {

            $tabs[$lang] = Tab::make($lang)
                ->label(ServiceLanguage::getLanguageNativeName($lang));
        }

        return $tabs;
    }

    /**
     * @return void
     */
    public function resetPage(): void
    {
        $this->form->fill([]);
        $columns = Schema::getColumnListing((new CompanySetting())->getTable());
        $this->data = array_fill_keys($columns, null);
        $this->setting = CompanySetting::query()
            ->where('lang', '=', $this->activeTab)
            ->first();
        $this->form->fill($this->setting->toArray());
    }

    /**
     * @return void
     */
    public function submit(): void
    {

        try {

            $this->beginDatabaseTransaction();

            $data = $this->form->getState();

            $this->setting->update($data);

            Notification::make()
                ->success()
                ->color('success')
                ->title('修改成功')
                ->send();

            $this->commitDatabaseTransaction();

        } catch (Exception $e) {

            $this->rollBackDatabaseTransaction();

            $message = $e->getMessage();
            $message = CCUtility::isProduction() ? '修改失敗' : "修改失敗, 原因：$message";
            Notification::make()
                ->danger()
                ->color('danger')
                ->title($message)
                ->send();
        }
    }


}
