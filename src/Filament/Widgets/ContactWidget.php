<?php

namespace Stephen<PERSON>org\BaseFilamentPlugin\Filament\Widgets;

use Stephen<PERSON>org\BaseFilamentPlugin\Models\Contact;
use Stephenchenorg\BaseFilamentPlugin\Service\CountService;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\Gate;

class ContactWidget extends BaseWidget
{
    protected function getStats(): array
    {

        if (!config('cs.contact_visible') || Gate::denies('view-any', Contact::class)) {
            return [];
        }

        $unread = CountService::getContactUnreadCount();
        $unreadUnit = CountService::getUnit('contact_unread');
        $read = CountService::getContactReadCount();
        $readUnit = CountService::getUnit('contact_read');
        $replied = CountService::getContactRepliedCount();
        $repliedUnit = CountService::getUnit('contact_replied');

        return [
            Stat::make('新訊息', "$unread $unreadUnit"),
            Stat::make('已讀訊息', "$read $readUnit"),
            Stat::make('已回覆訊息', "$replied $repliedUnit"),
        ];
    }
}
