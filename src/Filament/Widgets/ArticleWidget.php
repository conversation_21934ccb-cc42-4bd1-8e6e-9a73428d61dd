<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Widgets;

use <PERSON><PERSON>org\BaseFilamentPlugin\Models\Article;
use Stephenchenorg\BaseFilamentPlugin\Service\CountService;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\Gate;

class ArticleWidget extends BaseWidget
{
    protected function getStats(): array
    {
        if (!config('cs.article_visible') || Gate::denies('view-any', Article::class)) {
            return [];
        }

        $name = '文章';
        $articleCategoryCount = CountService::getArticleCategoryCount();
        $articleCategoryUnit = CountService::getUnit('article_category');
        $articleCount = CountService::getArticleCount();
        $articleUnit = CountService::getUnit('article');
        $articleTagCount = CountService::getArticleTagCount();
        $articleTagUnit = CountService::getUnit('article_tag');


        return [
            Stat::make($name, "$articleCount $articleUnit"),
            Stat::make($name.'類別', "$articleCategoryCount $articleCategoryUnit"),
            Stat::make($name.'標籤', "$articleTagCount $articleTagUnit"),
        ];
    }
}
