<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Widgets;

use Stephenchenorg\BaseFilamentPlugin\Models\Product;
use Stephenchenorg\BaseFilamentPlugin\Service\CountService;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\Gate;

class ProductWidget extends BaseWidget
{
    protected function getStats(): array
    {

        if (!config('cs.product_visible') || Gate::denies('view-any', Product::class)) {
            return [];
        }

        $productCategoryCount = CountService::getProductCategoryCount();
        $productCategoryUnit = CountService::getUnit('product_category');
        $productCount = CountService::getProductCount();
        $productUnit = CountService::getUnit('product');
        $productSpecificationCount = CountService::getProductSpecificationCount();
        $productSpecificationUnit = CountService::getUnit('product_specification');
        $productTagCount = CountService::getProductTagCount();
        $productTagUnit = CountService::getUnit('product_tag');

        return [
            Stat::make('產品', "$productCount $productUnit"),
            Stat::make('產品類別', "$productCategoryCount $productCategoryUnit"),
            Stat::make('產品規格', "$productSpecificationCount $productSpecificationUnit"),
            Stat::make('產品標籤', "$productTagCount $productTagUnit"),
        ];
    }
}
