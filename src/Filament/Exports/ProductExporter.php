<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Exports;

use Stephenchenorg\BaseFilamentPlugin\Enum\EnumProductType;
use Stephenchenorg\BaseFilamentPlugin\Models\Product;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceLanguage;
use Filament\Actions\Exports\ExportColumn;
use Filament\Actions\Exports\Exporter;
use Filament\Actions\Exports\Models\Export;

class ProductExporter extends Exporter
{
    protected static ?string $model = Product::class;

    public static function getColumns(): array
    {
        $languages = ServiceLanguage::getLanguages();
        $columns = [
            ExportColumn::make('type')
                ->label('產品種類')
                ->formatStateUsing(fn($state) => collect(EnumProductType::getOptions())
                    ->firstWhere('value', $state)?->getLabel()),

            ExportColumn::make('part_number')
                ->label('產品型號'),

            ExportColumn::make('category.translations.title')
                ->label('產品類別')
                ->formatStateUsing(function ($state, Product $record) {
                    return $record->category?->translations()
                        ->where('lang', ServiceLanguage::getDefaultLanguage())
                        ->first()?->title;
                }),

            ExportColumn::make('is_hottest')
                ->label('是否熱門')
                ->formatStateUsing(fn($state) => $state ? '是' : '否'),

            ExportColumn::make('is_newest')
                ->label('是否最新')
                ->formatStateUsing(fn($state) => $state ? '是' : '否'),

            ExportColumn::make('status')
                ->label('狀態')
                ->formatStateUsing(fn($state) => $state ? '啟用' : '停用'),

            ExportColumn::make('sort')
                ->label('排序'),

            ExportColumn::make('started_at')
                ->label('開始日期'),

            ExportColumn::make('ended_at')
                ->label('結束日期'),

            ExportColumn::make('tags')
                ->label('標籤')
                ->formatStateUsing(function ($state, Product $record) {
                    $defaultLang = ServiceLanguage::getDefaultLanguage();
                    return $record->tags()
                        ->with(['translations' => function ($query) use ($defaultLang) {
                            $query->where('lang', $defaultLang);
                        }])
                        ->get()
                        ->map(function ($tag) {
                            return $tag->translations->first()?->title;
                        })
                        ->filter()
                        ->implode(', ');
                }),
        ];

        // 為每個語言添加翻譯欄位
        foreach ($languages as $lang) {
            // 標題欄位
            $columns[] = ExportColumn::make("translations.title_{$lang}")
                ->label("產品名稱 ({$lang})")
                ->formatStateUsing(function ($state, Product $record) use ($lang) {
                    return $record->translations()
                        ->where('lang', $lang)
                        ->first()?->title;
                });

            // 內容欄位 1
            $columns[] = ExportColumn::make("translations.content_1_{$lang}")
                ->label("內容一 ({$lang})")
                ->formatStateUsing(function ($state, Product $record) use ($lang) {
                    return $record->translations()
                        ->where('lang', $lang)
                        ->first()?->content_1;
                });

            // 內容欄位 2
            $columns[] = ExportColumn::make("translations.content_2_{$lang}")
                ->label("內容二 ({$lang})")
                ->formatStateUsing(function ($state, Product $record) use ($lang) {
                    return $record->translations()
                        ->where('lang', $lang)
                        ->first()?->content_2;
                });

            // 內容欄位 3
            $columns[] = ExportColumn::make("translations.content_3_{$lang}")
                ->label("內容三 ({$lang})")
                ->formatStateUsing(function ($state, Product $record) use ($lang) {
                    return $record->translations()
                        ->where('lang', $lang)
                        ->first()?->content_3;
                });

            // 標籤欄位
            $columns[] = ExportColumn::make("tags_{$lang}")
                ->label("標籤 ({$lang})")
                ->formatStateUsing(function ($state, Product $record) use ($lang) {
                    return $record->tags()
                        ->with(['translations' => function ($query) use ($lang) {
                            $query->where('lang', $lang);
                        }])
                        ->get()
                        ->map(function ($tag) {
                            return $tag->translations->first()?->title;
                        })
                        ->filter()
                        ->implode(', ');
                });
        }

        return $columns;
    }

    public static function getCompletedNotificationBody(Export $export): string
    {
        $body = '您的產品匯出已完成，';
        $body .= number_format($export->successful_rows) . ' 筆資料匯出成功。';

        if ($failedRowsCount = $export->getFailedRowsCount()) {
            $body .= ' ' . number_format($failedRowsCount) . ' 筆資料匯出失敗。';
        }

        return $body;
    }
}
