<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Exports;

use Stephenchenorg\BaseFilamentPlugin\Enum\EnumProductType;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductCategory;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceLanguage;
use Filament\Actions\Exports\ExportColumn;
use Filament\Actions\Exports\Exporter;
use Filament\Actions\Exports\Models\Export;

class ProductCategoryExporter extends Exporter
{
    protected static ?string $model = ProductCategory::class;

    public static function getColumns(): array
    {
        $languages = ServiceLanguage::getLanguages();
        $columns = [
            ExportColumn::make('type')
                ->label('產品種類')
                ->formatStateUsing(fn($state) => collect(EnumProductType::getOptions())
                    ->firstWhere('value', $state)?->getLabel()),

            ExportColumn::make('parent.translations.title')
                ->label('父類別')
                ->formatStateUsing(function ($state, ProductCategory $record) {
                    return $record->parent?->translations()
                        ->where('lang', ServiceLanguage::getDefaultLanguage())
                        ->first()?->title;
                }),

            ExportColumn::make('is_hottest')
                ->label('是否熱門')
                ->formatStateUsing(fn($state) => $state ? '是' : '否'),

            ExportColumn::make('is_newest')
                ->label('是否最新')
                ->formatStateUsing(fn($state) => $state ? '是' : '否'),

            ExportColumn::make('status')
                ->label('狀態')
                ->formatStateUsing(fn($state) => $state ? '啟用' : '停用'),

            ExportColumn::make('sort')
                ->label('排序'),
        ];

        // 為每個語言添加標題欄位
        foreach ($languages as $lang) {
            $columns[] = ExportColumn::make("translations.title_{$lang}")
                ->label("標題 ({$lang})")
                ->formatStateUsing(function ($state, ProductCategory $record) use ($lang) {
                    return $record->translations()
                        ->where('lang', $lang)
                        ->first()?->title;
                });
        }

        return $columns;
    }

    public static function getCompletedNotificationBody(Export $export): string
    {
        $body = '您的產品類別匯出已完成，';
        $body .= number_format($export->successful_rows) . ' 筆資料匯出成功。';

        if ($failedRowsCount = $export->getFailedRowsCount()) {
            $body .= ' ' . number_format($failedRowsCount) . ' 筆資料匯出失敗。';
        }

        return $body;
    }
}
