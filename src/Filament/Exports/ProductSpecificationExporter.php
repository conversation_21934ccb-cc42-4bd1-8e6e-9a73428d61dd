<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Exports;

use Stephenchenorg\BaseFilamentPlugin\Enum\EnumProductType;
use <PERSON><PERSON>org\BaseFilamentPlugin\Models\ProductAttributeItem;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductSpecification;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceLanguage;
use Filament\Actions\Exports\ExportColumn;
use Filament\Actions\Exports\Exporter;
use Filament\Actions\Exports\Models\Export;
use Illuminate\Database\Eloquent\Builder;

class ProductSpecificationExporter extends Exporter
{
    protected static ?string $model = ProductSpecification::class;

    public static function getColumns(): array
    {
        $languages = ServiceLanguage::getLanguages();
        $columns = [

            ExportColumn::make('type')
                ->formatStateUsing(function($state){
                    return EnumProductType::tryFrom($state)?->getLabel() ?? EnumProductType::Product->getLabel();
                })
                ->label('規格類型'),

            ExportColumn::make('product.part_number')
                ->label('產品型號'),

            ExportColumn::make('product.translations.title')
                ->label('產品名稱')
                ->formatStateUsing(function ($state, ProductSpecification $record) {
                    return $record->product?->translations()
                        ->where('lang', ServiceLanguage::getDefaultLanguage())
                        ->first()?->title;
                }),

            ExportColumn::make('listing_price')
                ->label('建議售價'),


            ExportColumn::make('sku')
                ->label('SKU'),

            ExportColumn::make('ean')
                ->label('EAN'),

            ExportColumn::make('status')
                ->label('狀態')
                ->formatStateUsing(fn($state) => $state ? '啟用' : '停用'),

            ExportColumn::make('sort')
                ->label('排序'),
        ];

        // 為每個語言添加翻譯欄位
        foreach ($languages as $lang) {
            // 標題欄位
            $columns[] = ExportColumn::make("translations.title_{$lang}")
                ->label("規格名稱 ({$lang})")
                ->formatStateUsing(function ($state, ProductSpecification $record) use ($lang) {
                    return $record->translations()
                        ->where('lang', $lang)
                        ->first()?->title;
                });

            // 屬性欄位
            $columns[] = ExportColumn::make("attr1_{$lang}")
                ->label("屬性1 ({$lang})")
                ->formatStateUsing(function ($state, ProductSpecification $record) use ($lang) {
                    // 從 combination_key 獲取屬性項目 ID
                    if (!$record->combination_key) return null;

                    $itemIds = explode('-', $record->combination_key);
                    if (empty($itemIds)) return null;


                    // 獲取第一個屬性項目
                    $attributeItem = ProductAttributeItem::query()->find($itemIds[0] ?? null);
                    if (!$attributeItem) return null;


                    // 獲取屬性
                    $attribute = $attributeItem->attribute;
                    if (!$attribute) return null;

                    // 獲取屬性和屬性項目的翻譯
                    $attrTitle = $attribute->translations()
                        ->where('lang', $lang)
                        ->first()?->title;

                    $itemTitle = $attributeItem->translations()
                        ->where('lang', $lang)
                        ->first()?->title;

                    if ($attrTitle && $itemTitle) {
                        return "{$attrTitle}:{$itemTitle}";
                    }

                    return null;
                });

            $columns[] = ExportColumn::make("attr2_{$lang}")
                ->label("屬性2 ({$lang})")
                ->formatStateUsing(function ($state, ProductSpecification $record) use ($lang) {
                    // 從 combination_key 獲取屬性項目 ID
                    if (!$record->combination_key) return null;

                    $itemIds = explode('-', $record->combination_key);
                    if (count($itemIds) < 2) return null;

                    // 獲取第二個屬性項目
                    $attributeItem = ProductAttributeItem::query()->find($itemIds[1] ?? null);
                    if (!$attributeItem) return null;

                    // 獲取屬性
                    $attribute = $attributeItem->attribute;
                    if (!$attribute) return null;

                    // 獲取屬性和屬性項目的翻譯
                    $attrTitle = $attribute->translations()
                        ->where('lang', $lang)
                        ->first()?->title;

                    $itemTitle = $attributeItem->translations()
                        ->where('lang', $lang)
                        ->first()?->title;

                    if ($attrTitle && $itemTitle) {
                        return "{$attrTitle}:{$itemTitle}";
                    }

                    return null;
                });

            $columns[] = ExportColumn::make("attr3_{$lang}")
                ->label("屬性3 ({$lang})")
                ->formatStateUsing(function ($state, ProductSpecification $record) use ($lang) {
                    // 從 combination_key 獲取屬性項目 ID
                    if (!$record->combination_key) return null;

                    $itemIds = explode('-', $record->combination_key);
                    if (count($itemIds) < 3) return null;

                    // 獲取第三個屬性項目
                    $attributeItem = ProductAttributeItem::query()->find($itemIds[2] ?? null);
                    if (!$attributeItem) return null;

                    // 獲取屬性
                    $attribute = $attributeItem->attribute;
                    if (!$attribute) return null;

                    // 獲取屬性和屬性項目的翻譯
                    $attrTitle = $attribute->translations()
                        ->where('lang', $lang)
                        ->first()?->title;

                    $itemTitle = $attributeItem->translations()
                        ->where('lang', $lang)
                        ->first()?->title;

                    if ($attrTitle && $itemTitle) {
                        return "{$attrTitle}:{$itemTitle}";
                    }

                    return null;
                });
        }

        $columns =  array_merge($columns,[
            ExportColumn::make('selling_price')
                ->label('銷售價格'),

            ExportColumn::make('inventory')
                ->label('庫存數量'),
        ]);

        return $columns;
    }

    public static function getCompletedNotificationBody(Export $export): string
    {
        $body = '您的產品規格匯出已完成，';
        $body .= number_format($export->successful_rows) . ' 筆資料匯出成功。';

        if ($failedRowsCount = $export->getFailedRowsCount()) {
            $body .= ' ' . number_format($failedRowsCount) . ' 筆資料匯出失敗。';
        }

        return $body;
    }

    public static function modifyQuery(Builder $query): Builder
    {
        return ProductSpecification::query();
    }


}
