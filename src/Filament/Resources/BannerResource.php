<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Resources;

use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\BannerResource\Pages;
use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\BannerResource\RelationManagers;
use Stephenchenorg\BaseFilamentPlugin\Models\Banner;
use Stephenchenorg\BaseFilamentPlugin\Traits\CCTraitColumn;
use Stephenchenorg\BaseFilamentPlugin\Traits\CCTraitFormContent;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\HtmlString;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitAction;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormAdmin;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormImage;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormSort;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormTimestamps;

class BannerResource extends Resource
{
    use CCTraitColumn;
    use CCTraitFormAdmin;
    use CCTraitFormTimestamps;
    use CCTraitFormImage;
    use CCTraitFormContent;
    use CCTraitFormSort;
    use CCTraitAction;

    protected static ?string $model = Banner::class;

    protected static ?string $navigationIcon = 'heroicon-o-photo';

    protected static ?string $navigationGroup = '橫幅廣告管理';

    protected static ?string $label = '橫幅廣告';

    protected static ?int $navigationSort = 90;

//    public static function getNavigationBadge(): ?string
//    {
//        $count = CountService::getBannerCount();
//        $unit = CountService::getUnit('banner');
//
//        return "$count $unit";
//    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('basic_settings')
                    ->heading('基本設定')
                    ->schema([
                        self::getFormTitle()
                            ->nullable(),
                        self::getFormToggle('status')
                            ->label('啟用/停用'),
                        self::getFormSort(),
                    ]),
                self::getFormSectionImage(),

                Section::make('cta')
                    ->heading('CTA 相關')
                    ->schema([
                        TextInput::make('cta_title')->label('CTA 標題')
                            ->helperText(new HtmlString('請輸入 <span class="text-lg font-bold text-primary-500 dark:text-primary-100">30</span> 字以內'))
                            ->maxLength(30),

                        TextInput::make('cta_link')->label('CTA 連結')->url(),
                    ]),

                self::getFormTimestamps(),
                self::getFormSectionAdminId(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                self::getColumnTextTitle(),
                self::getColumnTextInputSort(),
                self::getColumnImage('image', '圖片'),
                self::getColumnTextStatus(),
//                TextColumn::make('cta_title')
//                    ->label('CTA 標題')
//                    ->limit(50),
//                self::getColumnTextCopyable('cta_link')
//                    ->label('CTA連結')
//                    ->icon('heroicon-o-clipboard')
            ])
            ->paginated([5, 10, 25, 50, 100, 'all'])
            ->defaultPaginationPageOption(10)
            ->filters([
                //
            ])
            ->actions([
                self::getActionEdit(),
                self::getActionView(),
                self::getActionDelete(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListBanners::route('/'),
            'create' => Pages\CreateBanner::route('/create'),
            'edit' => Pages\EditBanner::route('/{record}/edit'),
            'view' => Pages\ViewBanner::route('/{record}'),
        ];
    }

    public static function canAccess(): bool
    {
        return config('cs.banner_visible') && Gate::allows('view-any', Banner::class);
    }

}
