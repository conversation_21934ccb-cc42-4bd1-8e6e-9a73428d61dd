<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Resources\CustomerResource\Pages;

use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\CustomerResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitRedirectToIndex;

class CreateCustomer extends CreateRecord
{
    use CCTraitRedirectToIndex;

    /**
     * @var string
     */
    protected static string $resource = CustomerResource::class;

    /**
     * @return string
     */
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
