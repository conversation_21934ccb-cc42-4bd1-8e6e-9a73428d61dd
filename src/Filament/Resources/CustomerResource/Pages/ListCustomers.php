<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Resources\CustomerResource\Pages;

use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\CustomerResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListCustomers extends ListRecords
{
    protected static string $resource = CustomerResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
