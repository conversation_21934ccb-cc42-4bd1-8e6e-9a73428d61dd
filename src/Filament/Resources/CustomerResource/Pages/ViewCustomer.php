<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Resources\CustomerResource\Pages;

use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\CustomerResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewCustomer extends ViewRecord
{
    protected static string $resource = CustomerResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }
}
