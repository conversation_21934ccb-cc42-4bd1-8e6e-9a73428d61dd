<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ArticleCategoryResource\Pages;

use <PERSON><PERSON>org\BaseFilamentPlugin\Filament\Resources\ArticleCategoryResource;
use Stephenchenorg\BaseFilamentPlugin\Service\ResourceService;
use Filament\Resources\Pages\ViewRecord;

class ViewArticleCategory extends ViewRecord
{
    protected static string $resource = ArticleCategoryResource::class;

    protected function getActions(): array
    {
        return [
            ResourceService::getRedirectAction($this->getRedirectUrl()),
        ];
    }


    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

}
