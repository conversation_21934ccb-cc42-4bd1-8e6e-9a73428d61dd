<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ArticleCategoryResource\Pages;

use <PERSON><PERSON>org\BaseFilamentPlugin\Filament\Resources\ArticleCategoryResource;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitRedirectToIndex;
use Filament\Resources\Pages\CreateRecord;

class CreateArticleCategory extends CreateRecord
{
    use CCTraitRedirectToIndex;

    protected static string $resource = ArticleCategoryResource::class;

    /**
     * @return string
     */
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
