<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ProductCategoryResource\Pages;

use Stephenchenorg\BaseFilamentPlugin\Enum\EnumProductType;
use Stephenchenorg\BaseFilamentPlugin\Filament\Exports\ProductCategoryExporter;
use Stephenchenorg\BaseFilamentPlugin\Filament\Imports\ProductCategoryImporter;
use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ProductCategoryResource;
use Filament\Actions\Action;
use Filament\Actions\ExportAction;
use Filament\Actions\ImportAction;
use Filament\Resources\Concerns\HasTabs;
use Filament\Resources\Components\Tab;
use Filament\Resources\Pages\Page;

class ListProductCategories extends Page
{
    use HasTabs;

    protected static string $resource = ProductCategoryResource::class;

    protected static string $view = 'base-filament-plugin::filament.resources.pages.product-categories.list-records';

    protected static ?string $title = '產品類別';

    public function getTabs(): array
    {
        // Get available product types
        $productTypes = EnumProductType::getOptions();

        // If there's only one product type, don't show tabs
        if (count($productTypes) < 2) {
            return [];
        }

        $tabs = [
            'all' => Tab::make('全部')
                ->label('全部')
        ];

        // Add tabs for each product type
        foreach ($productTypes as $type) {
            $tabs[$type->value] = Tab::make($type->value)
                ->label($type->getLabel());
        }

        return $tabs;
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('create')
                ->label('新增 產品類別')
                ->action(function ($action){
                    $action->redirect(ProductCategoryResource::getUrl('create'));
                }),

            ImportAction::make('import')
                ->importer(ProductCategoryImporter::class)
                ->label('匯入')
                ->color('info')
                ->icon('heroicon-o-arrow-up-tray'),

            ExportAction::make('export')
                ->exporter(ProductCategoryExporter::class)
                ->icon('heroicon-o-arrow-down-tray')
                ->label('匯出')
                ->color('info'),
        ];
    }



    protected function resetPage(): void
    {
        $this->dispatch('refresh-product-category-table', activeTab: $this->activeTab);
    }

}
