<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ProductCategoryResource\Pages;

use Stephen<PERSON>org\BaseFilamentPlugin\Filament\Resources\ProductCategoryResource;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitRedirectToIndex;
use Filament\Resources\Pages\CreateRecord;

class CreateProductCategory extends CreateRecord
{
    use CCTraitRedirectToIndex;

    protected static string $resource = ProductCategoryResource::class;

    /**
     * @return string
     */
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
