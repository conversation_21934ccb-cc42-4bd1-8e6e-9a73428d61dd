<?php

namespace <PERSON><PERSON>org\BaseFilamentPlugin\Filament\Resources\ProductCategoryResource\Pages;

use Filament\Actions;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;
use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ProductCategoryResource;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceProductCategory;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitRedirectToIndex;

class EditProductCategory extends EditRecord
{
    use CCTraitRedirectToIndex;

    protected static string $resource = ProductCategoryResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make()
                ->before(function ($record,$action) {
                    if (ServiceProductCategory::hasProducts($record)) {
                        Notification::make()
                            ->title('此類別下有產品，無法刪除')
                            ->color('danger')
                            ->danger()
                            ->send();
                        $action->halt();
                    }
                }),
        ];
    }

    /**
     * @return string
     */
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }


}
