<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ProductCategoryResource\Pages;

use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ProductCategoryResource;
use Stephenchenorg\BaseFilamentPlugin\Service\ResourceService;
use Filament\Resources\Pages\ViewRecord;

class ViewProductCategory extends ViewRecord
{
    protected static string $resource = ProductCategoryResource::class;


    protected function getActions(): array
    {
        return [
            ResourceService::getRedirectAction($this->getRedirectUrl()),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
