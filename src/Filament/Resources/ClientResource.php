<?php

namespace <PERSON><PERSON>org\BaseFilamentPlugin\Filament\Resources;

use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ClientResource\Pages;
use Stephen<PERSON>org\BaseFilamentPlugin\Models\Client;
use Stephenchenorg\BaseFilamentPlugin\Service\PasswordService;
use Stephenchenorg\BaseFilamentPlugin\Traits\CCTraitColumn;
use Filament\Forms;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Support\Facades\Gate;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitAction;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormAdmin;
use Stephen<PERSON>org\CsCoreFilamentPlugin\Traits\CCTraitFormTimestamps;

class ClientResource extends Resource
{
    use CCTraitAction;
    use CCTraitColumn;
    use CCTraitFormAdmin;
    use CCTraitFormTimestamps;

    /**
     * @var string|null
     */
    protected static ?string $model = Client::class;

    /**
     * @var string|null
     */
    protected static ?string $navigationIcon = 'heroicon-o-building-office';

    /**
     * @var string|null
     */
    protected static ?string $navigationGroup = '會員管理';

    /**
     * @var string|null
     */
    protected static ?string $navigationLabel = 'B2B會員';

    /**
     * @var string|null
     */
    protected static ?string $label = 'B2B會員';

    protected static ?int $navigationSort = 110;

    /**
     * @return string
     */
    public static function getModelLabel(): string
    {
        return 'B2B會員';
    }

    /**
     * @return string
     */
    public static function getPluralModelLabel(): string
    {
        return 'B2B會員';
    }

    /**
     * @return string
     */
    public static function getNavigationLabel(): string
    {
        return 'B2B會員';
    }

    /**
     * @param Form $form
     * @return Form
     */
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('基本資料')
                    ->schema([
                        TextInput::make('code')
                            ->label('客戶代號')
                            ->maxLength(30)
                            ->unique(ignoreRecord: true),

                        TextInput::make('name')
                            ->label('客戶簡稱')
                            ->required()
                            ->maxLength(50)
                            ->autofocus(),

                        TextInput::make('email')
                            ->label('電子郵件')
                            ->required()
                            ->email()
                            ->maxLength(70)
                            ->unique(ignoreRecord: true),

                        (new PasswordService())->getPassword(),

                        (new PasswordService())->getPasswordConfirmation(),

                        Forms\Components\DateTimePicker::make('email_verified_at')
                            ->label('電子郵件驗證時間')
                            ->displayFormat('Y-m-d H:i:s'),
                    ]),

                Forms\Components\Section::make('發票與地址')
                    ->schema([

                        TextInput::make('invoice_title')
                            ->label('客戶發票標題')
                            ->maxLength(150),

                        TextInput::make('invoice_address')
                            ->label('發票地址')
                            ->maxLength(255),

                        TextInput::make('address')
                            ->label('客戶地址')
                            ->maxLength(255),

                    ]),

                Forms\Components\Section::make('聯絡資訊')
                    ->schema([
                        TextInput::make('phone1')
                            ->label('客戶電話1')
                            ->maxLength(20),

                        TextInput::make('phone1_extension')
                            ->label('電話1分機')
                            ->maxLength(10),

                        TextInput::make('phone2')
                            ->label('客戶電話2')
                            ->maxLength(20),

                        TextInput::make('phone2_extension')
                            ->label('電話2分機')
                            ->maxLength(10),

                        TextInput::make('fax')
                            ->label('傳真電話')
                            ->maxLength(20),

                        TextInput::make('mobile')
                            ->label('手機')
                            ->maxLength(20),

                        TextInput::make('contact_person')
                            ->label('聯絡人')
                            ->maxLength(50),
                    ]),

                self::getFormTimestamps(),
                self::getFormSectionAdminId(),
            ]);
    }

    /**
     * @param Table $table
     * @return Table
     */
    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('code')
                    ->label('客戶代號')
                    ->searchable(),

                TextColumn::make('name')
                    ->label('客戶簡稱')
                    ->searchable(),

                self::getColumnTextEmail(),

                TextColumn::make('phone1')
                    ->label('電話')
                    ->searchable(),

                TextColumn::make('mobile')
                    ->label('手機')
                    ->searchable(),

                TextColumn::make('contact_person')
                    ->label('聯絡人')
                    ->searchable(),

                TextColumn::make('vat')
                    ->label('統一編號')
                    ->searchable(),

                TextColumn::make('created_at')
                    ->label('建立時間')
                    ->dateTime('Y-m-d H:i:s'),
            ])
            ->paginated([5, 10, 25, 50, 100])
            ->defaultPaginationPageOption(10)
            ->filters([
                //
            ])
            ->actions([
                self::getActionEdit(),
                self::getActionView(),
            ]);
    }

    /**
     * @return array
     */
    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    /**
     * @return array
     */
    public static function getPages(): array
    {
        return [
            'index' => Pages\ListClients::route('/'),
            'create' => Pages\CreateClient::route('/create'),
            'edit' => Pages\EditClient::route('/{record}/edit'),
            'view' => Pages\ViewClient::route('/{record}'),
        ];
    }

    public static function canAccess(): bool
    {
        return config('cs.client_visible') && Gate::allows('view-any', Client::class);
    }
}
