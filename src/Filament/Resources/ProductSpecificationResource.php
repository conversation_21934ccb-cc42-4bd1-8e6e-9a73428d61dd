<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Resources;

use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\HtmlString;
use Illuminate\Validation\ValidationException;
use Stephenchenorg\BaseFilamentPlugin\Contracts\ServiceProductSpecificationInterface;
use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ProductSpecificationResource\Pages;
use Stephenchenorg\BaseFilamentPlugin\Models\Product;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductSpecification;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceLanguage;
use Stephenchenorg\BaseFilamentPlugin\Traits\CCTraitColumn;
use Stephenchenorg\BaseFilamentPlugin\Traits\CCTraitFormContent;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitAction;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormEditor;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormSort;

class ProductSpecificationResource extends Resource
{
    use CCTraitColumn;
    use CCTraitFormContent;
    use CCTraitFormEditor;
    use CCTraitFormSort;
    use CCTraitAction;

    protected static ?string $model = ProductSpecification::class;

    protected static ?string $navigationIcon = 'heroicon-o-cog-6-tooth';

    protected static ?string $navigationGroup = '產品管理';

    protected static ?string $label = '產品規格';

    protected static ?int $navigationSort = 180;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([


                Section::make('basic_settings')
                    ->heading('基本設定')
                    ->schema([

                        Select::make('product_id')
                            ->label('所屬產品')
                            ->relationship('product', 'id')
                            ->getOptionLabelFromRecordUsing(function ($record) {
                                $name = $record->translations
                                    ->where('lang', '=', ServiceLanguage::getDefaultLanguage())
                                    ->first()
                                    ?->title;

                                return "$name #{$record->part_number}";
                            })
                            ->preload()
                            ->searchable()
                            ->columnSpanFull()
                            ->reactive()
                            ->required(),

                        self::getFormToggle('status')
                            ->label('啟用/停用'),

                        TextInput::make('sku')
                            ->label('SKU')
                            ->required()
                            ->maxLength(64),

                        TextInput::make('ean')
                            ->label(' EAN 條碼')
                            ->nullable()
                            ->maxLength(15),


                        Hidden::make('type')
                            ->default('none'),

                        self::getFormSort(),

                    ]),


                self::getAttributeForm(),


                Section::make('data_settings')
                    ->id('data_settings')
                    ->heading('數據設定')
                    ->schema([

                        TextInput::make('listing_price')
                            ->label('定價')
                            ->numeric() // 確保是數字
                            ->minValue(0)
                            ->required(),


                        TextInput::make('selling_price')
                            ->label('售價')
                            ->numeric() // 確保是數字
                            ->minValue(0)
                            ->required(),

                        TextInput::make('inventory')
                            ->label('庫存')
                            ->numeric() // 確保是數字
                            ->minValue(0)
                            ->default(0)
                            ->required(),

                    ]),

                self::getTabLanguage([
                    self::getFormTitle()->nullable(),
                    self::getFormEditor('content')
                        ->label('內容 - 圖文編輯器')
                        ->placeholder('請輸入內容')
                        ->columnSpan('full'),
                ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('sku')
            ->columns([
                TextColumn::make('product.translations.title')
                    ->label('產品名稱')
                    ->getStateUsing(function ($record) {
                        return $record->product->translations
                            ->where('lang', '=', ServiceLanguage::getDefaultLanguage())
                            ->first()
                            ?->title ?? "產品 #{$record->product_id}";
                    }),

                TextColumn::make('sku')
                    ->label('SKU'),

                TextColumn::make('inventory')
                    ->label('庫存'),

                TextColumn::make('combination_key')
                    ->label('規格組合')
                    ->formatStateUsing(function ($state, $record) {
                        $serviceProductSpecification = app(ServiceProductSpecificationInterface::class);

                        if (empty($state)) {
                            $name = '';
                        } else {
                            $name = $serviceProductSpecification->getCombinationName($state);
                        }

                        if (!$serviceProductSpecification->validateCombinationKey($state, $record->product)) {
                            $name = $name . "&nbsp;&nbsp;" . '<span style="color: red;">請更新</span>';
                        }

                        return new HtmlString($name);
                    }),

                TextColumn::make('listing_price')
                    ->label('建議售價')
                    ->abbr('B2C 官網的建議售價', asTooltip: true),

                TextColumn::make('selling_price')
                    ->label('銷售價一')
                    ->abbr('B2C 官網的銷售價', asTooltip: true),

                TextColumn::make('reservingItem.quantity')
                    ->label('預留量')
                    ->badge(),

                TextColumn::make('available_inventory')
                    ->label('可用庫存')
                    ->getStateUsing(function ($record) {
                        return $record->inventory - ($record->reservingItem->quantity ?? 0);
                    })
                    ->badge()
                    ->color(fn($state) => $state <= 0 ? 'danger' : ($state <= 5 ? 'warning' : 'success')),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('product_id')
                    ->label('產品')
                    ->relationship('product', 'id')
                    ->getOptionLabelFromRecordUsing(function ($record) {
                        return $record->translations
                            ->where('lang', '=', ServiceLanguage::getDefaultLanguage())
                            ->first()
                            ?->title ?? "產品 #{$record->id}";
                    })
                    ->searchable(),
            ])
            ->actions([
                self::getActionEdit()
                    ->using(function (Model $record, array $data, $action): Model {
                        $combinationKey = $data['combination_key'] ?? null;

                        $exist = ProductSpecification::query()
                            ->whereNot('id', '=', $record->id)
                            ->where('product_id', '=', $record->product_id)
                            ->where('combination_key', '=', $combinationKey)
                            ->exists();

                        if ($exist) {
                            Notification::make()
                                ->title('已經存在相同規格')
                                ->color('danger')
                                ->danger()
                                ->send();
                            $action->halt();
                        }

                        $record->update($data);

                        return $record;
                    }),
                self::getActionView(),
                self::getActionDelete(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('id', 'desc')
            ->paginated([5, 10, 25, 50, 100])
            ->defaultPaginationPageOption(10);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListProductSpecifications::route('/'),
            'create' => Pages\CreateProductSpecification::route('/create'),
            'view' => Pages\ViewProductSpecification::route('/{record}'),
            'edit' => Pages\EditProductSpecification::route('/{record}/edit'),
        ];
    }

    /**
     * @return Section
     */
    public static function getAttributeForm(): Section
    {


        return Section::make('attribute_settings')
            ->schema(function (Get $get) {

                $form = [];

                $product = Product::query()->find($get('product_id'));

                if (empty($product)) return $form;

                foreach ($product->attributes as $attribute) {

                    $attributeTitle = $attribute->translations()
                        ->where('lang', '=', ServiceLanguage::getDefaultLanguage())
                        ->first()->title;

                    $options = $attribute->items->mapWithKeys(function ($item) {
                        $optionTitle = $item->translations()->where('lang', '=', ServiceLanguage::getDefaultLanguage())->first()->title;
                        return [$item->id => $optionTitle];
                    })->toArray();

                    $form[] = Select::make(self::getAttributeKey($attribute->id))
                        ->label($attributeTitle)
                        ->options($options)
                        ->required()
                        ->formatStateUsing(function (Get $get) use ($options) {
                            return self::getSelectedOption($options, $get);
                        })
                        ->dehydrated(false);
                }

                $form[] = Hidden::make('combination_key')
                    ->dehydrateStateUsing(function (Get $get) use ($product) {
                        return self::getCombinationKey($product, $get);
                    });

                return $form;
            })
            ->visible(function (Get $get) {
                return !empty(Product::query()->find($get('product_id'))?->attributes);
            })
            ->dehydrated()
            ->heading('規格屬性設定');

    }


    /**
     * @param Get $get
     * @return String|null
     * @throws ValidationException
     */
    public static function getCombinationKey(Product $product, Get $get): ?string
    {

        if ($product->attributes->isEmpty()) return null;

        $attributeIds = $product->attributes->map(function ($attribute) {
            return $attribute->id;
        })->toArray();

        $selectedItemIds = array_map(function ($attributeId) use ($get) {
            return $get(self::getAttributeKey($attributeId));
        }, $attributeIds);

        sort($selectedItemIds);

        $combinationKey = implode("-", $selectedItemIds);

        $exist = ProductSpecification::query()
            ->where('combination_key', $combinationKey)
            ->whereNot('id', '=', $get('id') ?? null)
            ->exists();

        if ($exist) {

            Notification::make()
                ->title('規格組合已存在')
                ->body('請更新規格屬性的選單')
                ->danger()
                ->send();

            throw ValidationException::withMessages([
                'combination_key' => '該規格組合已經存在',
            ]);
        }

        return implode("-", $selectedItemIds);
    }

    /**
     * @param int $id
     * @return string
     */
    public static function getAttributeKey(int $id): string
    {
        return "attribute_{$id}";
    }

    /**
     * 根據 combination_key 還原 Select 的選項
     *
     * @param array $options
     * @param Get $get
     * @return int|null
     */
    public static function getSelectedOption(array $options, Get $get): ?int
    {
        $combinationKey = $get('combination_key');

        if (!$combinationKey) return null;

        $selectedItemIds = explode("-", $combinationKey);

        $optionIds = array_keys($options);

        $selectedOptionId = array_intersect($selectedItemIds, $optionIds);

        if (empty($selectedOptionId)) return null;

        return array_shift($selectedOptionId);
    }
}
