<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Resources;

use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\CustomerResource\Pages;
use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\CustomerResource\RelationManagers;
use Stephen<PERSON>org\BaseFilamentPlugin\Models\Customer;
use Stephenchenorg\BaseFilamentPlugin\Service\PasswordService;
use Stephenchenorg\BaseFilamentPlugin\Traits\CCTraitColumn;
use Filament\Forms;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Support\Facades\Gate;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitAction;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormAdmin;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormTimestamps;

class CustomerResource extends Resource
{
    use CCTraitAction;
    use CCTraitColumn;
    use CCTraitFormAdmin;
    use CCTraitFormTimestamps;

    /**
     * @var string|null
     */
    protected static ?string $model = Customer::class;

    /**
     * @var string|null
     */
    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    /**
     * @var string|null
     */
    protected static ?string $navigationGroup = '會員管理';
    /**
     * @var string|null
     */
    protected static ?string $navigationLabel = 'B2C會員';

    /**
     * @var string|null
     */
    protected static ?string $label = 'B2C會員';

    protected static ?int $navigationSort = 120;

    /**
     * @return string
     */
    public static function getModelLabel(): string
    {
        return 'B2C會員';
    }

    /**
     * @return string
     */
    public static function getPluralModelLabel(): string
    {
        return 'B2C會員';
    }

    /**
     * @return string
     */
    public static function getNavigationLabel(): string
    {
        return 'B2C會員';
    }

    /**
     * @param Form $form
     * @return Form
     */
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('基本資料')
                    ->schema([
                        TextInput::make('name')
                            ->label('名稱')
                            ->required()
                            ->maxLength(50)
                            ->autofocus(),

                        TextInput::make('email')
                            ->label('電子郵件')
                            ->required()
                            ->email()
                            ->maxLength(70)
                            ->unique(ignoreRecord: true),

                        (new PasswordService())->getPassword(),

                        (new PasswordService())->getPasswordConfirmation(),

                        Forms\Components\DateTimePicker::make('email_verified_at')
                            ->label('電子郵件驗證時間')
                            ->displayFormat('Y-m-d H:i:s'),
                    ]),

                Forms\Components\Section::make('個人資訊')
                    ->schema([
                        TextInput::make('phone')
                            ->label('電話')
                            ->tel()
                            ->maxLength(20),

                        TextInput::make('address')
                            ->label('地址')
                            ->maxLength(255),
                    ]),

                self::getFormTimestamps(),
                self::getFormSectionAdminId(),
            ]);
    }

    /**
     * @param Table $table
     * @return Table
     */
    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->label('名稱')
                    ->searchable(),

                self::getColumnTextEmail(),

                TextColumn::make('phone')
                    ->label('電話')
                    ->searchable(),

                TextColumn::make('address')
                    ->label('地址')
                    ->limit(30),

                TextColumn::make('created_at')
                    ->label('建立時間')
                    ->dateTime('Y-m-d H:i:s'),
            ])
            ->filters([
                //
            ])
            ->actions([
                self::getActionEdit(),
                self::getActionView(),
            ])
            ->paginated([5, 10, 25, 50, 100])
            ->defaultPaginationPageOption(10);
    }

    /**
     * @return array
     */
    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    /**
     * @return array
     */
    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCustomers::route('/'),
            'create' => Pages\CreateCustomer::route('/create'),
            'edit' => Pages\EditCustomer::route('/{record}/edit'),
            'view' => Pages\ViewCustomer::route('/{record}'),
        ];
    }

    public static function canAccess(): bool
    {
        return config('cs.customer_visible') && Gate::allows('view-any', Customer::class);
    }
}
