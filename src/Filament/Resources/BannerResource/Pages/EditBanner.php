<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Resources\BannerResource\Pages;

use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\BannerResource;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitRedirectToIndex;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditBanner extends EditRecord
{
    use CCTraitRedirectToIndex;

    protected static string $resource = BannerResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }

    /**
     * @return string
     */
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
