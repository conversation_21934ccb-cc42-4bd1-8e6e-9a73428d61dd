<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Resources\BannerResource\Pages;

use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\BannerResource;
use Stephenchenorg\BaseFilamentPlugin\Service\ResourceService;
use Filament\Resources\Pages\ViewRecord;

class ViewBanner extends ViewRecord
{
    protected static string $resource = BannerResource::class;

    protected function getActions(): array
    {
        return [
            ResourceService::getRedirectAction($this->getRedirectUrl()),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
