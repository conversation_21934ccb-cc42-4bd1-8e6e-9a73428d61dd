<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Resources\BannerResource\Pages;

use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\BannerResource;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitRedirectToIndex;
use Filament\Resources\Pages\CreateRecord;

class CreateBanner extends CreateRecord
{
    use CCTraitRedirectToIndex;

    /**
     * @var string
     */
    protected static string $resource = BannerResource::class;

    /**
     * @return string
     */
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
