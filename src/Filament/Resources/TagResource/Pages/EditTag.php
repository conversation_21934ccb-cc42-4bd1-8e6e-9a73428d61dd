<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Resources\TagResource\Pages;

use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\TagResource;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitRedirectToIndex;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditTag extends EditRecord
{
    use CCTraitRedirectToIndex;

    protected static string $resource = TagResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }

    /**
     * @return string
     */
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }


}
