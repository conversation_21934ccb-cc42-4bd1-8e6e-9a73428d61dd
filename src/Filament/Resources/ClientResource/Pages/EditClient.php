<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ClientResource\Pages;

use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ClientResource;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitRedirectToIndex;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditClient extends EditRecord
{
    use CCTraitRedirectToIndex;


    protected static string $resource = ClientResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }

    /**
     * @return string
     */
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
