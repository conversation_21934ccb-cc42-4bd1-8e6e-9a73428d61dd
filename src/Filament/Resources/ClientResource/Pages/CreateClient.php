<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ClientResource\Pages;

use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ClientResource;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitRedirectToIndex;
use Filament\Resources\Pages\CreateRecord;

class CreateClient extends CreateRecord
{
    use CCTraitRedirectToIndex;

    protected static string $resource = ClientResource::class;

    /**
     * @return string
     */
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
