<?php

namespace <PERSON>org\BaseFilamentPlugin\Filament\Resources;

use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\TagResource\Pages;
use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\TagResource\RelationManagers;
use Stephen<PERSON>org\BaseFilamentPlugin\Models\Tag;
use Stephenchenorg\BaseFilamentPlugin\Service\ResourceService;
use Stephenchenorg\BaseFilamentPlugin\Service\TagService;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Support\Facades\Gate;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitAction;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormAdmin;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormTimestamps;

class TagResource extends Resource
{

    use CCTraitFormAdmin;
    use CCTraitFormTimestamps;
    use CCTraitAction;

    protected static ?string $model = Tag::class;

    protected static ?string $navigationIcon = 'heroicon-o-tag';

    protected static ?string $navigationGroup = '標籤管理';

    protected static ?string $label = '標籤';

    protected static ?int $navigationSort = 190;

    /**
     * @throws \Exception
     */
    public static function form(Form $form): Form
    {
        return $form
            ->schema(TagService::getAllTagForm());
    }

    public static function table(Table $table): Table
    {

        return $table
            ->columns(TagService::getAllTagTable())
            ->paginated([5, 10, 25, 50, 100, 'all'])
            ->defaultPaginationPageOption(10)
            ->filters([
                //
            ])
            ->actions([
                self::getActionEdit(),
                self::getActionView(),
                self::getActionDelete(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index'  => Pages\ListTags::route('/'),
            'create' => Pages\CreateTag::route('/create'),
            'edit'   => Pages\EditTag::route('/{record}/edit'),
        ];
    }

    public static function canAccess(): bool
    {
        return (config('cs.article_visible') || config('cs.product_visible')) && Gate::allows('view-any', Tag::class);
    }

}
