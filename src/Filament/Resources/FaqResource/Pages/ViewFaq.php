<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Resources\FaqResource\Pages;

use Stephen<PERSON>org\BaseFilamentPlugin\Filament\Resources\FaqResource;
use Stephenchenorg\BaseFilamentPlugin\Service\ResourceService;
use Filament\Resources\Pages\ViewRecord;

class ViewFaq extends ViewRecord
{
    protected static string $resource = FaqResource::class;

    protected function getActions(): array
    {
        return [
            ResourceService::getRedirectAction($this->getRedirectUrl()),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

}
