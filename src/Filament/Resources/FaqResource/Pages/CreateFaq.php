<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Resources\FaqResource\Pages;

use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\FaqResource;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitRedirectToIndex;
use Filament\Resources\Pages\CreateRecord;

class CreateFaq extends CreateRecord
{
    use CCTraitRedirectToIndex;

    protected static string $resource = FaqResource::class;

    /**
     * @return string
     */
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
