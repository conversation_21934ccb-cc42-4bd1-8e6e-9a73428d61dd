<?php

namespace Stephen<PERSON>org\BaseFilamentPlugin\Filament\Resources;

use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\RoleResource\Pages\CreateRole;
use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\RoleResource\Pages\EditRole;
use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\RoleResource\Pages\ListRoles;
use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\RoleResource\Pages\ViewRole;
use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\RoleResource\RelationManager\PermissionRelationManager;
use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\RoleResource\RelationManager\UserRelationManager;
use Filament\Facades\Filament;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Support\Enums\IconSize;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Validation\Rules\Unique;
use Spatie\Permission\Models\Role;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormAdmin;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormTimestamps;

class RoleResource extends Resource
{
    use CCTraitFormAdmin;
    use CCTraitFormTimestamps;

    protected static ?int $navigationSort = 170;

    public static function isScopedToTenant(): bool
    {
        return config('filament-spatie-roles-permissions.scope_to_tenant', true);
    }

    public static function getNavigationIcon(): ?string
    {
        return config('filament-spatie-roles-permissions.icons.role_navigation');
    }

    public static function shouldRegisterNavigation(): bool
    {
        return config('filament-spatie-roles-permissions.should_register_on_navigation.roles', true);
    }

    public static function getModel(): string
    {
        return config('permission.models.role', Role::class);
    }

    public static function getLabel(): string
    {
        return __('filament-spatie-roles-permissions::filament-spatie.section.role');
    }

    public static function getNavigationGroup(): ?string
    {
        return __(config('filament-spatie-roles-permissions.navigation_section_group',
            'filament-spatie-roles-permissions::filament-spatie.section.roles_and_permissions'));
    }

    public static function getNavigationSort(): ?int
    {
        return config('filament-spatie-roles-permissions.sort.role_navigation');
    }

    public static function getPluralLabel(): string
    {
        return __('filament-spatie-roles-permissions::filament-spatie.section.roles');
    }

    public static function getCluster(): ?string
    {
        return config('filament-spatie-roles-permissions.clusters.roles', null);
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make()
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextInput::make('name')
                                    ->label('系統名稱')
                                    ->helperText('唯一值，不能重複，請使用英文')
                                    ->required()
                                    ->unique(ignoreRecord: true, modifyRuleUsing: function (Unique $rule)
                                    {
                                        // If using teams and Tenancy, ensure uniqueness against current tenant
                                        if (config('permission.teams', false) && Filament::hasTenancy()) {
                                            // Check uniqueness against current user/team
                                            $rule->where(config('permission.column_names.team_foreign_key', 'team_id'),
                                                Filament::getTenant()->id);
                                        }
                                        return $rule;
                                    }),

                                TextInput::make('display_name')
                                    ->label('備註')
                                    ->rule('max:100')
                                    ->required(),

                                //                                Select::make('guard_name')
                                //                                    ->label(__('filament-spatie-roles-permissions::filament-spatie.field.guard_name'))
                                //                                    ->options(config('filament-spatie-roles-permissions.guard_names'))
                                //                                    ->default(config('filament-spatie-roles-permissions.default_guard_name'))
                                //                                    ->visible(fn() => config('filament-spatie-roles-permissions.should_show_guard',
                                //                                        true))
                                //                                    ->required(),

                                Select::make('permissions')
                                    ->columnSpanFull()
                                    ->multiple()
                                    ->label(__('filament-spatie-roles-permissions::filament-spatie.field.permissions'))
                                    ->relationship(
                                        name: 'permissions',
//                                        modifyQueryUsing: fn(Builder $query) => $query->orderBy('display_name')
                                    )
                                    ->getOptionLabelFromRecordUsing(fn(Model $record,
                                    ) => "$record->display_name")
                                    ->searchable(['display_name'])
                                    ->preload(config('filament-spatie-roles-permissions.preload_permissions')),

                            ]),
                    ]),


                self::getFormTimestamps(),
                self::getFormSectionAdminId(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(function ($query)
            {
                $query->where('name', '!=', 'Super Admin');
            })
            ->selectCurrentPageOnly()
            ->columns([
                TextColumn::make('id')
                    ->label('ID')
                    ->searchable(),
                TextColumn::make('name')
                    ->label('系統名稱')
                    ->searchable(),
                TextColumn::make('display_name')
                    ->label('備註')
                    ->searchable(),
                TextColumn::make('permissions_count')
                    ->counts('permissions')
                    ->label('擁有權限數量')
                    ->toggleable(isToggledHiddenByDefault: config('filament-spatie-roles-permissions.toggleable_guard_names.roles.isToggledHiddenByDefault',
                        true)),
            ])
            ->paginated([5, 10, 25, 50, 100, 'all'])
            ->defaultPaginationPageOption(10)
            ->filters([

            ])
            ->actions([
                Tables\Actions\ReplicateAction::make()
                    ->iconSize(IconSize::Large)
                    ->excludeAttributes(['permissions_count'])
                    ->label(' ')
                    ->before(function (Model $record): void
                    {
                        // Load permissions to replicate
                        $record->load('permissions');
                    })
                    ->beforeReplicaSaved(function (Model $replica): void
                    {
                        // Append timestamp to name to make it unique
                        $name = $replica['name'];
                        $timestamp = now()->timestamp;
                        $replica['name'] = "$name-copy-$timestamp";
                    })
                    ->after(function (Model $replica): void
                    {
                        $replica->permissions()->sync($replica->permissions->pluck('id'));
                    }),

                Tables\Actions\EditAction::make()
                    ->iconSize(IconSize::Large)
                    ->label(' '),
                Tables\Actions\ViewAction::make()
                    ->iconSize(IconSize::Large)
                    ->label(' '),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->emptyStateActions([
                Tables\Actions\CreateAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            PermissionRelationManager::class,
            UserRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index'  => ListRoles::route('/'),
            'create' => CreateRole::route('/create'),
            'edit'   => EditRole::route('/{record}/edit'),
            'view'   => ViewRole::route('/{record}'),
        ];
    }
}
