<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Resources;

use Stephenchenorg\BaseFilamentPlugin\Enum\EnumContactStatus;
use Stephenchenorg\BaseFilamentPlugin\Enum\EnumContactType;
use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ContactResource\Pages;
use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ContactResource\RelationManagers;
use Stephenchenorg\BaseFilamentPlugin\Models\Contact;
use Stephenchenorg\BaseFilamentPlugin\Traits\CCTraitColumn;
use Stephenchenorg\BaseFilamentPlugin\Traits\CCTraitFormContent;
use Exception;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Support\Enums\ActionSize;
use Filament\Support\Facades\FilamentIcon;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Support\Facades\Gate;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitAction;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormAdmin;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormEditor;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormImage;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormTimestamps;

class ContactResource extends Resource
{
    use CCTraitColumn;
    use CCTraitFormAdmin;
    use CCTraitFormTimestamps;
    use CCTraitFormContent;
    use CCTraitFormEditor;
    use CCTraitFormImage;
    use CCTraitAction;

    protected static ?string $model = Contact::class;

    protected static ?string $navigationIcon = 'heroicon-o-user-circle';

    protected static ?string $navigationGroup = '聯絡我們管理';

    protected static ?string $label = '聯絡我們';

    protected static ?int $navigationSort = 130;


//    public static function getNavigationBadge(): ?string
//    {
//        $count = CountService::getContactUnreadCount();
//        $unit = CountService::getUnit('contact_unread');
//
//        return "$count $unit";
//    }

//    public static function getNavigationBadgeColor(): string|array|null
//    {
//        return 'warning';
//    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([

                Section::make('basic_settings')
                    ->heading('基本設定')
                    ->schema([

                        Select::make('type')
                            ->label('表單類型')
                            ->options(collect(EnumContactType::cases())->mapWithKeys(fn($case,
                            ) => [$case->value => $case->getLabel()])->toArray())
                            ->multiple(false)
                            ->required()
                            ->searchable(),

                        TextInput::make('name')
                            ->label('姓名')
                            ->maxLength(100)
                            ->placeholder('請輸入您的姓名')
                            ->required(),
                    ]),

                Section::make('contact_settings')
                    ->heading('聯絡資訊')
                    ->schema([


                        TextInput::make('phone')
                            ->label('電話')
                            ->tel()
                            ->placeholder('請輸入您的電話號碼')
                            ->required(),

                        TextInput::make('email')
                            ->label('電子信箱')
                            ->email()
                            ->placeholder('請輸入您的電子信箱')
                            ->required()
                            ->maxLength(70),

                        TextInput::make('line')
                            ->label('Line ID (可選)')
                            ->maxLength(30)
                            ->placeholder('請輸入您的 Line ID'),


                        TextInput::make('address')
                            ->label('地址 (可選)')
                            ->maxLength(200)
                            ->placeholder('請輸入您的地址')
                            ->columnSpan('full'),

                    ]),

                Section::make('content_settings')
                    ->heading('內容設定')
                    ->schema([

                        TextInput::make('title')
                            ->label('標題')
                            ->required()
                            ->maxLength(80)
                            ->placeholder('請輸入標題'),

                        self::getFormEditor('content')
                            ->label('內容 - 圖文編輯器')
                            ->placeholder('請輸入內容')
                            ->columnSpan('full')
                            ->required(),

                        Repeater::make('files')
                            ->relationship('files')
                            ->defaultItems(0)
                            ->label('附件檔案')
                            ->schema([

                                self::getFormFileUpload('file')
                                    ->label('檔案')
                                    ->acceptedFileTypes(['application/pdf', 'image/jpeg', 'image/png'])
                                    ->maxSize(5120)
                            ]),

                    ]),

                self::getFormTimestamps(),
                self::getFormSectionAdminId(),


            ]);
    }

    /**
     * @throws Exception
     */
    public static function table(Table $table): Table
    {
        return $table
            ->columns([

                TextColumn::make('type')
                    ->label('表單類型')
                    ->badge()
                    ->searchable()->formatStateUsing(function ($state) {
                        return $state->getLabel();
                    }),

                BadgeColumn::make('status')
                    ->label('狀態')
                    ->sortable()
                    ->searchable()
                    ->color(function (EnumContactStatus $state) {

                        if ($state == EnumContactStatus::UNREAD) {
                            return 'danger';
                        }
                        if ($state == EnumContactStatus::READ) {
                            return 'primary';
                        }
                        if ($state == EnumContactStatus::REPLIED) {
                            return 'success';
                        }

                        return 'primary';
                    })
                    ->formatStateUsing(function ($state) {
                        return $state->getLabel();
                    }),

                TextColumn::make('name')
                    ->label('姓名')
                    ->searchable(),

                self::getColumnTextPhone(),
                self::getColumnTextEmail(),


            ])
            ->filters([
//                Tables\Filters\TrashedFilter::make(),

                SelectFilter::make('status')
                    ->label('訊息狀態')
                    ->options(fn() => collect(EnumContactStatus::cases())
                        ->mapWithKeys(fn($case) => [$case->value => $case->getLabel()])
                        ->toArray())
                    ->query(function ($query, array $data) {
                        if (empty($data['value'])) {
                            return $query;
                        }
                        return $query->where('status', '=', $data['value']);
                    }),


            ])
            ->actions([
                Action::make('view')
                    ->size(ActionSize::Large)
                    ->label('')
                    ->color('gray')
                    ->icon(FilamentIcon::resolve('actions::view-action') ?? 'heroicon-m-eye')
                    ->disabledForm()
                    ->action(function (array $data, $record, $livewire, $action) {
                        $record->update([
                            'status' => EnumContactStatus::READ,
                        ]);
                        $livewire->resetTable();
                        redirect(self::getUrl('view', [
                            'record' => $record,
                        ]));
                    })
            ])
            ->bulkActions([
            ])
            ->paginated([5, 10, 25, 50, 100])
            ->defaultPaginationPageOption(10);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListContacts::route('/'),
            'create' => Pages\CreateContact::route('/create'),
            'edit' => Pages\EditContact::route('/{record}/edit'),
            'view' => Pages\ViewContact::route('/{record}'),
        ];
    }

//    public static function getEloquentQuery(): Builder
//    {
//        return parent::getEloquentQuery()
//            ->withoutGlobalScopes([
//                SoftDeletingScope::class,
//            ]);
//    }

    public static function canAccess(): bool
    {
        return config('cs.contact_visible') && Gate::allows('view-any', Contact::class);
    }
}
