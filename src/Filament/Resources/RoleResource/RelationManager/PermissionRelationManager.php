<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Resources\RoleResource\RelationManager;

use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables\Actions\AttachAction;
use Filament\Tables\Actions\DetachAction;
use Filament\Tables\Actions\DetachBulkAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;
use Spatie\Permission\PermissionRegistrar;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitAction;

class PermissionRelationManager extends RelationManager
{
    use CCTraitAction;

    protected static string $relationship = 'permissions';

    protected static ?string $recordTitleAttribute = 'name';

    protected static ?string $title = '權限';

    protected static ?string $label = '權限';

    /*
     * Support changing tab title by translations in RelationManager.
     */
    public static function getTitle(Model $ownerRecord, string $pageClass): string
    {
        return __('filament-spatie-roles-permissions::filament-spatie.section.permissions') ?? (string) str(static::getRelationshipName())
            ->kebab()
            ->replace('-', ' ')
            ->headline();
    }

    protected static function getModelLabel(): string
    {
        return __('filament-spatie-roles-permissions::filament-spatie.section.permission');
    }

    protected static function getPluralModelLabel(): string
    {
        return __('filament-spatie-roles-permissions::filament-spatie.section.permissions');
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('name')
                    ->label('角色名稱'),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            // Support changing table heading by translations.
            ->heading(__('filament-spatie-roles-permissions::filament-spatie.section.permissions'))
            ->columns([
                TextColumn::make('name')
                    ->searchable()
                    ->label('key'),
                TextColumn::make('display_name')
                    ->searchable()
                    ->label('顯示名稱'),
            ])
            ->filters([

            ])->headerActions([
                AttachAction::make('Attach Permission')
                    ->preloadRecordSelect()
                    ->after(
                        fn() => app()->make(PermissionRegistrar::class)
                            ->forgetCachedPermissions()
                    ),
            ])->actions([
                self::getActionDetach()->after(fn() => app()->make(PermissionRegistrar::class)->forgetCachedPermissions()),
            ])->bulkActions([
                DetachBulkAction::make()->after(fn(
                ) => app()->make(PermissionRegistrar::class)->forgetCachedPermissions()),
            ]);
    }
}
