<?php

namespace <PERSON>chenorg\BaseFilamentPlugin\Filament\Resources\RoleResource\Pages;

use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\RoleResource;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitRedirectToIndex;
use Filament\Actions\Action;
use Filament\Actions\ActionGroup;
use Filament\Actions\DeleteAction;
use Filament\Actions\ViewAction;
use Filament\Resources\Pages\EditRecord;

class EditRole extends EditRecord
{
    use CCTraitRedirectToIndex;

    /**
     * @var string
     */
    protected static string $resource = RoleResource::class;

    /**
     * @return array|Action[]|ActionGroup[]
     */
    public function getHeaderActions(): array
    {
        return [
            ViewAction::make(),
            DeleteAction::make(),
        ];
    }

    /**
     * @return string|null
     */
    protected function getRedirectUrl(): ?string
    {
        $resource = static::getResource();

        return config('filament-spatie-roles-permissions.should_redirect_to_index.roles.after_create', false)
            ? $resource::getUrl('index')
            : parent::getRedirectUrl();
    }

}
