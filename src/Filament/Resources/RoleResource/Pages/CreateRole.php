<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Resources\RoleResource\Pages;

use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\RoleResource;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitRedirectToIndex;
use Filament\Resources\Pages\CreateRecord;

class CreateRole extends CreateRecord
{
    use CCTraitRedirectToIndex;

    /**
     * @var string
     */
    protected static string $resource = RoleResource::class;

    /**
     * @return string
     */
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
