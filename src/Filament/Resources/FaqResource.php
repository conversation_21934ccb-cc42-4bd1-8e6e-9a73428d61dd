<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Resources;

use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\FaqResource\Pages;
use Stephenchenorg\BaseFilamentPlugin\Models\Faq;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceLanguage;
use Stephenchenorg\BaseFilamentPlugin\Traits\CCTraitColumn;
use Stephenchenorg\BaseFilamentPlugin\Traits\CCTraitFormContent;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Support\Facades\Gate;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitAction;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormAdmin;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormEditor;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormOG;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormSEO;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormSort;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormTimestamps;

class FaqResource extends Resource
{
    use CCTraitColumn;
    use CCTraitFormAdmin;
    use CCTraitFormTimestamps;
    use CCTraitFormContent;
    use CCTraitFormEditor;
    use CCTraitFormSort;
    use CCTraitFormSEO;
    use CCTraitFormOG;
    use CCTraitAction;

    protected static ?string $model = Faq::class;

    protected static ?string $navigationIcon = 'heroicon-o-question-mark-circle';

    protected static ?string $navigationGroup = '問與答管理';

    protected static ?string $label = '問與答';

    protected static ?int $navigationSort = 150;

//    public static function getNavigationBadge(): ?string
//    {
//        $count = CountService::getFaqCount();
//        $unit = CountService::getUnit('faq');
//
//        return "$count $unit";
//    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('basic_settings')
                    ->heading('基本設定')
                    ->schema([
                        self::getFormToggle('status')
                            ->label('啟用/停用'),

                        Select::make('faq_category_id')
                            ->label('問答類別')
                            ->relationship('category', 'key')
                            ->getOptionLabelFromRecordUsing(function($record) {
                                return $record->translations
                                    ->where('lang', '=', ServiceLanguage::getDefaultLanguage())
                                    ->first()
                                    ->title;
                            })
                            ->columnSpanFull()
                            ->required(),
                        self::getFormKey(),
                        self::getFormSort(),
                    ]),
                self::getTabLanguage([
                    self::getFormTitle(),
                    self::getFormEditor('content')
                        ->label('內容 - 圖文編輯器')
                        ->placeholder('請輸入內容')
                        ->columnSpan('full')
                        ->required(),
                    self::getFormSectionSEO(),
                    self::getFormSectionOG(),
                ]),

                self::getFormTimestamps(),
                self::getFormSectionAdminId(),
            ]);

    }

    /**
     * @throws \Exception
     */
    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                self::getColumnTranslation('title', false, 'category')
                    ->label('問與答類別'),
                self::getColumnTranslation('title', true)
                    ->label('問與答標題'),
                self::getColumnTextKey(),
                self::getColumnTextStatus(),
                self::getColumnTextInputSort(),
            ])
            ->paginated([5, 10, 25, 50, 100, 'all'])
            ->defaultPaginationPageOption(10)
            ->filters([
                SelectFilter::make('faq_category_id')
                    ->preload()
                    ->relationship('category','key')
                    ->getOptionLabelFromRecordUsing(function ($record) {
                        return $record->translations
                            ->where('lang', '=', ServiceLanguage::getDefaultLanguage())
                            ->first()->title;
                    })
                    ->label('問與答類別')
                    ->searchable(),
            ])
            ->actions([
                self::getActionEdit(),
                self::getActionView(),
                self::getActionDelete(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
        ];
    }

    public static function getPages(): array
    {
        return [
            'index'  => Pages\ListFaqs::route('/'),
            'create' => Pages\CreateFaq::route('/create'),
            'edit'   => Pages\EditFaq::route('/{record}/edit'),
            'view'   => Pages\ViewFaq::route('/{record}'),
        ];
    }

    public static function canAccess(): bool
    {
        return config('cs.faq_visible') && Gate::allows('view-any', Faq::class);
    }

}
