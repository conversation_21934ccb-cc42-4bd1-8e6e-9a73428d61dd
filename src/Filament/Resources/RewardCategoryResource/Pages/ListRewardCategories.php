<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Resources\RewardCategoryResource\Pages;

use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\RewardCategoryResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListRewardCategories extends ListRecords
{
    protected static string $resource = RewardCategoryResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
