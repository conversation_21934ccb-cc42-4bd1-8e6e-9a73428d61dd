<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Resources\RewardCategoryResource\Pages;

use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\RewardCategoryResource;
use Filament\Resources\Pages\CreateRecord;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitRedirectToIndex;

class CreateRewardCategory extends CreateRecord
{
    use CCTraitRedirectToIndex;

    protected static string $resource = RewardCategoryResource::class;
}
