<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Resources;

use Filament\Forms\Components\Hidden;
use Filament\Notifications\Notification;
use Filament\Tables\Actions\BulkAction;
use Illuminate\Database\Eloquent\Collection;
use Stephenchenorg\BaseFilamentPlugin\Enum\EnumProductType;
use Stephenchenorg\BaseFilamentPlugin\Filament\Exports\ProductCategoryExporter;
use Stephenchenorg\BaseFilamentPlugin\Filament\Exports\ProductExporter;
use Stephenchenorg\BaseFilamentPlugin\Filament\Exports\ProductSpecificationExporter;
use Stephenchenorg\BaseFilamentPlugin\Filament\Imports\ProductCategoryImporter;
use Stephenchenorg\BaseFilamentPlugin\Filament\Imports\ProductImporter;
use Stephenchenorg\BaseFilamentPlugin\Filament\Imports\ProductSpecificationImporter;
use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ProductResource\Pages;
use Stephenchenorg\BaseFilamentPlugin\Models\Product;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductCategory;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceProductCategory;
use Stephenchenorg\BaseFilamentPlugin\Service\TagService;
use Stephenchenorg\BaseFilamentPlugin\Traits\CCTraitColumn;
use Stephenchenorg\BaseFilamentPlugin\Traits\CCTraitFormContent;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\ExportAction;
use Filament\Tables\Actions\ImportAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Table;
use Illuminate\Support\Facades\Gate;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitAction;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormAdmin;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormEditor;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormImage;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormOG;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormSEO;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormSort;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormTimestamps;

class ProductResource extends Resource
{
    use CCTraitFormAdmin;
    use CCTraitFormTimestamps;
    use CCTraitFormImage;
    use CCTraitFormContent;
    use CCTraitFormEditor;
    use CCTraitFormSort;
    use CCTraitFormSEO;
    use CCTraitFormOG;
    use CCTraitColumn;
    use CCTraitAction;

    protected static ?string $model = Product::class;

    protected static ?string $navigationIcon = 'heroicon-o-cube';


    protected static ?string $navigationGroup = '產品管理';

    protected static ?string $label = '產品';

    protected static ?int $navigationSort = 170;

//    public static function getNavigationBadge(): ?string
//    {
//        $count = CountService::getProductCount();
//        $unit = CountService::getUnit('product');
//
//        return "$count $unit";
//    }

    public static function form(Form $form): Form
    {

        if(count(EnumProductType::getOptions()) > 1)
        {
            $productType = Select::make('type')
                ->label('產品種類')
                ->reactive()
                ->required()
                ->options(collect(EnumProductType::getOptions())->mapWithKeys(function ($item) {
                    return [$item->value => $item->getLabel()];
                }))
                ->default(function() {
                    $options = EnumProductType::getOptions();
                    return !empty($options) ? $options[0]->value : null;
                });
        }
        else
        {
            $productType = Hidden::make('type')
                ->default(EnumProductType::getOptions()[0]->value);
        }

        return $form
            ->schema([

                Section::make('basic_settings')
                    ->heading('基本設定')
                    ->schema([

                        $productType,

                        ServiceProductCategory::getColumnSelectTree('product_category_id', function (Get $get) {
                            return $get('type');
                        }, 'category')
                            ->required(),

                        TextInput::make('part_number')
                            ->label('產品型號')
                            ->required()
                            ->maxLength(100),

                        self::getFormToggle('status')
                            ->label('啟用/停用'),

                        self::getFormToggle('is_hottest')
                            ->label('(是/否)熱門'),

                        self::getFormToggle('is_newest')
                            ->label('(是/否)最新'),

                        self::getFormSort(),

                    ]),

                Section::make('tag_settings')
                    ->heading('標籤設定')
                    ->schema([
                        TagService::getProductTagSelectForm(),
                    ]),


                self::getFormTime(),
                self::getTabLanguage([
                    self::getFormTitle(),
                    self::getFormEditor('content_1')
                        ->label('內容1 - 圖文編輯器'),
                    self::getFormEditor('content_2')
                        ->label('內容2 - 圖文編輯器'),
                    self::getFormEditor('content_3')
                        ->label('內容3 - 圖文編輯器'),
                    self::getFormSectionSEO(),
                    self::getFormSectionOG(),
                ]),


                self::getFormTimestamps(),
                self::getFormSectionAdminId(),

            ]);

    }

    /**
     * @throws \Exception
     */
    public static function table(Table $table): Table
    {
        return $table
            ->headerActions([
                ImportAction::make('importProducts')
                    ->label('匯入 產品')
                    ->importer(ProductImporter::class)
                    ->maxRows(2000)
                    ->chunkSize(100)
                    ->icon('heroicon-o-arrow-up-tray'),

                ExportAction::make('exportProducts')
                    ->label('匯出 產品')
                    ->exporter(ProductExporter::class)
                    ->color('info')
                    ->icon('heroicon-o-arrow-down-tray'),


                ImportAction::make('importProductSpecifications')
                    ->label('匯入 產品規格')
                    ->importer(ProductSpecificationImporter::class)
                    ->maxRows(2000)
                    ->chunkSize(100)
                    ->icon('heroicon-o-arrow-up-tray'),


                ExportAction::make('exportProductsSpecifications')
                    ->label('匯出 產品規格')
                    ->exporter(ProductSpecificationExporter::class)
                    ->color('info')
                    ->icon('heroicon-o-arrow-down-tray'),
            ])
            ->defaultSort('id', 'desc')
            ->paginated([5, 10, 25, 50, 100])
            ->defaultPaginationPageOption(10)
            ->columns([

                self::getColumnTranslation('title', true, 'category')
                    ->label('產品類別'),

                self::getColumnTranslation('title', true)
                    ->icon('heroicon-o-clipboard-document-check')
                    ->iconColor('warning')
                    ->copyMessage('已複製')
                    ->copyable()
                    ->label('產品標題'),

                TextColumn::make('part_number')
                    ->copyable()
                    ->icon('heroicon-o-clipboard-document-check')
                    ->iconColor('warning')
                    ->copyMessage('已複製')
                    ->label('產品型號')
                    ->searchable(true, function ($query, $search) {
                        $query->where('part_number', '=', $search);
                    }, true)
                    ->limit(100),

                self::getColumnImage('image', '圖片')
                    ->getStateUsing(function ($record) {
                        return $record->images->first()?->image;
                    }),


                self::getColumnTextStatus(),
                self::getColumnTextIsHottest(),
                self::getColumnTextIsNewest(),

                self::getColumnTextTag()->label('產品標籤'),

                //...self::getColumnsTextPeriod(),
            ])
            ->filters([
                Filter::make('tree')
                    ->form([

                        ServiceProductCategory::getColumnSelectTree('product_category_id', relation: 'category')
                            ->required(),

                    ])
                    ->query(function ($query, array $data) {
                        return $query->when($data['product_category_id'] && $data['product_category_id'] != -1, function ($query) use ($data) {
                            $category = ProductCategory::query()->find($data['product_category_id']);
                            $ids = $category->descendantsWithSelf()->pluck('id')->toArray();
                            return $query->whereIn('product_category_id', $ids);
                        });
                    })
            ])
            ->actions([
                self::getActionEdit(),
                self::getActionView(),
                self::getActionDelete(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([

                    BulkAction::make('changeCategory')
                        ->label('改變產品類別')
                        ->icon('heroicon-o-folder')
                        ->color('warning')
                        ->form(function($form){
                            return [
                                ServiceProductCategory::getColumnSelectTree('product_category_id', relation: 'category')
                                    ->required(),
                            ];
                        })
                        ->action(function (Collection $records, array $data): void
                        {
                            $productIds = $records->pluck('id')->toArray();
                            $products = Product::query()->whereIn('id', $productIds)->get();

                            // 不能夠一口氣更新 因為我要觸發事件 讓listener處理
                            foreach ($products as $product) {
                                $product->product_category_id = $data['product_category_id'];
                                $product->save();
                            }

                            Notification::make()
                                ->title('產品類別更新成功')
                                ->body("已成功更新 {$products->count()} 個產品的類別")
                                ->success()
                                ->color('success')
                                ->send();
                        })
                        ->deselectRecordsAfterCompletion(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            ProductResource\RelationManagers\AttributesRelationManager::class,
            ProductResource\RelationManagers\SpecificationsRelationManager::class,
            ProductResource\RelationManagers\ImagesRelationManager::class,
            ProductResource\RelationManagers\TagsRelationManager::class,
            ProductResource\RelationManagers\DetailsRelationManager::class,
            ProductResource\RelationManagers\ShippingMethodsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListProducts::route('/'),
            'create' => Pages\CreateProduct::route('/create'),
            'edit' => Pages\EditProduct::route('/{record}/edit'),
            'view' => Pages\ViewProduct::route('/{record}'),
        ];
    }

    public static function canAccess(): bool
    {
        return config('cs.product_visible') && Gate::allows('view-any', Product::class);
    }

}
