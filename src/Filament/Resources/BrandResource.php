<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Resources;

use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\BrandResource\Pages;
use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\BrandResource\RelationManagers;
use Stephenchenorg\BaseFilamentPlugin\Models\Brand;
use Stephenchenorg\BaseFilamentPlugin\Service\ResourceService;
use Stephenchenorg\BaseFilamentPlugin\Traits\CCTraitColumn;
use Stephenchenorg\BaseFilamentPlugin\Traits\CCTraitFormContent;

use Filament\Forms\Components\Section;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Support\Facades\Gate;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitAction;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormAdmin;
use Stephen<PERSON>org\CsCoreFilamentPlugin\Traits\CCTraitFormImage;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormSort;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormTimestamps;

class BrandResource extends Resource
{
    use  CCTraitColumn;
    use CCTraitFormAdmin;
    use CCTraitFormTimestamps;
    use CCTraitFormImage;
    use CCTraitFormContent;
    use CCTraitFormSort;
    use CCTraitAction;

    protected static ?string $model = Brand::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $navigationGroup = '合作夥伴管理';

    protected static ?string $label = '品牌 (合作夥伴)';

    protected static ?int $navigationSort = 100;


    public static function form(Form $form): Form
    {
        return $form
            ->schema([

                Section::make('basic_settings')
                    ->heading('基本設定')
                    ->schema([
                        self::getFormKey(),
                        self::getFormSort(),
                    ]),

                Section::make('image_settings')
                    ->heading('圖片設定')
                    ->schema([

                        self::getFormImage('logo')
                            ->label('Logo')
                            ->required(),
                    ]),

                self::getTabLanguage([
                    self::getFormTitle()
                        ->required(),

                    self::getFormDescription()
                        ->required(false),
                ]),

                self::getFormTimestamps(),
                self::getFormSectionAdminId(),

            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                self::getColumnTranslation('title')
                    ->label('品牌名稱'),
                self::getColumnImage('logo','背景圖片'),
                self::getColumnTextInputSort(),
            ])
            ->paginated([5, 10, 25, 50, 100, 'all'])
            ->defaultPaginationPageOption(10)
            ->filters([
                //
            ])
            ->actions([
                self::getActionEdit(),
                self::getActionView(),
                self::getActionDelete(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index'  => Pages\ListBrands::route('/'),
            'create' => Pages\CreateBrand::route('/create'),
            'edit'   => Pages\EditBrand::route('/{record}/edit'),
            'view'   => Pages\EditBrand::route('/{record}'),
        ];
    }


    public static function canAccess(): bool
    {
        return config('cs.brand_visible') && Gate::allows('view-any', Brand::class);
    }

}
