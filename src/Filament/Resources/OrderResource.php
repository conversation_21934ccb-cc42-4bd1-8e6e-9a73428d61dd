<?php

namespace Stephen<PERSON>org\BaseFilamentPlugin\Filament\Resources;

use Filament\Forms;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Stephenchenorg\BaseFilamentPlugin\Enum\EnumInvoiceMethod;
use Stephenchenorg\BaseFilamentPlugin\Enum\EnumOrderStatus;
use Stephenchenorg\BaseFilamentPlugin\Enum\EnumPaymentMethod;
use Stephenchenorg\BaseFilamentPlugin\Enum\EnumPaymentStatus;
use Stephenchenorg\BaseFilamentPlugin\Enum\EnumShippingMethod;
use Stephenchenorg\BaseFilamentPlugin\Enum\EnumShippingStatus;
use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\OrderResource\Pages;
use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\OrderResource\RelationManagers\ItemsRelationManager;
use Stephenchenorg\BaseFilamentPlugin\Models\Order;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitAction;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitColumn;

class OrderResource extends Resource
{
    use CCTraitAction;
    use CCTraitColumn;

    protected static ?string $model = Order::class;

    protected static ?string $navigationIcon = 'heroicon-o-shopping-cart';

    protected static ?string $navigationGroup = '訂單管理';

    protected static ?string $label = '訂單管理';

    protected static ?int $navigationSort = 200;


    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('訂單資訊')
                    ->schema([
                        TextInput::make('order_key')
                            ->label('訂單編號')
                            ->required()
                            ->unique(ignoreRecord: true),
                        Select::make('orderable_type')
                            ->label('客戶類型')
                            ->options(function () {
                                $clientModel = config('auth.providers.clients.model');
                                $customerModel = config('auth.providers.customers.model');
                                return [
                                    $clientModel => 'B2B客戶',
                                    $customerModel => 'B2C客戶',
                                ];
                            })
                            ->required()
                            ->reactive(),

                        Select::make('orderable_id')
                            ->label('客戶')
                            ->options(function (callable $get) {
                                $type = $get('orderable_type');
                                if (!$type) {
                                    return [];
                                }
                                return $type::pluck('name', 'id');
                            })
                            ->required()
                            ->searchable(),

                        TextInput::make('item_amount')
                            ->label('商品總金額')
                            ->numeric()
                            ->required(),

                        TextInput::make('shipping_cost')
                            ->label('運費')
                            ->numeric()
                            ->required(),

                        TextInput::make('total_amount_untaxed')
                            ->label('未稅總金額')
                            ->numeric()
                            ->required(),

                        TextInput::make('total_amount_taxed')
                            ->label('含稅總金額')
                            ->numeric()
                            ->required(),

                        Select::make('payment_method')
                            ->label('付款方式')
                            ->options(fn() => collect(EnumPaymentMethod::cases())
                                ->mapWithKeys(fn($case) => [$case->value => $case->getLabel()])
                                ->toArray())
                            ->required(),

                        Select::make('payment_status')
                            ->label('付款狀態')
                            ->options(fn() => collect(EnumPaymentStatus::cases())
                                ->mapWithKeys(fn($case) => [$case->value => $case->getLabel()])
                                ->toArray())
                            ->required(),

                        Select::make('shipping_method')
                            ->label('運送方式')
                            ->options(fn() => collect(EnumShippingMethod::cases())
                                ->mapWithKeys(fn($case) => [$case->value => $case->getLabel()])
                                ->toArray())
                            ->required(),

                        Select::make('shipping_status')
                            ->label('配送狀態')
                            ->options(fn() => collect(EnumShippingStatus::cases())
                                ->mapWithKeys(fn($case) => [$case->value => $case->getLabel()])
                                ->toArray())
                            ->required(),

                        Select::make('status')
                            ->label('訂單狀態')
                            ->options(fn() => collect(EnumOrderStatus::cases())
                                ->mapWithKeys(fn($case) => [$case->value => $case->getLabel()])
                                ->toArray())
                            ->required(),
                    ])
                    ->columns(2),
                Section::make('收件人資訊')
                    ->schema([
                        TextInput::make('name')
                            ->label('收件人姓名')
                            ->required(),

                        TextInput::make('phone')
                            ->label('收件人電話')
                            ->required(),

                        Select::make('country_code')
                            ->label('國家代碼')
                            ->default('TWN')
                            ->required(),

                        TextInput::make('state')
                            ->label('州/省/縣')
                            ->default('台灣')
                            ->required(),

                        TextInput::make('city')
                            ->label('城市')
                            ->required(),

                        TextInput::make('district')
                            ->label('區/鄉/鎮')
                            ->required(),

                        TextInput::make('postal_code')
                            ->label('郵遞區號')
                            ->required(),

                        TextInput::make('address_line1')
                            ->label('地址行1')
                            ->required(),

                        TextInput::make('address_line2')
                            ->label('地址行2')
                            ->nullable(),

                        Select::make('store_address_id')
                            ->label('商店地址')
                            ->relationship('storeAddress', 'id')
                            ->nullable(),
                    ])
                    ->columns(2),

                Section::make('發票資訊')
                    ->schema([
                        Select::make('invoice_method')
                            ->label('發票開立方式')
                            ->options(fn() => collect(EnumInvoiceMethod::cases())
                                ->mapWithKeys(fn($case) => [$case->value => $case->getLabel()])
                                ->toArray())
                            ->required(),

                        TextInput::make('carrier_value')
                            ->label('載具號碼')
                            ->maxLength(50)
                            ->nullable()
                            ->helperText('手機條碼或自然人憑證載具時使用'),

                        TextInput::make('invoice_address')
                            ->label('發票地址')
                            ->maxLength(255)
                            ->nullable()
                            ->helperText('二聯/三聯發票時使用'),

                        TextInput::make('vat')
                            ->label('統一編號')
                            ->maxLength(20)
                            ->nullable(),

                        TextInput::make('invoice_title')
                            ->label('客戶發票標題')
                            ->maxLength(255)
                            ->nullable(),
                    ])
                    ->columns(2)
                    ->collapsible()
                    ->collapsed(),

                Section::make('付款資訊')
                    ->schema([
                        TextInput::make('bank_code')
                            ->label('繳費銀行代碼')
                            ->maxLength(3)
                            ->nullable(),

                        TextInput::make('v_account')
                            ->label('虛擬帳號')
                            ->maxLength(16)
                            ->nullable(),

                        TextInput::make('payment_no')
                            ->label('繳費代碼')
                            ->maxLength(14)
                            ->nullable()
                            ->helperText('CVS時使用，BARCODE時為空白'),

                        Forms\Components\DateTimePicker::make('expire_date')
                            ->label('繳費期限')
                            ->nullable(),

                        TextInput::make('barcode1')
                            ->label('條碼第一段號碼')
                            ->maxLength(20)
                            ->nullable()
                            ->helperText('代碼時為空白'),

                        TextInput::make('barcode2')
                            ->label('條碼第二段號碼')
                            ->maxLength(20)
                            ->nullable()
                            ->helperText('代碼時為空白'),

                        TextInput::make('barcode3')
                            ->label('條碼第三段號碼')
                            ->maxLength(20)
                            ->nullable()
                            ->helperText('代碼時為空白'),
                    ])
                    ->columns(2)
                    ->collapsible()
                    ->collapsed(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([

                self::getColumnTextCopyable('order_key')
                    ->label('訂單編號')
                    ->searchable(true, function ($query, $search) {
                        $query->where('order_key', '=', $search);
                    }, true),

                TextColumn::make('orderable_type')
                    ->label('客戶類型')
                    ->formatStateUsing(function ($state) {
                        if ($state === config('auth.providers.clients.model')) {
                            return 'B2B';
                        }
                        if ($state === config('auth.providers.customers.model')) {
                            return 'B2C';
                        }
                        return '未登入';
                    }),


                TextColumn::make('name')
                    ->label('收件人')
                    ->searchable(true, function ($query, $search) {
                        $query->where('name', 'like', "%$search%");
                    }, true),

                self::getColumnTextPhone('phone')
                    ->label('電話')
                    ->searchable(true, function ($query, $search) {
                        $query->where('phone', 'like', "%$search%");
                    }, true),

                TextColumn::make('payment_method')
                    ->label('付款方式')
                    ->formatStateUsing(function ($state) {
                        return $state ? $state->getLabel() : '-';
                    }),

                TextColumn::make('shipping_method')
                    ->label('運送方式')
                    ->formatStateUsing(function ($state) {
                        return $state ? $state->getLabel() : '-';
                    }),

                BadgeColumn::make('payment_status')
                    ->label('付款狀態')
                    ->sortable()
                    ->formatStateUsing(function ($state) {
                        $enum = EnumPaymentStatus::tryFrom($state);
                        return $enum ? $enum->getLabel() : $state;
                    })
                    ->color(function ($state) {
                        $enum = EnumPaymentStatus::tryFrom($state);
                        if (!$enum) return 'primary';
                        return match ($enum) {
                            EnumPaymentStatus::UNPAID => 'danger',
                            EnumPaymentStatus::PAID => 'success',
                            EnumPaymentStatus::FAILED => 'danger',
                            EnumPaymentStatus::REFUNDING => 'warning',
                            EnumPaymentStatus::REFUNDED => 'gray',
                            EnumPaymentStatus::REFUND_FAILED => 'danger',
                            default => 'primary',
                        };
                    }),

                BadgeColumn::make('shipping_status')
                    ->label('配送狀態')
                    ->sortable()
                    ->formatStateUsing(function ($state) {
                        $enum = EnumShippingStatus::tryFrom($state);
                        return $enum ? $enum->getLabel() : $state;
                    })
                    ->color(function ($state) {
                        $enum = EnumShippingStatus::tryFrom($state);
                        if (!$enum) return 'primary';
                        return match ($enum) {
                            EnumShippingStatus::UNSHIPPED => 'danger',
                            EnumShippingStatus::SHIPPED => 'success',
                            EnumShippingStatus::DELIVERED => 'info',
                            EnumShippingStatus::COMPLETED => 'success',
                            EnumShippingStatus::PICKUP_FAILED => 'danger',
                            EnumShippingStatus::RETURNED => 'warning',
                            default => 'primary',
                        };
                    }),

                BadgeColumn::make('status')
                    ->label('訂單狀態')
                    ->sortable()
                    ->formatStateUsing(function ($state) {
                        $enum = EnumOrderStatus::tryFrom($state);
                        return $enum ? $enum->getLabel() : $state;
                    })
                    ->color(function ($state) {
                        $enum = EnumOrderStatus::tryFrom($state);
                        if (!$enum) return 'primary';
                        return match ($enum) {
                            EnumOrderStatus::PENDING => 'warning',
                            EnumOrderStatus::CANCELED => 'danger',
                            EnumOrderStatus::COMPLETED => 'success',
                            EnumOrderStatus::FAILED => 'danger',
                            default => 'primary',
                        };
                    }),

                TextColumn::make('total_amount_untaxed')
                    ->label('未稅總金額')
                    ->money('TWD')
                    ->sortable(),

                TextColumn::make('total_amount_taxed')
                    ->label('含稅總金額')
                    ->money('TWD')
                    ->sortable(),

                TextColumn::make('created_at')
                    ->label('訂單日期')
                    ->dateTime('Y-m-d H:i:s')
                    ->sortable(),
            ])
            ->paginated([5, 10, 25, 50, 100])
            ->defaultPaginationPageOption(10)
            ->filters([
                Tables\Filters\SelectFilter::make('payment_status')
                    ->label('付款狀態')
                    ->options(fn() => collect(EnumPaymentStatus::cases())
                        ->mapWithKeys(fn($case) => [$case->value => $case->getLabel()])
                        ->toArray()),

                Tables\Filters\SelectFilter::make('shipping_status')
                    ->label('配送狀態')
                    ->options(fn() => collect(EnumShippingStatus::cases())
                        ->mapWithKeys(fn($case) => [$case->value => $case->getLabel()])
                        ->toArray()),

                Tables\Filters\SelectFilter::make('status')
                    ->label('訂單狀態')
                    ->options(fn() => collect(EnumOrderStatus::cases())
                        ->mapWithKeys(fn($case) => [$case->value => $case->getLabel()])
                        ->toArray()),

                Tables\Filters\SelectFilter::make('orderable_type')
                    ->label('訂單類型')
                    ->options(function () {
                        $clientModel = config('auth.providers.clients.model');
                        $customerModel = config('auth.providers.customers.model');
                        return [
                            $clientModel => 'B2B客戶',
                            $customerModel => 'B2C客戶',
                        ];
                    }),
            ])
            ->actions([
                ActionGroup::make([
                    Action::make('update_payment_status')
                        ->label('更新付款狀態')
                        ->icon('heroicon-m-credit-card')
                        ->form(function ($record) {
                            $currentStatus = $record->payment_status;

                            // 從 Enum 取得所有可能的狀態，然後檢查哪些可以轉換
                            $options = [];
                            foreach (EnumPaymentStatus::cases() as $status) {
                                // 檢查是否可以從當前狀態轉換到這個狀態
                                if ($record->paymentStatus()->canBe($status->value)) {
                                    $options[$status->value] = $status->getLabel();
                                }
                            }

                            return [
                                Forms\Components\Select::make('payment_status')
                                    ->label('付款狀態')
                                    ->options($options)
                                    ->default($currentStatus)
                                    ->required(),
                            ];
                        })
                        ->action(function (array $data, $record) {
                            $record->payment_status = $data['payment_status'];
                            $record->save();
                        })
                        ->visible(function ($record) {
                            // 檢查是否有可用的轉換選項
                            foreach (EnumPaymentStatus::cases() as $status) {
                                if ($record->paymentStatus()->canBe($status->value)) {
                                    return true;
                                }
                            }
                            return false;
                        }),

                    Action::make('update_shipping_status')
                        ->label('更新出貨狀態')
                        ->icon('heroicon-m-truck')
                        ->form(function ($record) {
                            $currentStatus = $record->shipping_status;

                            // 從 Enum 取得所有可能的狀態，然後檢查哪些可以轉換
                            $options = [];
                            foreach (EnumShippingStatus::cases() as $status) {
                                // 檢查是否可以從當前狀態轉換到這個狀態
                                if ($record->shippingStatus()->canBe($status->value)) {
                                    $options[$status->value] = $status->getLabel();
                                }
                            }

                            return [
                                Forms\Components\Select::make('shipping_status')
                                    ->label('出貨狀態')
                                    ->options($options)
                                    ->default($currentStatus)
                                    ->required(),
                            ];
                        })
                        ->action(function (array $data, $record) {
                            $record->shipping_status = $data['shipping_status'];
                            $record->save();
                        })
                        ->visible(function ($record) {
                            // 檢查是否有可用的轉換選項
                            foreach (EnumShippingStatus::cases() as $status) {
                                if ($record->shippingStatus()->canBe($status->value)) {
                                    return true;
                                }
                            }
                            return false;
                        }),
                ])
                    ->label('狀態管理')
                    ->icon('heroicon-m-cog-6-tooth')
                    ->color('primary')
                    ->tooltip('管理訂單狀態'),

                self::getActionView(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            ItemsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListOrders::route('/'),
            'create' => Pages\CreateOrder::route('/create'),
            'edit' => Pages\EditOrder::route('/{record}/edit'),
            'view' => Pages\ViewOrder::route('/{record}'),
        ];
    }

}
