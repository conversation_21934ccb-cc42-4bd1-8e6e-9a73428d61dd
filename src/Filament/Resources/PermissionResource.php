<?php


namespace Stephen<PERSON>org\BaseFilamentPlugin\Filament\Resources;


use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\PermissionResource\Pages\CreatePermission;
use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\PermissionResource\Pages\EditPermission;
use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\PermissionResource\Pages\ListPermissions;
use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\PermissionResource\Pages\ViewPermission;
use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\PermissionResource\RelationManager\RoleRelationManager;
use Exception;
use Filament\Facades\Filament;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Resources\Pages\PageRegistration;
use Filament\Resources\Resource;
use Filament\Support\Enums\IconSize;
use Filament\Tables;
use Filament\Tables\Actions\BulkAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Grouping\Group;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Stephenchenorg\BaseFilamentPlugin\Models\Permission;
use Stephenchenorg\BaseFilamentPlugin\Models\Role;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormAdmin;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormTimestamps;

class PermissionResource extends Resource
{
    use CCTraitFormAdmin;
    use CCTraitFormTimestamps;


    protected static ?int $navigationSort = 180;

    /**
     * @var bool
     */
    protected static bool $isScopedToTenant = false;

    /**
     * @return string|null
     */
    public static function getNavigationIcon(): ?string
    {
        return config('filament-spatie-roles-permissions.icons.permission_navigation');
    }

    /**
     * @return bool
     */
    public static function shouldRegisterNavigation(): bool
    {
        return config('filament-spatie-roles-permissions.should_register_on_navigation.permissions', true);
    }

    /**
     * @return string
     */
    public static function getModel(): string
    {
        return config('permission.models.permission', Permission::class);
    }

    /**
     * @return string
     */
    public static function getLabel(): string
    {
        return __('filament-spatie-roles-permissions::filament-spatie.section.permission');
    }

    /**
     * @return string|null
     */
    public static function getNavigationGroup(): ?string
    {
        return __(config('filament-spatie-roles-permissions.navigation_section_group',
            'filament-spatie-roles-permissions::filament-spatie.section.roles_and_permissions'));
    }

    /**
     * @return int|null
     */
    public static function getNavigationSort(): ?int
    {
        return config('filament-spatie-roles-permissions.sort.permission_navigation');
    }

    /**
     * @return string
     */
    public static function getPluralLabel(): string
    {
        return __('filament-spatie-roles-permissions::filament-spatie.section.permissions');
    }

    /**
     * @return string|null
     */
    public static function getCluster(): ?string
    {
        return config('filament-spatie-roles-permissions.clusters.permissions', null);
    }

    /**
     * @param  Table  $table
     * @return Table
     * @throws Exception
     */
    public static function table(Table $table): Table
    {
//        dd(Role::query()->pluck('display_name', 'id'));

        return $table
            ->modifyQueryUsing(function (Builder $query)
            {
                return $query->select([
                    'id',
                    'name',
                    'display_name',
                    'group',
                    'guard_name',
                ]);
            })
            ->defaultPaginationPageOption(56)
            ->paginationPageOptions([56, 112, 168])
            ->selectCurrentPageOnly()
            ->groups([
                Group::make('group')
                    ->label('權限分組')
                    ->collapsible(),
            ])
            ->defaultGroup('group')
            ->columns([
//                TextColumn::make('id')
//                    ->label('#'),

TextColumn::make('name')
    ->label('系統權限唯一值'),

TextColumn::make('display_name')
    ->label('系統權限中文名稱')
    ->searchable(isIndividual: true),
            ])
            ->filters([
//                SelectFilter::make('models')
//                    ->label('旁邊選單名稱')
//                    ->multiple()
//                    ->options(function () {
//                        $options = [];
//                        $policyFiles = Utility::discoverPolicyFiles();
//
//                        foreach ($policyFiles as $policyFile) {
//                            $policyInstance = new $policyFile();
//
//                            if (!method_exists($policyInstance, 'getPermissionDisplayName')) {
//                                continue;
//                            }
//
//                            if (method_exists($policyInstance, 'getPermissionGroupName')) {
//                                $options[$policyInstance->getPermissionGroupName()] = $policyInstance->getPermissionDisplayName();
//                                continue;
//                            }
//                            $options[$policyInstance->getPermissionDisplayName()] = $policyInstance->getPermissionDisplayName();
//                        }
//
//                        return $options;
//                    })
//                    ->query(function (Builder $query, array $data) {
//                        if (isset($data['values'])) {
//                            $query->where(function (Builder $query) use ($data) {
//                                foreach ($data['values'] as $value) {
//                                    if ($value) {
//                                        $query->orWhere('group', '=', $value);
//                                    }
//                                }
//                            });
//                        }
//
//                        return $query;
//                    }),
            ])->actions([
                Tables\Actions\EditAction::make()
                    ->iconSize(IconSize::Large)
                    ->label(' '),
                Tables\Actions\ViewAction::make()
                    ->iconSize(IconSize::Large)
                    ->label(' '),
            ])
            ->bulkActions([
                BulkAction::make('Attach to roles')
                    ->label('批量設定到角色')
                    ->action(function (Collection $records, array $data): void
                    {
                        Role::query()->whereIn('id', $data['roles'])->each(function (Role $role) use ($records): void
                        {
                            $records->each(fn(Permission $permission) => $role->givePermissionTo($permission));
                        });
                    })
                    ->form([
                        Select::make('roles')
                            ->multiple()
                            ->label('請選擇角色')
                            ->options(Role::query()->where('name', '!=', 'Super Admin')->pluck('display_name',
                                'id')->toArray())
                            ->required(),
                    ])
                    ->deselectRecordsAfterCompletion(),
            ])
            ->emptyStateActions([
                Tables\Actions\CreateAction::make(),
            ]);
    }

    /**
     * @param  Form  $form
     * @return Form
     */
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make()
                    ->schema([
                        Grid::make(2)->schema([
                            TextInput::make('name')
                                ->label('系統權限唯一值')
                                ->disabled()
                                ->required(),
                            Select::make('guard_name')
                                ->label('系統權限渠道')
                                ->options(config('filament-spatie-roles-permissions.guard_names'))
                                ->default(config('filament-spatie-roles-permissions.default_guard_name'))
                                ->visible(fn() => config('filament-spatie-roles-permissions.should_show_guard', true))
                                ->live()
                                ->afterStateUpdated(fn(Set $set) => $set('roles', null))
                                ->required(),
                            Select::make('roles')
                                ->multiple()
                                ->label('所屬角色')
                                ->relationship(
                                    name: 'roles',
                                    titleAttribute: 'display_name',
                                    modifyQueryUsing: function (Builder $query, Get $get)
                                    {

                                        $query->whereNot('name', '=', 'Super Admin');

                                        if (!empty($get('guard_name'))) {
                                            $query->where('guard_name', $get('guard_name'));
                                        }
                                        if (Filament::hasTenancy()) {
                                            return $query->where(config('permission.column_names.team_foreign_key'),
                                                Filament::getTenant()->id);
                                        }
                                        return $query;
                                    }
                                )
                                ->preload(config('filament-spatie-roles-permissions.preload_roles', true)),
                        ]),
                    ]),


                self::getFormTimestamps(),
                self::getFormSectionAdminId(),
            ]);
    }

    /**
     * @return class-string[]
     */
    public static function getRelations(): array
    {
        return [
            RoleRelationManager::class,
        ];
    }

    /**
     * @return array|PageRegistration[]
     */
    public static function getPages(): array
    {
        return [
            'index'  => ListPermissions::route('/'),
            'create' => CreatePermission::route('/create'),
            'edit'   => EditPermission::route('/{record}/edit'),
            'view'   => ViewPermission::route('/{record}'),
        ];
    }

    /**
     * @return bool
     */
    public static function canCreate(): bool
    {
        return false;
    }
}
