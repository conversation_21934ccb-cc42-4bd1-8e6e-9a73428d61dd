<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Resources;

use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\FaqCategoryResource\Pages;
use Stephenchenorg\BaseFilamentPlugin\Models\FaqCategory;
use Stephenchenorg\BaseFilamentPlugin\Traits\CCTraitColumn;
use Stephenchenorg\BaseFilamentPlugin\Traits\CCTraitFormContent;
use Filament\Forms\Components\Section;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Str;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitAction;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormAdmin;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormEditor;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormOG;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormSEO;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormSort;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormTimestamps;

class FaqCategoryResource extends Resource
{
    use CCTraitColumn;
    use CCTraitFormAdmin;
    use CCTraitFormTimestamps;
    use CCTraitFormContent;
    use CCTraitFormEditor;
    use CCTraitFormSort;
    use CCTraitFormSEO;
    use CCTraitFormOG;
    use CCTraitAction;

    protected static ?string $model = FaqCategory::class;

    protected static ?string $navigationIcon = 'heroicon-o-inbox';

    protected static ?string $navigationGroup = '問與答管理';

    protected static ?string $label = '問與答類別';

    protected static ?int $navigationSort = 140;

//    public static function getNavigationBadge(): ?string
//    {
//        $count = CountService::getFaqCategoryCount();
//        $unit = CountService::getUnit('faq_category');
//
//        return "$count $unit";
//    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([

                Section::make('basic_settings')
                    ->heading('基本設定')
                    ->schema([
                        self::getFormToggle('status')
                            ->label('啟用/停用'),

                        self::getFormKey()
                            ->reactive()
                            ->debounce(500)
                            ->afterStateUpdated(function ($state, callable $set) {
                                $set('slug', Str::slug($state));
                            }),
                        self::getFormSort(),
                        self::getFormSlug(),
                    ]),

                self::getTabLanguage([
                    self::getFormTitle(),
                    self::getFormEditor('content')
                        ->label('內容 - 圖文編輯器')
                        ->placeholder('請輸入內容')
                        ->columnSpan('full'),
                    self::getFormSectionSEO(),
                    self::getFormSectionOG(),
                ]),

                self::getFormTimestamps(),
                self::getFormSectionAdminId(),
            ]);

    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                self::getColumnTextKey(),
                self::getColumnTextStatus(),
                self::getColumnTranslation('title', true)
                    ->label('問與答類別標題'),
                self::getColumnTextCount()->label('問答總數'),
                self::getColumnTextInputSort(),
                self::getColumnTextSlug(),
            ])
            ->paginated([5, 10, 25, 50, 100, 'all'])
            ->defaultPaginationPageOption(10)
            ->filters([
                //
            ])
            ->actions([
                self::getActionEdit(),
                self::getActionView(),
                self::getActionDelete(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [

        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListFaqCategories::route('/'),
            'create' => Pages\CreateFaqCategory::route('/create'),
            'edit' => Pages\EditFaqCategory::route('/{record}/edit'),
            'view' => Pages\ViewFaqCategory::route('/{record}'),
        ];
    }

    public static function canAccess(): bool
    {
        return config('cs.faq_visible') && Gate::allows('view-any', FaqCategory::class);
    }


}
