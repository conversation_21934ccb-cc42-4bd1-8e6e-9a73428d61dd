<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ProductSpecificationResource\Pages;

use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ProductSpecificationResource;

class ListProductSpecifications extends ListRecords
{
    protected static string $resource = ProductSpecificationResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
