<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ProductSpecificationResource\Pages;

use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ProductSpecificationResource;

class ViewProductSpecification extends ViewRecord
{
    protected static string $resource = ProductSpecificationResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }
}
