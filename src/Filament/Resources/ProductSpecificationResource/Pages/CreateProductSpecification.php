<?php

namespace <PERSON><PERSON>org\BaseFilamentPlugin\Filament\Resources\ProductSpecificationResource\Pages;

use Filament\Resources\Pages\CreateRecord;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Model;
use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ProductSpecificationResource;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductSpecification;

class CreateProductSpecification extends CreateRecord
{
    protected static string $resource = ProductSpecificationResource::class;

    protected function handleRecordCreation(array $data): Model
    {
        $combinationKey = $data['combination_key'] ?? null;

        $exist = ProductSpecification::query()
            ->where('product_id', '=', $data['product_id'])
            ->where('combination_key', '=', $combinationKey)
            ->exists();

        if ($exist) {
            Notification::make()
                ->title('已經存在相同規格')
                ->color('danger')
                ->danger()
                ->send();
            $this->halt();
        }

        return static::getModel()::create($data);
    }
}
