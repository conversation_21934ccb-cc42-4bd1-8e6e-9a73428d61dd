<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ProductSpecificationResource\Pages;

use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ProductSpecificationResource;

class EditProductSpecification extends EditRecord
{
    protected static string $resource = ProductSpecificationResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }
}
