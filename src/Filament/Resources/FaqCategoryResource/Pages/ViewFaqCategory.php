<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Resources\FaqCategoryResource\Pages;

use <PERSON>chenorg\BaseFilamentPlugin\Filament\Resources\FaqCategoryResource;
use Stephenchenorg\BaseFilamentPlugin\Service\ResourceService;
use Filament\Resources\Pages\ViewRecord;

class ViewFaqCategory extends ViewRecord
{
    protected static string $resource = FaqCategoryResource::class;

    protected function getActions(): array
    {
        return [
            ResourceService::getRedirectAction($this->getRedirectUrl()),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

}
