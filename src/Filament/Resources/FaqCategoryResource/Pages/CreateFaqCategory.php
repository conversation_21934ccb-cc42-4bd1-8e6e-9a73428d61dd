<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Resources\FaqCategoryResource\Pages;

use <PERSON><PERSON>org\BaseFilamentPlugin\Filament\Resources\FaqCategoryResource;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitRedirectToIndex;
use Filament\Resources\Pages\CreateRecord;

class CreateFaqCategory extends CreateRecord
{
    use CCTraitRedirectToIndex;

    protected static string $resource = FaqCategoryResource::class;

    /**
     * @return string
     */
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }


}
