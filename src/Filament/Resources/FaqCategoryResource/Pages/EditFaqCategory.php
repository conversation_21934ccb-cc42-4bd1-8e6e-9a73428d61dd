<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Resources\FaqCategoryResource\Pages;

use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\FaqCategoryResource;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitRedirectToIndex;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditFaqCategory extends EditRecord
{
    use CCTraitRedirectToIndex;

    /**
     * @var string
     */
    protected static string $resource = FaqCategoryResource::class;

    /**
     * @return array|Actions\Action[]|Actions\ActionGroup[]
     */
    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }

    /**
     * @return string
     */
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
