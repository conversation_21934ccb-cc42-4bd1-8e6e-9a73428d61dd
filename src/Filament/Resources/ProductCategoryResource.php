<?php

namespace Stephen<PERSON>org\BaseFilamentPlugin\Filament\Resources;

use Stephenchenorg\BaseFilamentPlugin\Enum\EnumProductType;
use Stephenchenorg\BaseFilamentPlugin\Filament\Exports\ProductCategoryExporter;
use Stephenchenorg\BaseFilamentPlugin\Filament\Imports\ProductCategoryImporter;
use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ProductCategoryResource\Pages;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductCategory;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceLanguage;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceProductCategory;
use Stephenchenorg\BaseFilamentPlugin\Traits\CCTraitColumn;
use Stephenchenorg\BaseFilamentPlugin\Traits\CCTraitFormContent;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\ExportAction;
use Filament\Tables\Actions\ImportAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Str;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitAction;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormAdmin;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormImage;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormOG;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormSEO;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormSort;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormTimestamps;

class ProductCategoryResource extends Resource
{
    use CCTraitFormAdmin;
    use CCTraitFormTimestamps;
    use CCTraitFormImage;
    use CCTraitFormContent;
    use CCTraitFormSort;
    use CCTraitFormSEO;
    use CCTraitFormOG;
    use CCTraitColumn;
    use CCTraitAction;

    protected static ?string $model = ProductCategory::class;

    protected static ?string $navigationIcon = 'heroicon-o-inbox';

    protected static ?string $navigationGroup = '產品管理';

    protected static ?string $label = '產品類別';

    protected static ?int $navigationSort = 160;


    public static function form(Form $form): Form
    {
        return $form
            ->schema([

                Section::make('basic_settings')
                    ->heading('基本設定')
                    ->schema([

                        Select::make('type')
                            ->label('產品種類')
                            ->reactive()
                            ->required()
                            ->options(collect(EnumProductType::getOptions())->mapWithKeys(function ($item) {
                                return [$item->value => $item->getLabel()];
                            }))
                            ->default(function() {
                                $options = EnumProductType::getOptions();
                                return !empty($options) ? $options[0]->value : null;
                            }),

                        ServiceProductCategory::getColumnSelectTree('parent_id', function (Get $get) {
                            return $get('type');
                        }, 'parent')
                            ->required(),

                        self::getFormToggle('is_hottest')
                            ->label('(是/否)熱門'),

                        self::getFormToggle('is_newest')
                            ->label('(是/否)最新'),


                        self::getFormToggle('status')
                            ->label('啟用/停用'),

                        self::getFormSort(),
                    ]),

                self::getFormSectionImage(nullable: true),
                self::getTabLanguage([
                    self::getFormTitle(),
                    self::getFormSectionSEO(),
                    self::getFormSectionOG(),
                ]),

                self::getFormTimestamps(),
                self::getFormSectionAdminId(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                self::getColumnTranslation('title', true)
                    ->label('產品類別標題'),
                TextColumn::make("parent.translations.title")
                    ->label('上一層類別標題')
                    ->limit(50)
                    ->getStateUsing(function ($record) {
                        return $record->parent?->translations()
                            ->where('lang', '=', ServiceLanguage::getDefaultLanguage())
                            ->first()?->title;
                    })
                    ->searchable(true, function ($query, $search) {
                        $query->whereHas('translations', function ($q) use ($search) {
                            $q->where('lang', '=', ServiceLanguage::getDefaultLanguage());
                            $q->whereRaw("LOWER('title') LIKE ?", ['%' . strtolower($search) . '%']);
                        });
                    }, true),
                self::getColumnTextIsHottest(),
                self::getColumnTextStatus(),
                self::getColumnTextCount()->label('產品總數'),
                self::getColumnTextInputSort(),
            ])
            ->paginated([5, 10, 25, 50, 100])
            ->defaultPaginationPageOption(10)
            ->filters([
                //
            ])
            ->actions([
                self::getActionEdit(),
                self::getActionView(),
                self::getActionDelete(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //ProductCategoryResource\RelationManagers\TranslationsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListProductCategories::route('/'),
            'create' => Pages\CreateProductCategory::route('/create'),
            'edit' => Pages\EditProductCategory::route('/{record}/edit'),
            'view' => Pages\ViewProductCategory::route('/{record}'),
        ];
    }

    public static function canAccess(): bool
    {
        return config('cs.product_visible') && Gate::allows('view-any', ProductCategory::class);
    }

}
