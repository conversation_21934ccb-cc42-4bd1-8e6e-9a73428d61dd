<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ActivityLogResource\Pages;

use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ActivityLogResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitRedirectToIndex;

class EditActivityLog extends EditRecord
{
    use CCTraitRedirectToIndex;

    protected static string $resource = ActivityLogResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}
