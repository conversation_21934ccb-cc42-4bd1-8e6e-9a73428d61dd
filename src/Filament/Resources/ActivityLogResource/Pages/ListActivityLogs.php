<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ActivityLogResource\Pages;

use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ActivityLogResource;
use Filament\Resources\Pages\ListRecords;

class ListActivityLogs extends ListRecords
{
    protected static string $resource = ActivityLogResource::class;

    protected function getHeaderActions(): array
    {
        return [
//            Actions\CreateAction::make(),
        ];
    }
}
