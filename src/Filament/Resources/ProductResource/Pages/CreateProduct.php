<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ProductResource\Pages;

use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ProductResource;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitRedirectToIndex;
use Filament\Resources\Pages\CreateRecord;

class CreateProduct extends CreateRecord
{
    use CCTraitRedirectToIndex;

    protected static string $resource = ProductResource::class;

    /**
     * @return string
     */
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
