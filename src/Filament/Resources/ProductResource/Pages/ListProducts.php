<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ProductResource\Pages;

use Stephenchenorg\BaseFilamentPlugin\Enum\EnumProductType;
use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ProductResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Resources\Components\Tab;

class ListProducts extends ListRecords
{
    protected static string $resource = ProductResource::class;

    public function getTabs(): array
    {
        // Get available product types
        $productTypes = EnumProductType::getOptions();

        // If there's only one product type, don't show tabs
        if (count($productTypes) < 2) {
            return [];
        }

        $tabs = [
            'all' => Tab::make('全部')
                ->label('全部')
                ->query(fn($query) => $query),
        ];

        // Add tabs for each product type
        foreach ($productTypes as $type) {
            $tabs[$type->value] = Tab::make($type->value)
                ->label($type->getLabel())
                ->query(fn($query) => $query->where('type', $type->value));
        }

        return $tabs;
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
