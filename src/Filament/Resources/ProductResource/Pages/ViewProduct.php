<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ProductResource\Pages;

use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ProductResource;
use Stephenchenorg\BaseFilamentPlugin\Service\ResourceService;
use Filament\Resources\Pages\ViewRecord;

class ViewProduct extends ViewRecord
{
    protected static string $resource = ProductResource::class;

    protected function getActions(): array
    {
        return [
            ResourceService::getRedirectAction($this->getRedirectUrl()),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

}
