<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ProductResource\Pages;

use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ProductResource;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitRedirectToIndex;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditProduct extends EditRecord
{
    use CCTraitRedirectToIndex;

    protected static string $resource = ProductResource::class;

    protected function getHeaderActions(): array
    {
        return [
        ];
    }

    /**
     * @return string
     */
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }


}
