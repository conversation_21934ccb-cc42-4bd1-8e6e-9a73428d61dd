<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ProductResource\RelationManagers;

use Stephenchenorg\BaseFilamentPlugin\Enum\EnumProductDetailType;
use Stephenchenorg\BaseFilamentPlugin\Service\ResourceService;
use Stephenchenorg\BaseFilamentPlugin\Traits\CCTraitColumn;
use Stephenchenorg\BaseFilamentPlugin\Traits\CCTraitFormContent;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitAction;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormEditor;

class DetailsRelationManager extends RelationManager
{
    use CCTraitColumn;
    use CCTraitFormEditor;
    use CCTraitAction;
    use CCTraitFormContent;

    protected static string $relationship = 'details';

    protected static ?string $label = '說明';

    protected static ?string $title = '說明';

    public function form(Form $form): Form
    {
        return $form
            ->schema([

                Section::make('basic_settings')
                    ->heading('基本設定')
                    ->schema([
                        Select::make('type')
                            ->label('類型')
                            ->options(collect(EnumProductDetailType::cases())->mapWithKeys(function ($item) {
                                return [$item->value => $item->getLabel()];
                            })),
                    ]),

                self::getTabLanguage([
                    self::getFormEditor('content')
                        ->label('內容 - 圖文編輯器')
                        ->placeholder('請輸入內容')
                        ->columnSpan('full')
                        ->required(),
                ]),

            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('id')
            ->columns([

                TextColumn::make('type')
                    ->label('類型')
                    ->sortable(true)
                    ->searchable(true)
                    ->toggleable(false)
                    ->limit(50)
                    ->formatStateUsing(function ($state) {
                        return $state->getLabel();
                    }),


            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                self::getActionEdit(),
                self::getActionView(),
                self::getActionDelete(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
