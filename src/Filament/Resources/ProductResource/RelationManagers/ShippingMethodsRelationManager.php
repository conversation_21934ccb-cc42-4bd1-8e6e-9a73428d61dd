<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ProductResource\RelationManagers;

use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Stephenchenorg\BaseFilamentPlugin\Enum\EnumShippingMethod;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductShippingMethod;
use Stephenchenorg\BaseFilamentPlugin\Models\ShippingMethod;
use Stephenchenorg\BaseFilamentPlugin\Traits\CCTraitColumn;
use Stephenchenorg\BaseFilamentPlugin\Traits\CCTraitFormContent;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitAction;

class ShippingMethodsRelationManager extends RelationManager
{
    use CCTraitColumn;
    use CCTraitFormContent;
    use CCTraitAction;

    protected static string $relationship = 'shippingMethods';

    protected static ?string $title = '物流設定';

    protected static ?string $label = '物流設定';

    protected static ?string $pluralLabel = '物流設定';

    public function form(Form $form): Form
    {
        return $form
            ->schema([

                Section::make('basic_settings')
                    ->heading('基本設定')
                    ->schema([
                        Select::make('shipping_method_id')
                            ->label('運送方式')
                            ->options(function () {
                                return ShippingMethod::all()->mapWithKeys(function ($method) {
                                    return [$method->id => $method->type->getLabel()];
                                });
                            })
                            ->required()
                            ->searchable()
                            ->preload(),

                        self::getFormToggle('status')
                            ->label('啟用/停用')
                            ->helperText('設定此產品是否支援該運送方式'),

                        TextInput::make('amount')
                            ->label('運費金額')
                            ->numeric()
                            ->minValue(0)
                            ->suffix('元')
                            ->default(0)
                            ->required()
                            ->helperText('設定此產品使用該運送方式的運費，0表示免運費'),
                    ]),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('type')
            ->columns([

                TextColumn::make('type')
                    ->label('運送方式')
                    ->formatStateUsing(function ($state) {
                        return $state->getLabel();
                    })
                    ->badge(),

                Tables\Columns\ToggleColumn::make('pivot.status')
                    ->label('啟用/停用')
                    ->updateStateUsing(function ($state, $record) {
                        $record->pivot->status = $state;
                        $record->pivot->save();
                    }),

                Tables\Columns\ToggleColumn::make('pivot.combine_same_product')
                    ->label('同一個商品運費是否疊加')
                    ->updateStateUsing(function ($state, $record) {
                        $record->pivot->combine_same_product = $state;
                        $record->pivot->save();
                    }),

                Tables\Columns\ToggleColumn::make('pivot.combine_different_product')
                    ->label('不同商品運費是否疊加')
                    ->updateStateUsing(function ($state, $record) {
                        $record->pivot->combine_different_product = $state;
                        $record->pivot->save();
                    }),

                Tables\Columns\TextInputColumn::make('pivot.amount')
                    ->label('運費金額')
                    ->updateStateUsing(function ($state, $record) {
                        $record->pivot->amount = $state;
                        $record->pivot->save();
                    }),

                TextColumn::make('default_amount')
                    ->label('系統預設運費(供參考)')
                    ->formatStateUsing(function ($state) {
                        return $state == 0 ? '免運費' : '$' . number_format($state);
                    })
                    ->color('gray')
                    ->description('系統設定的預設運費金額'),
            ])
            ->filters([
            ])
            ->headerActions([

                Tables\Actions\Action::make('shipping_setting')
                    ->label('開啟單品設定')
                    ->action(function () {

                        $methods = ShippingMethod::all();
                        $editableMethodValues = array_map(function ($method) {
                            return $method->value;
                        }, EnumShippingMethod::getEditableMethods());

                        foreach ($methods as $method) {

                            if(!in_array($method->type->value, $editableMethodValues)) continue;

                            ProductShippingMethod::query()
                                ->updateOrCreate([
                                    'product_id' => $this->getOwnerRecord()->id,
                                    'shipping_method_id' => $method->id,
                                ], [
                                    'amount' => $method->default_amount,
                                ]);
                        }
                    })
            ])
            ->bulkActions([
            ])
            ->defaultSort('shipping_methods.id', 'asc');
    }
}
