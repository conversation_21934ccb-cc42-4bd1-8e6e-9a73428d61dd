<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ProductResource\RelationManagers;

use Stephenchenorg\BaseFilamentPlugin\Traits\CCTraitColumn;
use Stephenchenorg\BaseFilamentPlugin\Traits\CCTraitFormContent;
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Support\Enums\IconSize;
use Filament\Tables;
use Filament\Tables\Table;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitAction;

class AttributesRelationManager extends RelationManager
{
    use CCTraitColumn;
    use CCTraitFormContent;
    use CCTraitAction;

    protected static string $relationship = 'attributes';

    protected static ?string $title = '規格屬性';

    protected static ?string $label = '規格屬性';

    public function form(Form $form): Form
    {
        return $form
            ->schema([

                self::getTabLanguage([
                    self::getFormTitle()
                        ->helperText('如尺寸、顏色、型號'),
                ], false),

                Repeater::make('items')
                    ->columnSpanFull()
                    ->label('屬性選項')
                    ->relationship('items')
                    ->defaultItems(1)
                    ->minItems(1)
                    ->deleteAction(
                        function (Action $action) {
                            return $action
                                ->requiresConfirmation()
                                ->before(function ($state, array $arguments) use ($action) {
                                    $specifications = $this->getOwnerRecord()->specifications;
                                    foreach ($specifications as $specification) {
                                        if (empty($specification->combination_key)) {
                                            continue;
                                        }
                                        $optionId = $state[$arguments['item']]['id'];
                                        $selectedItems = explode('-', $specification->combination_key);
                                        if (in_array($optionId, $selectedItems)) {
                                            Notification::make()
                                                ->title('正在使用中的規格無法刪除')
                                                ->body('請先更新價格與型號表')
                                                ->danger()
                                                ->send();

                                            $action->halt();
                                        }
                                    }
                                });
                        }
                    )
                    ->schema([
                        self::getTabLanguage([
                            self::getFormTitle()
                                ->helperText('具體的選項 如果屬性是尺寸 那選項就是大、中、小'),
                        ], false),
                    ]),


            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('key')
            ->columns([
                self::getColumnTranslation('title', false)
                    ->label('名稱'),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->successNotification(function(){
                        return Notification::make()
                            ->title('屬性新增成功')
                            ->body('請去更新價格與型號表 否則無法出現在產品頁')
                            ->info()
                            ->color('info')
                            ->persistent()
                            ->send();
                    }),
            ])
            ->actions([

                self::getActionEdit(),

                self::getActionDelete()
                    ->label(' ')
                    ->before(function($record){
                        $this->getOwnerRecord()->specifications->each(function($spec) use ($record){
                            $itemIds = $record->items->map(fn($item) => $item->id)->toArray();
                            $updatedKey = collect(explode('-', $spec->combination_key ?? ''))
                                ->filter(fn($id) => !in_array($id, $itemIds));
                            $updatedKey = $updatedKey->isEmpty() ? null : $updatedKey->implode('-');
                            $spec->combination_key = $updatedKey;
                            $spec->save();
                        });
                    })
                    ->successNotification(function(){
                        return Notification::make()
                            ->title('屬性刪除成功')
                            ->body('請去更新價格與型號表 否則會出錯')
                            ->info()
                            ->color('info')
                            ->persistent()
                            ->send();
                    })

            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
