<?php

namespace <PERSON>chenorg\BaseFilamentPlugin\Filament\Resources\ProductResource\RelationManagers;

use Stephenchenorg\BaseFilamentPlugin\Service\ResourceService;
use Stephenchenorg\BaseFilamentPlugin\Service\TagService;
use Stephenchenorg\BaseFilamentPlugin\Traits\CCTraitColumn;

use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitAction;

class TagsRelationManager extends RelationManager
{
    use  CCTraitColumn;
    use CCTraitAction;

    protected static string $relationship = 'tags';


    protected static ?string $title = '標籤';

    protected static ?string $label = '標籤';


    public function form(Form $form): Form
    {
        return $form
            ->schema(TagService::getProductTagForm());
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('key')
            ->columns(TagService::getProductTagTable())
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                self::getActionEdit(),
                self::getActionView(),
                self::getActionDelete(),
            ])
            ->defaultSort('tags.id', 'desc')
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
