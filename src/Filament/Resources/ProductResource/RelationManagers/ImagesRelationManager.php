<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ProductResource\RelationManagers;

use Stephenchenorg\BaseFilamentPlugin\Traits\CCTraitColumn;
use Stephenchenorg\BaseFilamentPlugin\Traits\CCTraitFormContent;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Table;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitAction;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormImage;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormSort;

class ImagesRelationManager extends RelationManager
{
    protected static string $relationship = 'images';

    use CCTraitColumn;
    use CCTraitColumn;
    use CCTraitFormImage;
    use CCTraitFormSort;
    use CCTraitAction;
    use CCTraitFormContent;


    protected static ?string $title = '圖片';

    protected static ?string $label = '圖片';

    public function form(Form $form): Form
    {
        return $form
            ->schema([

                self::getFormToggle('is_default')
                    ->label('默認圖片')
                    ->helperText('啟用默認圖片將會顯示在列表頁面'),

                self::getFormSort(),
                self::getFormSectionImage(),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('id')
            ->columns([
                IconColumn::make('is_default')
                    ->label('默認圖片')
                    ->sortable()
                    ->searchable()
                    ->boolean() // 指定這是一個布爾列
                    ->trueIcon('heroicon-o-check-circle') // 當值為 true 時顯示的圖標
                    ->falseIcon('heroicon-o-x-circle') // 當值為 false 時顯示的圖標
                    ->trueColor('success') // 當值為 true 時的顏色
                    ->falseColor('danger'), // 當值為 false 時的顏色
                self::getColumnTextInputSort(),
                self::getColumnImage('image', '圖片'),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                self::getActionEdit(),
                self::getActionView(),
                self::getActionDelete(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
