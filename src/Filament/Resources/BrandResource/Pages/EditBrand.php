<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Resources\BrandResource\Pages;

use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\BrandResource;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitRedirectToIndex;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditBrand extends EditRecord
{
    use CCTraitRedirectToIndex;

    protected static string $resource = BrandResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }

    /**
     * @return string
     */
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }


}
