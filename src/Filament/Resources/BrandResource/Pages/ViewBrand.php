<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Resources\BrandResource\Pages;

use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ContactResource;
use Stephenchenorg\BaseFilamentPlugin\Service\ResourceService;
use Filament\Resources\Pages\ViewRecord;

class ViewBrand extends ViewRecord
{
    protected static string $resource = ContactResource::class;

    protected function getActions(): array
    {
        return [
            ResourceService::getRedirectAction($this->getRedirectUrl()),
        ];
    }


    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
