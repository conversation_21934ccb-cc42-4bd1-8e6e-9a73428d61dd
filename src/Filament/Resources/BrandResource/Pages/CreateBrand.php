<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Resources\BrandResource\Pages;

use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\BrandResource;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitRedirectToIndex;
use Filament\Resources\Pages\CreateRecord;

class CreateBrand extends CreateRecord
{
    use CCTraitRedirectToIndex;

    protected static string $resource = BrandResource::class;

    /**
     * @return string
     */
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }


}
