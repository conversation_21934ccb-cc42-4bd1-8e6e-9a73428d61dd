<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Resources\OrderResource\Pages;

use Filament\Resources\Pages\ViewRecord;
use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\OrderResource;
use Stephenchenorg\BaseFilamentPlugin\Service\ResourceService;

class ViewOrder extends ViewRecord
{
    protected static string $resource = OrderResource::class;

    protected function getActions(): array
    {
        return [
            ResourceService::getRedirectAction($this->getRedirectUrl()),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
