<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Resources\OrderResource\Pages;

use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\OrderResource;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitRedirectToIndex;

class EditOrder extends EditRecord
{
    use CCTraitRedirectToIndex;

    protected static string $resource = OrderResource::class;
    protected function getHeaderActions(): array
    {
        return [
        ];
    }
}
