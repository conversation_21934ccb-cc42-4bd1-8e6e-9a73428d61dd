<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Resources\OrderResource\Pages;

use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\OrderResource;

class ListOrders extends ListRecords
{
    protected static string $resource = OrderResource::class;

    /**
     * @return array
     */
    protected function getHeaderActions(): array
    {
        return [
        ];
    }
}
