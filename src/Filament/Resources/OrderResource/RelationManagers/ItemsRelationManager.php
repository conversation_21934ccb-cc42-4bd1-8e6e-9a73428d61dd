<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Resources\OrderResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductSpecification;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceLanguage;
use Stephenchenorg\BaseFilamentPlugin\Traits\CCTraitColumn;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitAction;

class ItemsRelationManager extends RelationManager
{
    use CCTraitAction;
    use CCTraitColumn;

    protected static string $relationship = 'items';

    protected static ?string $title = '訂單明細';

    protected static ?string $label = '訂單明細';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('product_specification_id')
                    ->label('產品')
                    ->relationship('specification', 'title')
                    ->getOptionLabelFromRecordUsing(function($record) {
                        return $record->translations
                            ->where('lang', '=', ServiceLanguage::getDefaultLanguage())
                            ->first()
                            ->title;
                    })

                    ->searchable()
                    ->required()
                    ->reactive(),

//                self::getColumnTranslation('title')
//                    ->label('產品規格名稱'),

                Forms\Components\TextInput::make('quantity')
                    ->label('數量')
                    ->numeric()
                    ->default(1)
                    ->required(),

                Forms\Components\TextInput::make('unit_price')
                    ->label('單價')
                    ->numeric()
                    ->required(),

                Forms\Components\TextInput::make('total_amount')
                    ->label('總計')
                    ->numeric()
                    ->disabled(),
            ])->columns(1);
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([

                self::getColumnTranslation('title', true, 'specification')
                    ->label('產品規格名稱'),

                Tables\Columns\TextColumn::make('quantity')
                    ->label('數量')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('unit_price')
                    ->label('單價')
                    ->money('TWD')
                    ->sortable(),
                Tables\Columns\TextColumn::make('total_amount')
                    ->label('總計')
                    ->money('TWD')
                    ->sortable(),
            ])
            ->actions([
                self::getActionView(),
            ]);
    }
}
