<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Resources\RewardRecordResource\Pages;

use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\RewardRecordResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListRewardRecords extends ListRecords
{
    protected static string $resource = RewardRecordResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
