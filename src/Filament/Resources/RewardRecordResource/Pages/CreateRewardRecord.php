<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Resources\RewardRecordResource\Pages;

use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\RewardRecordResource;
use Filament\Resources\Pages\CreateRecord;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitRedirectToIndex;

class CreateRewardRecord extends CreateRecord
{
    use CCTraitRedirectToIndex;

    protected static string $resource = RewardRecordResource::class;
}
