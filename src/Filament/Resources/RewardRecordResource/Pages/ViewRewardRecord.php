<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Resources\RewardRecordResource\Pages;

use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\RewardRecordResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewRewardRecord extends ViewRecord
{
    protected static string $resource = RewardRecordResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }
}
