<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Resources;

use Stephenchenorg\BaseFilamentPlugin\Enum\EnumFieldType;
use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\PageResource\Pages;
use Stephenchenorg\BaseFilamentPlugin\Models\Page;
use Stephenchenorg\BaseFilamentPlugin\Traits\CCTraitColumn;
use Stephenchenorg\BaseFilamentPlugin\Traits\CCTraitFormContent;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Support\Str;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitAction;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormAdmin;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormOG;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormSEO;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormTimestamps;

class PageResource extends Resource
{
    use CCTraitColumn;
    use CCTraitFormAdmin;
    use CCTraitFormTimestamps;
    use CCTraitFormContent;
    use CCTraitFormSEO;
    use CCTraitFormOG;
    use CCTraitAction;

    protected static ?string $model = Page::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $navigationGroup = '系統管理';

    protected static ?string $navigationLabel = '靜態頁面管理';

    protected static ?string $label = '靜態頁面管理';

    protected static ?int $navigationSort = 20;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([

                Section::make('basic_settings')
                    ->heading('基本資料設定')
                    ->schema([
                        self::getFormKey()
                            ->reactive()
                            ->debounce(500)
                            ->afterStateUpdated(function ($state, callable $set) {
                                $set('slug', Str::slug($state));
                            }),
                        self::getFormSlug(),
                    ]),

                self::getTabLanguage([
                    Section::make('display')
                        ->heading('顯示區塊')
                        ->schema([
                            self::getFormTitle(),
                        ]),
                    self::getFormSectionSEO(),
                    self::getFormSectionOG(),
                ]),


                Section::make('fields')
                    ->heading('欄位設定')
                    ->schema([
                        Repeater::make('fields')
                            ->label('欄位')
                            ->relationship('fields')
                            ->schema([

                                Select::make('type')
                                    ->label('欄位類型')
                                    ->options(collect(EnumFieldType::cases())->mapWithKeys(fn($case,
                                    ) => [$case->value => $case->getLabel()])->toArray())
                                    ->multiple(false)
                                    ->required()
                                    ->searchable(),

                                self::getFormKey(isUnique: false)
                                    ->rules([
                                        fn(Get $get) => function ($attribute, $value, $fail) use ($get) {
                                            $fields = collect($get('../'));
                                            $exist = $fields->where('key', $get('key'))->count() > 1;
                                            if ($exist) {
                                                $fail('同一個頁面的key不可以重複');
                                            }
                                        },
                                    ])
                                    ->label('欄位唯一值'),

                                TextInput::make('helper_text')
                                    ->label('欄位說明文字')
                                    ->required(),

                            ])
                            ->addActionLabel('新增頁面欄位')
                            ->columnSpanFull(),
                    ]),


                self::getFormTimestamps(),
                self::getFormSectionAdminId(),


            ]);

    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                self::getColumnTextKey(),
                self::getColumnTranslation('title', true)
                    ->label('頁面標題'),
            ])
            ->paginated([5, 10, 25, 50, 100, 'all'])
            ->defaultPaginationPageOption(10)
            ->filters([
                //
            ])
            ->actions([
                self::getActionEdit(),
                self::getActionView(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            // PageResource\RelationManagers\TranslationsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPages::route('/'),
            'create' => Pages\CreatePage::route('/create'),
            'edit' => Pages\EditPage::route('/{record}/edit'),
            'view' => Pages\ViewPage::route('/{record}'),
        ];
    }


    public static function canAccess(): bool
    {
        $user = auth()->user();
        return $user && $user->isSuperAdmin();
    }

}
