<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Resources\AdminResource\Pages;

use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\AdminResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListAdmins extends ListRecords
{
    protected static string $resource = AdminResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
