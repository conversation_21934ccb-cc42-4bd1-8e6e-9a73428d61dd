<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Resources;

use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\AdminResource\Pages;
use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\AdminResource\RelationManagers;
use Stephenchenorg\BaseFilamentPlugin\Models\Admin;
use Filament\Forms;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Validation\Rules\Password;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitAction;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormAdmin;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormTimestamps;

class AdminResource extends Resource
{
    use CCTraitFormAdmin;
    use CCTraitFormTimestamps;
    use CCTraitAction;

    protected static ?string $model = Admin::class;

    protected static ?string $navigationIcon = 'heroicon-o-user-circle';

    protected static ?string $navigationGroup = '角色和權限';

    protected static ?string $navigationLabel = '帳號';

    protected static ?string $label = '帳號';

    protected static ?int $navigationSort = 60;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([

                Section::make()
                    ->heading('基本設定')
                    ->schema([

                        Forms\Components\TextInput::make('name')
                            ->label('名稱')
                            ->unique('admins', 'name', ignoreRecord: true)
                            ->required()
                            ->columnSpanFull()
                            ->maxLength(30),

                        Forms\Components\TextInput::make('email')
                            ->label('電子信箱')
                            ->unique('admins', 'email', ignoreRecord: true)
                            ->required()
                            ->email()
                            ->columnSpanFull()
                            ->maxLength(70),

                        TextInput::make('password')
                            ->label('密碼')
                            ->password()
                            ->rule(
                                Password::min(5) // 最小字數
                                ->max(100)  // 最長100
                                ->mixedCase()  // 大小寫
                                ->letters()    // 字母
                                ->numbers()    // 数字
                                ->symbols()    // 符號
                            )
                            ->revealable(filament()->arePasswordsRevealable())
                            ->autocomplete('new-password')
                            ->helperText('長度 5 ~ 100，並且符合1個大小寫字母、1個數字、1個特殊符號')
                            ->dehydrated(fn($state): bool => filled($state))
                            ->live(debounce: 500)
                            ->same('passwordConfirmation'),

                        TextInput::make('passwordConfirmation')
                            ->label('確認密碼')
                            ->password()
                            ->rule(
                                Password::min(5) // 最小字數
                                ->max(100)  // 最長10
                                ->mixedCase()  // 大小寫
                                ->letters()    // 字母
                                ->numbers()    // 数字
                                ->symbols()    // 符號
                            )
                            ->revealable(filament()->arePasswordsRevealable())
                            ->required()
                            ->visible(fn(Get $get): bool => filled($get('password')))
                            ->dehydrated(false),
                    ]),

                self::getFormTimestamps(),
                self::getFormSectionAdminId(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(function (Builder $query) {
                $query->whereDoesntHave('roles', function ($q) {
                    $q->where('name', 'Super Admin');
                });
            })
            ->columns([
                Tables\Columns\TextColumn::make('name')->sortable()->searchable()
                    ->label('名稱'),
                Tables\Columns\TextColumn::make('email')->sortable()->searchable()
                    ->label('電子信箱'),
                Tables\Columns\TextColumn::make('created_at')->dateTime()
                    ->label('建立時間'),
                Tables\Columns\TextColumn::make('updated_at')->dateTime()
                    ->label('更新時間'),
            ])
            ->paginated([5, 10, 25, 50, 100, 'all'])
            ->defaultPaginationPageOption(10)
            ->filters([
                //
            ])
            ->actions([
                self::getActionEdit(),
                self::getActionView(),
                self::getActionDelete(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAdmins::route('/'),
            'create' => Pages\CreateAdmin::route('/create'),
            'edit' => Pages\EditAdmin::route('/{record}/edit'),
        ];
    }
}
