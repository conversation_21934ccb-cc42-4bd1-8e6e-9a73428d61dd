<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Resources\PermissionResource\Pages;

use Stephen<PERSON>org\BaseFilamentPlugin\Filament\Resources\PermissionResource;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitRedirectToIndex;
use Filament\Resources\Pages\EditRecord;

class EditPermission extends EditRecord
{
    use CCTraitRedirectToIndex;

    /**
     * @var string
     */
    protected static string $resource = PermissionResource::class;

    /**
     * @return string|null
     */
    protected function getRedirectUrl(): ?string
    {
        $resource = static::getResource();

        return config('filament-spatie-roles-permissions.should_redirect_to_index.permissions.after_edit', false)
            ? $resource::getUrl('index')
            : parent::getRedirectUrl();
    }
}
