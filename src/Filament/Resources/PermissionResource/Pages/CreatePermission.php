<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Resources\PermissionResource\Pages;

use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\PermissionResource;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitRedirectToIndex;
use Filament\Resources\Pages\CreateRecord;

class CreatePermission extends CreateRecord
{
    use CCTraitRedirectToIndex;

    /**
     * @var string
     */
    protected static string $resource = PermissionResource::class;

    /**
     * @return string
     */
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
