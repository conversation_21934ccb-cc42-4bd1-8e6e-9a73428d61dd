<?php

namespace Stephen<PERSON>g\BaseFilamentPlugin\Filament\Resources\PermissionResource\Pages;

use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\PermissionResource;
use Filament\Actions\EditAction;
use Filament\Resources\Pages\ViewRecord;

class ViewPermission extends ViewRecord
{
    protected static string $resource = PermissionResource::class;

    public function getHeaderActions(): array
    {
        return [
            EditAction::make(),
        ];
    }
}
