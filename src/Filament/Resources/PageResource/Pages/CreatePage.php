<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Resources\PageResource\Pages;

use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\PageResource;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitRedirectToIndex;
use Filament\Resources\Pages\CreateRecord;

class CreatePage extends CreateRecord
{
    use CCTraitRedirectToIndex;

    /**
     * @var string
     */
    protected static string $resource = PageResource::class;

    /**
     * @return string
     */
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

}
