<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Resources\PageResource\Pages;

use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\PageResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListPages extends ListRecords
{
    protected static string $resource = PageResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
