<?php

namespace <PERSON>chenorg\BaseFilamentPlugin\Filament\Resources\PageResource\Pages;

use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\PageResource;
use Stephenchenorg\BaseFilamentPlugin\Service\ResourceService;
use Filament\Resources\Pages\ViewRecord;

class ViewPage extends ViewRecord
{
    protected static string $resource = PageResource::class;

    protected function getActions(): array
    {
        return [
            ResourceService::getRedirectAction($this->getRedirectUrl()),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

}
