<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ArticleResource\Pages;

use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ArticleResource;
use Stephenchenorg\BaseFilamentPlugin\Service\ResourceService;
use Filament\Resources\Pages\ViewRecord;

class ViewArticle extends ViewRecord
{
    protected static string $resource = ArticleResource::class;

    protected function getActions(): array
    {
        return [
            ResourceService::getRedirectAction($this->getRedirectUrl()),
        ];
    }


    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

}
