<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ArticleResource\Pages;

use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ArticleResource;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitRedirectToIndex;
use Filament\Resources\Pages\CreateRecord;

class CreateArticle extends CreateRecord
{
    use CCTraitRedirectToIndex;

    protected static string $resource = ArticleResource::class;

    /**
     * @return string
     */
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

}
