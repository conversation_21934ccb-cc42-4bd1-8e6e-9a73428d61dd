<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ArticleResource\Pages;

use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ArticleResource;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitRedirectToIndex;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditArticle extends EditRecord
{
    use CCTraitRedirectToIndex;

    protected static string $resource = ArticleResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];

    }

    /**
     * @return string
     */
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
