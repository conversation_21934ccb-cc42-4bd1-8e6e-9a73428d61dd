<?php

namespace Stephen<PERSON>org\BaseFilamentPlugin\Filament\Resources;

use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ActivityLogResource\Pages\ListActivityLogs;
use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\Panel\ActivityLogResource\Pages;
use Stephenchenorg\BaseFilamentPlugin\Models\ActivityLog;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Components\KeyValue;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitAction;

class ActivityLogResource extends Resource
{
    use CCTraitAction;

    protected static ?string $model = ActivityLog::class;

    protected static ?string $navigationIcon = 'heroicon-o-code-bracket-square';

    protected static ?string $navigationGroup = '系統管理';
    protected static ?string $navigationLabel = '後台記錄';

    protected static ?string $label = '後台記錄';

    protected static ?int $navigationSort = 30;

    public static function getModelLabel(): string
    {
        return '系統修改紀錄';
    }

    public static function getPluralModelLabel(): string
    {
        return '系統修改紀錄';
    }

    public static function getNavigationLabel(): string
    {
        return '系統修改紀錄';
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make()->schema([
                    TextInput::make('log_name')
                        ->label('被影響的資源')
                        ->required()
                        ->maxLength(255),

                    TextInput::make('subject_id')
                        ->label('資源ID')
                        ->numeric(),

                    Textarea::make('description')
                        ->label('描述')
                        ->required(),

                    //                    TextInput::make('subject_type')
                    //                        ->label('資源模型')
                    //                        ->maxLength(255),
                    //                    TextInput::make('event')
                    //                        ->label('事件')
                    //                        ->maxLength(255),

                    Fieldset::make('causer')
                        ->label('修改者')
                        ->relationship('causer')
                        ->schema([

                            TextInput::make('name')
                                ->label('名稱')
                                ->maxLength(255),

                            TextInput::make('email')
                                ->label('電子信箱')
                                ->maxLength(255),
                        ]),

                    //                    TextInput::make('causer_id')
                    //                        ->label('原因編號')
                    //                        ->numeric(),
                    KeyValue::make('properties.old')
                        ->label('修改前')
                        ->hidden(function (callable $get)
                        {
                            return !((isset($get('properties')['old']) && count($get('properties')['old'])) != 0);
                        })
                        ->default([]),

                    KeyValue::make('properties.attributes')
                        ->label('修改後')
                        ->hidden(function (callable $get)
                        {
                            return !((isset($get('properties')['old']) && count($get('properties')['old'])) != 0);
                        })
                        ->default([]),
                    //                    TextInput::make('batch_uuid')
                    //                        ->label('批量 UUID')
                    //                        ->maxLength(36),
                ]),

            ]);
    }

    /**
     * @throws \Exception
     */
    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('id')->label('序號'),
                TextColumn::make('log_name')->label('被影響的資源'),
                TextColumn::make('description')->label('描述'),
                //                //                TextColumn::make('subject_type')->label('主題'),
                //                TextColumn::make('event')->label('行為'),
                //                TextColumn::make('subject_id')->label('主題編號'),
                TextColumn::make('causer.name')->label('修改者'),
                TextColumn::make('created_at')->label('記錄時間'),
                //                TextColumn::make('properties')->label('特性'),
                //                TextColumn::make('batch_uuid')->label('Batch UUID'),
            ])
            ->filters([

                SelectFilter::make('month')
                    ->label('月份')
                    ->options([
                        1 => '一月',
                        2 => '二月',
                        3 => '三月',
                        4 => '四月',
                        5 => '五月',
                        6 => '六月',
                        7 => '七月',
                        8 => '八月',
                        9 => '九月',
                        10 => '十月',
                        11 => '十一月',
                        12 => '十二月',
                    ])
                    ->default(now()->month),
            ])
            ->actions([
                self::getActionView(),

                //                Tables\Actions\EditAction::make(),
            ])
            ->paginated([5, 10, 25, 50, 100])
            ->defaultPaginationPageOption(10);
    }

    public static function getPages(): array
    {
        return [
            'index' => ListActivityLogs::route('/'),
            //            'create' => Pages\CreateActivityLog::route('/create'),
            //            'edit' => Pages\EditActivityLog::route('/{record}/edit'),
        ];
    }
}
