<?php

namespace <PERSON><PERSON>org\BaseFilamentPlugin\Filament\Resources\ContactResource\Pages;

use <PERSON><PERSON>org\BaseFilamentPlugin\Enum\EnumContactStatus;
use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ContactResource;
use <PERSON><PERSON>org\BaseFilamentPlugin\Models\Contact;
use Stephenchenorg\BaseFilamentPlugin\Service\ResourceService;
use Filament\Resources\Pages\ViewRecord;

class ViewContact extends ViewRecord
{
    protected static string $resource = ContactResource::class;

    public function mount(string|int $record): void
    {
        parent::mount($record);
        $this->markAsRead($record);
    }


    /**
     * @param $record
     * @return void
     */
    public function markAsRead($record): void
    {
        $record = Contact::query()->findOrFail($record);

        if ($record->status === EnumContactStatus::UNREAD) {
            $record->update([
                'status' => EnumContactStatus::READ,
            ]);
        }
    }


    protected function getActions(): array
    {
        return [
            ResourceService::getRedirectAction($this->getRedirectUrl()),
        ];
    }


    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
