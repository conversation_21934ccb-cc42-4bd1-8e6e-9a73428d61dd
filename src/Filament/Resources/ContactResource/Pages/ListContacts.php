<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ContactResource\Pages;

use Stephenchenorg\BaseFilamentPlugin\Enum\EnumContactType;
use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ContactResource;
use Filament\Actions;
use Filament\Resources\Components\Tab;
use Filament\Resources\Pages\ListRecords;
use Stephenchenorg\BaseFilamentPlugin\Models\Contact;

class ListContacts extends ListRecords
{
    protected static string $resource = ContactResource::class;

    public function mount(): void
    {
        parent::mount();
    }


    public function getTabs(): array
    {
        // 使用 groupBy 查詢現有的類型
        $existingTypes = Contact::query()
            ->distinct()
            ->pluck('type')
            ->toArray();

        // 如果只有一種類型或沒有資料，不顯示 tabs
        if (count($existingTypes) < 2) {
            return [];
        }

        // 如果有兩種或以上類型，先加入「全部」tab
        $tabs = [
            Tab::make('全部')
                ->label('全部')
                ->query(fn($query) => $query),
        ];

        // 為每個存在的類型創建 tab
        foreach ($existingTypes as $type) {
            $tabs[] = Tab::make($type->value)
                ->label($type->getLabel())
                ->query(fn($query) => $query->where('type', $type->value));
        }

        return $tabs;
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }


}
