<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ContactResource\Pages;

use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ContactResource;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitRedirectToIndex;
use Filament\Resources\Pages\CreateRecord;

class CreateContact extends CreateRecord
{
    use CCTraitRedirectToIndex;

    protected static string $resource = ContactResource::class;

    /**
     * @return string
     */
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
