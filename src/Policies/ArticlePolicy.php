<?php

namespace Stephenchenorg\BaseFilamentPlugin\Policies;

use Stephenchenorg\BaseFilamentPlugin\Models\Admin;
use Illuminate\Database\Eloquent\Model;
use Stephenchenorg\CsCoreFilamentPlugin\Contracts\CCInterfaceHasPermissionName;

final class ArticlePolicy implements CCInterfaceHasPermissionName
{
    /**
     * Determine whether the Admin can view any Article.
     */
    public function viewAny(Admin $admin): bool
    {
        return $admin->hasPermissionTo('view-any Article');
    }

    /**
     * Determine whether the Admin can view the article.
     */
    public function view(Admin $admin, Model $model): bool
    {
        return $admin->hasPermissionTo('view Article');
    }

    /**
     * Determine whether the Admin can create Article.
     */
    public function create(Admin $admin): bool
    {
        return $admin->hasPermissionTo('create Article');
    }

    /**
     * Determine whether the Admin can update the article.
     */
    public function update(Admin $admin, Model $model): bool
    {
        return $admin->hasPermissionTo('update Article');
    }

    /**
     * Determine whether the Admin can delete the article.
     */
    public function delete(Admin $admin, Model $model): bool
    {
        return $admin->hasPermissionTo('delete Article');
    }

    /**
     * Determine whether the Admin can restore the article.
     */
    public function restore(Admin $admin, Model $model): bool
    {
        return $admin->hasPermissionTo('restore Article');
    }

    /**
     * Determine whether the Admin can permanently delete the article.
     */
    public function forceDelete(Admin $admin, Model $model): bool
    {
        return $admin->hasPermissionTo('force-delete Article');
    }

    public function getPermissionDisplayName(): string
    {
        return '文章';
    }
}
