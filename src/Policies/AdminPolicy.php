<?php

namespace Stephen<PERSON>org\BaseFilamentPlugin\Policies;

use Stephenchenorg\BaseFilamentPlugin\Models\Admin;
use Stephenchenorg\CsCoreFilamentPlugin\Contracts\CCInterfaceHasPermissionName;

final class AdminPolicy implements CCInterfaceHasPermissionName
{
    /**
     * Determine whether the Admin can view any Admins.
     */
    public function viewAny(Admin $admin): bool
    {
        return $admin->hasPermissionTo('view-any Admin');
    }

    /**
     * Determine whether the Admin can view the Admin.
     */
    public function view(Admin $admin, Admin $record): bool
    {

        if ($record->hasRole('Super Admin')) {
            return false;
        }

        return $admin->hasPermissionTo('view Admin');
    }

    /**
     * Determine whether the Admin can create Admins.
     */
    public function create(Admin $admin): bool
    {
        return $admin->hasPermissionTo('create Admin');
    }

    /**
     * Determine whether the Admin can update the Admin.
     */
    public function update(Admin $admin, Admin $record): bool
    {
        if ($record->hasRole('Super Admin')) {
            return false;
        }

        return $admin->hasPermissionTo('update Admin');
    }

    /**
     * Determine whether the Admin can delete the Admin.
     */
    public function delete(Admin $admin, Admin $record): bool
    {
        if ($record->hasRole('Super Admin')) {
            return false;
        }

        return $admin->hasPermissionTo('delete Admin');
    }

    /**
     * Determine whether the Admin can restore the Admin.
     */
    public function restore(Admin $admin, Admin $record): bool
    {

        if ($record->hasRole('Super Admin')) {
            return false;
        }

        return $admin->hasPermissionTo('restore Admin');
    }

    /**
     * Determine whether the Admin can permanently delete the Admin.
     */
    public function forceDelete(Admin $admin, Admin $record): bool
    {

        if ($record->hasRole('Super Admin')) {
            return false;
        }

        return $admin->hasPermissionTo('force-delete Admin');
    }

    public function getPermissionDisplayName(): string
    {
        return '後台管理者';
    }
}
