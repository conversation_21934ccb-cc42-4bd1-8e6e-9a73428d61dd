<?php

namespace Stephen<PERSON>org\BaseFilamentPlugin\Policies;

use Stephenchenorg\BaseFilamentPlugin\Models\Admin;
use Illuminate\Database\Eloquent\Model;
use Stephenchenorg\CsCoreFilamentPlugin\Contracts\CCInterfaceHasPermissionName;

final class PagePolicy implements CCInterfaceHasPermissionName
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(Admin $admin): bool
    {
        return $admin->hasPermissionTo('update Page');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(Admin $admin, Model $model): bool
    {
        return $admin->hasPermissionTo('update Page');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(Admin $admin): bool
    {
        return $admin->hasPermissionTo('update Page');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(Admin $admin, Model $model): bool
    {
        return $admin->hasPermissionTo('update Page');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(Admin $admin, Model $model): bool
    {
        return $admin->hasPermissionTo('update Page');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(Admin $admin, Model $model): bool
    {
        return $admin->hasPermissionTo('update Page');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(Admin $admin, Model $model): bool
    {
        return $admin->hasPermissionTo('update Page');
    }

    public function getPermissionDisplayName(): string
    {
        return '靜態頁面';
    }
}
