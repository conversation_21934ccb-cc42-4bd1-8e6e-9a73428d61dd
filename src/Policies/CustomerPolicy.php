<?php

namespace Stephen<PERSON>org\BaseFilamentPlugin\Policies;

use Stephenchenorg\BaseFilamentPlugin\Models\Admin;
use Stephenchenorg\BaseFilamentPlugin\Models\Customer;
use Illuminate\Database\Eloquent\Model;
use Stephenchenorg\CsCoreFilamentPlugin\Contracts\CCInterfaceHasPermissionName;

final class CustomerPolicy implements CCInterfaceHasPermissionName
{
    /**
     * Determine whether the Admin can view any Customer.
     */
    public function viewAny(Admin $admin): bool
    {
        return $admin->hasPermissionTo('view-any Customer');
    }

    /**
     * Determine whether the Admin can view the customer.
     */
    public function view(Admin $admin, Model $model): bool
    {
        return $admin->hasPermissionTo('view Customer');
    }

    /**
     * Determine whether the Admin can create Customer.
     */
    public function create(Admin $admin): bool
    {
        return $admin->hasPermissionTo('create Customer');
    }

    /**
     * Determine whether the Admin can update the customer.
     */
    public function update(Admin $admin, Model $model): bool
    {
        return $admin->hasPermissionTo('update Customer');
    }

    /**
     * Determine whether the Admin can delete the customer.
     */
    public function delete(Admin $admin, Model $model): bool
    {
        return $admin->hasPermissionTo('delete Customer');
    }

    /**
     * Determine whether the Admin can restore the customer.
     */
    public function restore(Admin $admin, Model $model): bool
    {
        return $admin->hasPermissionTo('restore Customer');
    }

    /**
     * Determine whether the Admin can permanently delete the customer.
     */
    public function forceDelete(Admin $admin, Model $model): bool
    {
        return $admin->hasPermissionTo('force-delete Customer');
    }

    public function getPermissionDisplayName(): string
    {
        return 'B2C會員';
    }
}
