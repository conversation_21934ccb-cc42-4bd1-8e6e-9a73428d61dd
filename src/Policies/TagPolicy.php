<?php

namespace Stephenchenorg\BaseFilamentPlugin\Policies;

use Stephenchenorg\BaseFilamentPlugin\Models\Admin;
use Illuminate\Database\Eloquent\Model;
use Stephenchenorg\CsCoreFilamentPlugin\Contracts\CCInterfaceHasPermissionName;

final class TagPolicy implements CCInterfaceHasPermissionName
{
    /**
     * Determine whether the Admin can view any Tag.
     */
    public function viewAny(Admin $admin): bool
    {
        return $admin->hasPermissionTo('view-any Tag');
    }

    /**
     * Determine whether the Admin can view the product.
     */
    public function view(Admin $admin, Model $model): bool
    {
        return $admin->hasPermissionTo('view Tag');
    }

    /**
     * Determine whether the Admin can create Tag.
     */
    public function create(Admin $admin): bool
    {
        return $admin->hasPermissionTo('create Tag');
    }

    /**
     * Determine whether the Admin can update the product.
     */
    public function update(Admin $admin, Model $model): bool
    {
        return $admin->hasPermissionTo('update Tag');
    }

    /**
     * Determine whether the Admin can delete the product.
     */
    public function delete(Admin $admin, Model $model): bool
    {
        return $admin->hasPermissionTo('delete Tag');
    }

    /**
     * Determine whether the Admin can restore the product.
     */
    public function restore(Admin $admin, Model $model): bool
    {
        return $admin->hasPermissionTo('restore Tag');
    }

    /**
     * Determine whether the Admin can permanently delete the product.
     */
    public function forceDelete(Admin $admin, Model $model): bool
    {
        return $admin->hasPermissionTo('force-delete Tag');
    }

    public function getPermissionDisplayName(): string
    {
        return '標籤';
    }
}
