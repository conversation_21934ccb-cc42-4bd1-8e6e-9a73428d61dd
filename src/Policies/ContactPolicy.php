<?php

namespace Stephenchenorg\BaseFilamentPlugin\Policies;

use Stephenchenorg\BaseFilamentPlugin\Models\Admin;
use Illuminate\Database\Eloquent\Model;
use Stephenchenorg\CsCoreFilamentPlugin\Contracts\CCInterfaceHasPermissionName;

final class ContactPolicy implements CCInterfaceHasPermissionName
{
    /**
     * Determine whether the Admin can view any  Contact.
     */
    public function viewAny(Admin $admin): bool
    {
        return $admin->hasPermissionTo('view-any Contact');
    }

    /**
     * Determine whether the Admin can view the contact.
     */
    public function view(Admin $admin, Model $model): bool
    {
        return $admin->hasPermissionTo('view Contact');
    }

    /**
     * Determine whether the Admin can create  Contact.
     */
    public function create(Admin $admin): bool
    {
        return $admin->hasPermissionTo('create Contact');
    }

    /**
     * Determine whether the Admin can update the contact.
     */
    public function update(Admin $admin, Model $model): bool
    {
        return $admin->hasPermissionTo('update Contact');
    }

    /**
     * Determine whether the Admin can delete the contact.
     */
    public function delete(Admin $admin, Model $model): bool
    {
        return $admin->hasPermissionTo('delete Contact');
    }

    /**
     * Determine whether the Admin can restore the contact.
     */
    public function restore(Admin $admin, Model $model): bool
    {
        return $admin->hasPermissionTo('restore Contact');
    }

    /**
     * Determine whether the Admin can permanently delete the contact.
     */
    public function forceDelete(Admin $admin, Model $model): bool
    {
        return $admin->hasPermissionTo('force-delete Contact');
    }

    public function getPermissionDisplayName(): string
    {
        return '聯絡人';
    }
}
