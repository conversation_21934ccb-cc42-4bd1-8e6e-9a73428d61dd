<?php

namespace <PERSON><PERSON>org\BaseFilamentPlugin\Policies;

use Stephen<PERSON>org\BaseFilamentPlugin\Models\Admin;
use Stephenchenorg\BaseFilamentPlugin\Models\Client;
use Illuminate\Database\Eloquent\Model;
use Stephenchenorg\CsCoreFilamentPlugin\Contracts\CCInterfaceHasPermissionName;

final class ClientPolicy implements CCInterfaceHasPermissionName
{
    /**
     * Determine whether the Admin can view any Client.
     */
    public function viewAny(Admin $admin): bool
    {
        return $admin->hasPermissionTo('view-any Client');
    }

    /**
     * Determine whether the Admin can view the client.
     */
    public function view(Admin $admin, Model $model): bool
    {
        return $admin->hasPermissionTo('view Client');
    }

    /**
     * Determine whether the Admin can create Client.
     */
    public function create(Admin $admin): bool
    {
        return $admin->hasPermissionTo('create Client');
    }

    /**
     * Determine whether the Admin can update the client.
     */
    public function update(Admin $admin, Model $model): bool
    {
        return $admin->hasPermissionTo('update Client');
    }

    /**
     * Determine whether the Admin can delete the client.
     */
    public function delete(Admin $admin, Model $model): bool
    {
        return $admin->hasPermissionTo('delete Client');
    }

    /**
     * Determine whether the Admin can restore the client.
     */
    public function restore(Admin $admin, Model $model): bool
    {
        return $admin->hasPermissionTo('restore Client');
    }

    /**
     * Determine whether the Admin can permanently delete the client.
     */
    public function forceDelete(Admin $admin, Model $model): bool
    {
        return $admin->hasPermissionTo('force-delete Client');
    }

    public function getPermissionDisplayName(): string
    {
        return 'B2B會員';
    }
}
