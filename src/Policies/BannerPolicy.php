<?php

namespace Stephenchenorg\BaseFilamentPlugin\Policies;

use Stephenchenorg\BaseFilamentPlugin\Models\Admin;
use Illuminate\Database\Eloquent\Model;
use Stephenchenorg\CsCoreFilamentPlugin\Contracts\CCInterfaceHasPermissionName;

final class BannerPolicy implements CCInterfaceHasPermissionName
{
    /**
     * Determine whether the Admin can view any Banner.
     */
    public function viewAny(Admin $admin): bool
    {
        return $admin->hasPermissionTo('view-any Banner');
    }

    /**
     * Determine whether the Admin can view the banner.
     */
    public function view(Admin $admin, Model $model): bool
    {
        return $admin->hasPermissionTo('view Banner');
    }

    /**
     * Determine whether the Admin can create Banner.
     */
    public function create(Admin $admin): bool
    {
        return $admin->hasPermissionTo('create Banner');
    }

    /**
     * Determine whether the Admin can update the banner.
     */
    public function update(Admin $admin, Model $model): bool
    {
        return $admin->hasPermissionTo('update Banner');
    }

    /**
     * Determine whether the Admin can delete the banner.
     */
    public function delete(Admin $admin, Model $model): bool
    {
        return $admin->hasPermissionTo('delete Banner');
    }

    /**
     * Determine whether the Admin can restore the banner.
     */
    public function restore(Admin $admin, Model $model): bool
    {
        return $admin->hasPermissionTo('restore Banner');
    }

    /**
     * Determine whether the Admin can permanently delete the banner.
     */
    public function forceDelete(Admin $admin, Model $model): bool
    {
        return $admin->hasPermissionTo('force-delete Banner');
    }

    public function getPermissionDisplayName(): string
    {
        return '橫幅廣告';
    }
}
