<?php

namespace Stephenchenorg\BaseFilamentPlugin\Policies;


use Stephenchenorg\BaseFilamentPlugin\Models\Admin;
use Spatie\Permission\Models\Role;
use Stephenchenorg\CsCoreFilamentPlugin\Contracts\CCInterfaceHasPermissionName;

final class RolePolicy implements CCInterfaceHasPermissionName
{
    /**
     * Determine whether the Admin can view any Admins.
     */
    public function viewAny(Admin $admin): bool
    {
        return $admin->hasPermissionTo('view-any Role');
    }

    /**
     * Determine whether the Admin can view the Admin.
     */
    public function view(Admin $admin, Role $role): bool
    {
        if ($role->name === 'Super Admin') {
            return false;
        }

        return $admin->hasPermissionTo('view Role');
    }

    /**
     * Determine whether the Admin can create Admins.
     */
    public function create(Admin $admin): bool
    {
        return $admin->hasPermissionTo('create Role');
    }

    /**
     * Determine whether the Admin can update the Admin.
     */
    public function update(Admin $admin, Role $role): bool
    {

        if ($role->name === 'Super Admin') {
            return false;
        }


        return $admin->hasPermissionTo('update Role');
    }

    /**
     * Determine whether the Admin can delete the Admin.
     */
    public function delete(Admin $admin, Role $role): bool
    {

        if ($role->name === 'Super Admin') {
            return false;
        }


        return $admin->hasPermissionTo('delete Role');
    }

    /**
     * Determine whether the Admin can restore the Admin.
     */
    public function restore(Admin $admin, Role $role): bool
    {
        if ($role->name === 'Super Admin') {
            return false;
        }

        return $admin->hasPermissionTo('restore Role');
    }

    /**
     * Determine whether the Admin can permanently delete the Admin.
     */
    public function forceDelete(Admin $admin, Role $role): bool
    {

        if ($role->name === 'Super Admin') {
            return false;
        }

        return $admin->hasPermissionTo('force-delete Role');
    }


    public function getPermissionDisplayName(): string
    {
        return '角色';
    }
}
