<?php

namespace Stephenchenorg\BaseFilamentPlugin\Policies;

use Stephenchenorg\BaseFilamentPlugin\Models\Admin;
use Illuminate\Database\Eloquent\Model;
use Stephenchenorg\CsCoreFilamentPlugin\Contracts\CCInterfaceHasPermissionName;

final class BrandPolicy implements CCInterfaceHasPermissionName
{
    /**
     * Determine whether the Admin can view any  Brand.
     */
    public function viewAny(Admin $admin): bool
    {
        return $admin->hasPermissionTo('view-any Brand');
    }

    /**
     * Determine whether the Admin can view the brand.
     */
    public function view(Admin $admin, Model $model): bool
    {
        return $admin->hasPermissionTo('view Brand');
    }

    /**
     * Determine whether the Admin can create  Brand.
     */
    public function create(Admin $admin): bool
    {
        return $admin->hasPermissionTo('create Brand');
    }

    /**
     * Determine whether the Admin can update the brand.
     */
    public function update(Admin $admin, Model $model): bool
    {
        return $admin->hasPermissionTo('update Brand');
    }

    /**
     * Determine whether the Admin can delete the brand.
     */
    public function delete(Admin $admin, Model $model): bool
    {
        return $admin->hasPermissionTo('delete Brand');
    }

    /**
     * Determine whether the Admin can restore the brand.
     */
    public function restore(Admin $admin, Model $model): bool
    {
        return $admin->hasPermissionTo('restore Brand');
    }

    /**
     * Determine whether the Admin can permanently delete the brand.
     */
    public function forceDelete(Admin $admin, Model $model): bool
    {
        return $admin->hasPermissionTo('force-delete Brand');
    }

    public function getPermissionDisplayName(): string
    {
        return '品牌';
    }
}
