<?php

namespace Stephenchenorg\BaseFilamentPlugin\Policies;

use Stephenchenorg\BaseFilamentPlugin\Models\Admin;
use Illuminate\Database\Eloquent\Model;
use Stephenchenorg\CsCoreFilamentPlugin\Contracts\CCInterfaceHasPermissionName;

final class CompanySettingPolicy implements CCInterfaceHasPermissionName
{
    /**
     * Determine whether the Admin can view any models.
     */
    public function viewAny(Admin $admin): bool
    {
        return $admin->hasPermissionTo('view-any CompanySetting');
    }

    /**
     * Determine whether the Admin can view the model.
     */
    public function view(Admin $admin, Model $model): bool
    {
        return $admin->hasPermissionTo('view CompanySetting');
    }

    /**
     * Determine whether the Admin can create models.
     */
    public function create(Admin $admin): bool
    {
        return $admin->hasPermissionTo('create CompanySetting');
    }

    /**
     * Determine whether the Admin can update the model.
     */
    public function update(Admin $admin, Model $model): bool
    {
        return $admin->hasPermissionTo('update CompanySetting');
    }

    /**
     * Determine whether the Admin can delete the model.
     */
    public function delete(Admin $admin, Model $model): bool
    {
        return $admin->hasPermissionTo('delete CompanySetting');
    }

    /**
     * Determine whether the Admin can restore the model.
     */
    public function restore(Admin $admin, Model $model): bool
    {
        return $admin->hasPermissionTo('restore CompanySetting');
    }

    /**
     * Determine whether the Admin can permanently delete the model.
     */
    public function forceDelete(Admin $admin, Model $model): bool
    {
        return $admin->hasPermissionTo('force-delete CompanySetting');
    }

    public function getPermissionDisplayName(): string
    {
        return '公司設定';
    }
}
