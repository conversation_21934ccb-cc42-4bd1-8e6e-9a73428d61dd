<?php

namespace <PERSON>org\BaseFilamentPlugin\Policies;

use <PERSON>tie\Permission\Models\Permission;
use <PERSON><PERSON>or<PERSON>\BaseFilamentPlugin\Models\Admin;
use <PERSON><PERSON>org\CsCoreFilamentPlugin\Contracts\CCInterfaceHasPermissionName;


final class PermissionPolicy implements CCInterfaceHasPermissionName
{
    /**
     * Determine whether the Permission can view any Permissions.
     */
    public function viewAny(Admin $admin): bool
    {
        return $admin->hasPermissionTo('view-any Permission');
    }

    /**
     * Determine whether the Permission can view the Permission.
     */
    public function view(Admin $admin, Permission $permission): bool
    {
        return $admin->hasPermissionTo('view Permission');
    }

    /**
     * Determine whether the Permission can create Permissions.
     */
    public function create(Admin $admin): bool
    {
        return $admin->hasPermissionTo('create Permission');
    }

    /**
     * Determine whether the Permission can update the Permission.
     */
    public function update(Admin $admin, Permission $permission): bool
    {
        return $admin->hasPermissionTo('update Permission');
    }

    /**
     * Determine whether the Permission can delete the Permission.
     */
    public function delete(Admin $admin, Permission $permission): bool
    {
        return $admin->hasPermissionTo('delete Permission');
    }

    /**
     * Determine whether the Permission can restore the Permission.
     */
    public function restore(Admin $admin, Permission $permission): bool
    {
        return $admin->hasPermissionTo('restore Permission');
    }

    public function getPermissionDisplayName(): string
    {
        return '權限';
    }
}
