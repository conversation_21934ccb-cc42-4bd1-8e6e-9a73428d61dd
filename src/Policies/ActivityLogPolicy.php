<?php

namespace <PERSON><PERSON>org\BaseFilamentPlugin\Policies;

use <PERSON><PERSON>org\BaseFilamentPlugin\Models\ActivityLog;
use Stephenchenorg\BaseFilamentPlugin\Models\Admin;
use Illuminate\Database\Eloquent\Model;
use Stephenchenorg\CsCoreFilamentPlugin\Contracts\CCInterfaceHasPermissionName;

final class ActivityLogPolicy implements CCInterfaceHasPermissionName
{
    /**
     * Determine whether the Admin can view any ActivityLog.
     */
    public function viewAny(Admin $admin): bool
    {
        return $admin->hasPermissionTo('view-any ActivityLog');
    }

    /**
     * Determine whether the Admin can view the activity log.
     */
    public function view(Admin $admin, Model $model): bool
    {
        return $admin->hasPermissionTo('view ActivityLog');
    }

    /**
     * Determine whether the Admin can create ActivityLog.
     */
    public function create(Admin $admin): bool
    {
        return $admin->hasPermissionTo('create ActivityLog');
    }

    /**
     * Determine whether the Admin can update the activity log.
     */
    public function update(Admin $admin, Model $model): bool
    {
        return $admin->hasPermissionTo('update ActivityLog');
    }

    /**
     * Determine whether the Admin can delete the activity log.
     */
    public function delete(Admin $admin, Model $model): bool
    {
        return $admin->hasPermissionTo('delete ActivityLog');
    }

    /**
     * Determine whether the Admin can restore the activity log.
     */
    public function restore(Admin $admin, Model $model): bool
    {
        return $admin->hasPermissionTo('restore ActivityLog');
    }

    /**
     * Determine whether the Admin can permanently delete the activity log.
     */
    public function forceDelete(Admin $admin, Model $model): bool
    {
        return $admin->hasPermissionTo('force-delete ActivityLog');
    }

    public function getPermissionDisplayName(): string
    {
        return '活動日誌';
    }
}
