<?php

namespace Stephenchenorg\BaseFilamentPlugin\Policies;

use Stephenchenorg\BaseFilamentPlugin\Models\Admin;
use Illuminate\Database\Eloquent\Model;
use Stephenchenorg\CsCoreFilamentPlugin\Contracts\CCInterfaceHasPermissionName;

final class ProductPolicy implements CCInterfaceHasPermissionName
{
    /**
     * Determine whether the Admin can view any Product.
     */
    public function viewAny(Admin $admin): bool
    {
        return $admin->hasPermissionTo('view-any Product');
    }

    /**
     * Determine whether the Admin can view the product.
     */
    public function view(Admin $admin, Model $model): bool
    {
        return $admin->hasPermissionTo('view Product');
    }


    /**
     * Determine whether the Admin can create Product.
     */
    public function create(Admin $admin): bool
    {
        return $admin->hasPermissionTo('create Product');
    }

    /**
     * Determine whether the Admin can update the product.
     */
    public function update(Admin $admin, Model $model): bool
    {
        return $admin->hasPermissionTo('update Product');
    }

    /**
     * Determine whether the Admin can delete the product.
     */
    public function delete(Admin $admin, Model $model): bool
    {
        return $admin->hasPermissionTo('delete Product');
    }

    /**
     * Determine whether the Admin can restore the product.
     */
    public function restore(Admin $admin, Model $model): bool
    {
        return $admin->hasPermissionTo('restore Product');
    }

    /**
     * Determine whether the Admin can permanently delete the product.
     */
    public function forceDelete(Admin $admin, Model $model): bool
    {
        return $admin->hasPermissionTo('force-delete Product');
    }

    public function getPermissionDisplayName(): string
    {
        return '產品';
    }
}

