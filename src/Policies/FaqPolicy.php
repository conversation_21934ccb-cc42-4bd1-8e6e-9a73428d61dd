<?php

namespace Stephenchenorg\BaseFilamentPlugin\Policies;

use Stephenchenorg\BaseFilamentPlugin\Models\Admin;
use Illuminate\Database\Eloquent\Model;
use Stephenchenorg\CsCoreFilamentPlugin\Contracts\CCInterfaceHasPermissionName;

final class FaqPolicy implements CCInterfaceHasPermissionName
{
    /**
     * Determine whether the Admin can view any Faq.
     */
    public function viewAny(Admin $admin): bool
    {
        return $admin->hasPermissionTo('view-any Faq');
    }

    /**
     * Determine whether the Admin can view the FAQ.
     */
    public function view(Admin $admin, Model $model): bool
    {
        return $admin->hasPermissionTo('view Faq');
    }

    /**
     * Determine whether the Admin can create Faq.
     */
    public function create(Admin $admin): bool
    {
        return $admin->hasPermissionTo('create Faq');
    }

    /**
     * Determine whether the Admin can update the FAQ.
     */
    public function update(Admin $admin, Model $model): bool
    {
        return $admin->hasPermissionTo('update Faq');
    }

    /**
     * Determine whether the Admin can delete the FAQ.
     */
    public function delete(Admin $admin, Model $model): bool
    {
        return $admin->hasPermissionTo('delete Faq');
    }

    /**
     * Determine whether the Admin can restore the FAQ.
     */
    public function restore(Admin $admin, Model $model): bool
    {
        return $admin->hasPermissionTo('restore Faq');
    }

    /**
     * Determine whether the Admin can permanently delete the FAQ.
     */
    public function forceDelete(Admin $admin, Model $model): bool
    {
        return $admin->hasPermissionTo('force-delete Faq');
    }

    public function getPermissionDisplayName(): string
    {
        return '問與答';
    }
}
