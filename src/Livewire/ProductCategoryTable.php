<?php

namespace Stephenchenorg\BaseFilamentPlugin\Livewire;

use Filament\Tables\Actions\ExportAction;
use Filament\Tables\Actions\ImportAction;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\HtmlString;
use Livewire\Attributes\On;
use Livewire\Attributes\Url;
use PowerComponents\LivewirePowerGrid\Button;
use PowerComponents\LivewirePowerGrid\Column;
use PowerComponents\LivewirePowerGrid\Facades\PowerGrid;
use PowerComponents\LivewirePowerGrid\PowerGridComponent;
use PowerComponents\LivewirePowerGrid\PowerGridFields;
use PowerComponents\LivewirePowerGrid\Traits\WithExport;
use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ProductCategoryResource;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductCategory;

final class ProductCategoryTable extends PowerGridComponent
{
    use WithExport;

    public string $tableName = 'product-category-table-rituhy-table';

    public string $sortField = 'sequence';

    public string $sortDirection = 'asc';

    #[Url]
    public ?string $activeTab = null;

    public function setUp(): array
    {

        //$this->showCheckBox();

        return [

            PowerGrid::detail()
                ->view('base-filament-plugin::livewire.power-grid-component.detail-product-row')
                ->showCollapseIcon(),

            PowerGrid::header()
                ->includeViewOnTop('base-filament-plugin::livewire.power-grid-component.product-category-header')
                ->showSearchInput(),

            PowerGrid::footer()
                ->showPerPage()
                ->showRecordCount(),
        ];
    }

    public function datasource(): Builder
    {
        $query = ProductCategory::query();

        // Filter by type if activeTab is set and not 'all'
        if ($this->activeTab && $this->activeTab !== 'all') {
            $query->where('type', $this->activeTab);
        }

        return $query;
    }

    public function relationSearch(): array
    {
        return [
            'translations' => [
                'title',
            ],
            'parentTranslations' => [
                'title',
            ],
        ];
    }

    public function fields(): PowerGridFields
    {
        return PowerGrid::fields()
            ->add('full_name', function (ProductCategory $category) {

                $symbol = $category->countAncestors() > 0 ? '&boxur;' : '';


                $ancestorChain = $category->getAncestors()
                    ->reverse()
                    ->reduce(function ($carry, $item) use ($symbol) {
                        return $carry . $item->name . " &nbsp;—&nbsp;—&nbsp;— ";
                    }, '');

                $name = $category->name;

                return new HtmlString("<span style='margin-inline-end: 0.75rem;'>$ancestorChain<span class='text-primary-500 dark:text-primary-100'>$name</span></span>");

            })
            ->add('category_parent', function (ProductCategory $category) {
                return $ancestorChain = $category->getAncestors()
                    ->reverse()
                    ->reduce(function ($carry, $item) {
                        return $carry . $item->name . " &nbsp;—&nbsp;—&nbsp;— ";
                    }, '');
            })
            ->add('category_name', fn(ProductCategory $category) => $category->name)
            ->add('is_hottest')
            ->add('is_newest')
            ->add('sort');
    }

    public function columns(): array
    {
        return [

            Column::action('Action'),


            // 只用來匯出
            Column::make('parent', 'category_parent')
                ->hidden()
                ->visibleInExport(true),

            Column::make('中文名稱', 'full_name'),

            Column::add()
                ->title('(是/否)熱門')
                ->field('is_hottest')
                ->toggleable(),

            Column::add()
                ->title('(是/否)最新')
                ->field('is_newest')
                ->toggleable(),

            Column::add()
                ->title('狀態')
                ->field('status')
                ->toggleable(),

            Column::make('排序', 'sort')
                ->sortable()
                ->editOnClick(),


        ];
    }

    public function filters(): array
    {
        return [
        ];
    }

    #[\Livewire\Attributes\On('edit')]
    public function edit($rowId): void
    {
        $this->js('alert(' . $rowId . ')');
    }

    #[On('refresh-product-category-table')]
    public function refreshTable(string $activeTab): void
    {
        $this->activeTab = ($activeTab === 'all' || $activeTab === 'All') ? null : $activeTab;
        $this->resetPage();
    }


    public function onUpdatedToggleable(string|int $id, string $field, string $value): void
    {
        $model = ProductCategory::query()->find($id);

        if ($model) {
            $model->{$field} = (int)$value;
            $model->save();
        }

        $this->refresh();
    }

    public function onUpdatedEditable(string|int $id, string $field, string $value): void
    {
        ProductCategory::query()
            ->where('id', '=', $id)
            ->update([
                $field => (int)($value),
            ]);
    }

    public function actions($row): array
    {
        return [

            Button::add('edit')
                ->can(function () use ($row) {
                    return Gate::allows('update', $row);
                })
                ->icon('default-heroicons-o-pencil-square')
                ->class('text-primary-500 font-bold rounded')
                ->dispatchSelf('editCategory', ['id' => $row->id]),

            Button::add('view')
                ->can(function () use ($row) {
                    return Gate::allows('view', $row);
                })
                ->icon('default-heroicons-o-eye')
                ->class('text-gray-400 font-bold rounded')
                ->dispatchSelf('viewCategory', ['id' => $row->id]),

        ];
    }

    #[On('editCategory')]
    public function editCategory(int $id): void
    {
        $this->redirect(ProductCategoryResource::getUrl('edit', [
            'record' => $id
        ]));
    }

    #[On('viewCategory')]
    public function viewCategory(int $id): void
    {
        $this->redirect(ProductCategoryResource::getUrl('view', [
            'record' => $id
        ]));
    }



}
