<?php

namespace Stephenchenorg\BaseFilamentPlugin\Mail\Client;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Attachment;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

final class ClientVerificationEmail extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * @var string
     */
    private string $url;

    /**
     * @var string
     */
    private string $code;

    /**
     * Create a new mailable instance.
     */
    public function __construct(string $code)
    {
        $this->code = $code;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: '[ ' . config('cs.mail_name') . ' ] 註冊信箱驗證碼',
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        $url = 'https://www.google.com';
        return new Content(
            markdown: 'base-filament-plugin::mail.client.email-verify',
            with: [
//                'url'  => $url,
                'code' => $this->code,
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
