<?php

namespace Stephenchenorg\BaseFilamentPlugin\Traits;

use Stephenchenorg\BaseFilamentPlugin\Service\ServiceLanguage;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\ToggleColumn;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitColumn as CCTraitColumnBase;

trait CCTraitColumn
{
    use CCTraitColumnBase;

    /**
     * @return TextColumn
     */
    public static function getColumnTextTitle(): TextColumn
    {
        return TextColumn::make('title')
            ->label('標題')
            ->searchable(true, function ($query, $search)
            {
                $query->where('title', 'like', '%'.$search.'%');
            }, true);
    }

    /**
     * @return TextColumn
     */
    public static function getColumnTextKey(): TextColumn
    {
        return TextColumn::make('key')
            ->label('唯一值')
            ->limit(50)
            ->searchable(true, function ($query, $search)
            {
                $query->where('key', 'like', '%'.$search.'%');
            }, true, false);
    }

    /**
     * @return ToggleColumn
     *
     */
    public static function getColumnTextStatus(): ToggleColumn
    {
        return ToggleColumn::make('status')
            ->label('啟用/停用')
            ->width('100px');
    }

    /**
     * @return TextColumn
     */
    public static function getColumnTextCount(): TextColumn
    {
        return TextColumn::make('count_total')->label('計算總數');
    }

    /**
     * @return TextColumn
     */
    public static function getColumnTextSlug(): TextColumn
    {
        return TextColumn::make('slug')
            ->label('自定義網址')
            ->searchable(true, function ($query, $search)
            {
                $query->where('slug', 'like', '%'.$search.'%');
            }, true)
            ->limit(50);
    }

    /**
     * @return TextColumn
     */
    public static function getColumnTextTag(): TextColumn
    {
        return TextColumn::make('tags')
            ->formatStateUsing(function ($state)
            {
                return $state->translations()->where('lang', ServiceLanguage::getDefaultLanguage())->first()->title;
            })
            ->badge()
            ->label('文章標籤')
            ->searchable()
            ->limit(100);
    }

    /**
     * @return array
     */
    public static function getColumnTextLanguage(): array
    {
        if (!ServiceLanguage::isMultiLanguage()) {
            return [];
        }

        return [
            TextColumn::make('lang')->label('語言')
                ->badge()
                ->formatStateUsing(fn($state) => ServiceLanguage::getLanguageNativeName($state)),
        ];
    }

    /**
     * @return ToggleColumn
     */
    public static function getColumnTextIsHottest(): ToggleColumn
    {
        return ToggleColumn::make('is_hottest')
            ->label('(是/否)熱門')
            ->abbr('勾選熱門的選項 通常會出現在官網顯眼的地方', asTooltip: true)
            ->width('100px');
    }

    /**
     * @return ToggleColumn
     */
    public static function getColumnTextIsNewest(): ToggleColumn
    {
        return ToggleColumn::make('is_newest')
            ->label('(是/否)最新')
            ->abbr('勾選是的選項 通常會出現在官網顯眼的地方', asTooltip: true)
            ->width('100px');
    }



    /**
     * @param  string  $column
     * @param  bool  $searchable
     * @param  string|null  $relation
     * @return TextColumn
     */
    public static function getColumnTranslation(
        string  $column,
        bool    $searchable = false,
        ?string $relation = null,
    ): TextColumn {
        return TextColumn::make($relation ? "$relation.translations.$column" : "translations.$column")
            ->limit(50)
            ->getStateUsing(function ($record) use ($column, $relation)
            {

                if (empty($relation)) {
                    return $record->translations
                        ->where('lang', '=', ServiceLanguage::getDefaultLanguage())
                        ->first()?->$column;
                }

                return $record->$relation
                    ->translations
                    ->where('lang', '=', ServiceLanguage::getDefaultLanguage())
                    ->first()?->$column;

            })
            ->searchable($searchable, function ($query, $search) use ($column)
            {
                $query->whereHas('translations', function ($q) use ($search, $column)
                {
                    $q->where('lang', '=', ServiceLanguage::getDefaultLanguage());
                    $q->whereRaw("LOWER($column) LIKE ?", ['%'.strtolower($search).'%']);
                });
            }, true);
    }
}
