<?php

namespace Stephenchenorg\BaseFilamentPlugin\Traits;

use Illuminate\Database\Schema\Blueprint;

trait CCTraitDatabaseContent
{
    /**
     * @param Blueprint $table
     * @return void
     */
    public function addImageFields(Blueprint $table): void
    {
        // 圖片相關欄位
        $table->string('image', 1024)->nullable()->comment('圖片');
        $table->string('image_mobile', 1024)->nullable()->comment('圖標');
    }

    /**
     * @param Blueprint $table
     * @return void
     */
    public function addTitleField(Blueprint $table): void
    {
        $table->string('title', 100)->nullable()->comment('標題');
    }

    /**
     * @param Blueprint $table
     * @return void
     */
    public function addDescriptionField(Blueprint $table): void
    {
        $table->text('description')->nullable()->comment('純文字描述');
    }

    /**
     * @param Blueprint $table
     * @param bool $multi_content
     * @return void
     */
    public function addMultiContentFields(Blueprint $table, bool $multi_content = false): void
    {
        $table->longText('content_1')->nullable()->comment('內容');
        $table->longText('content_2')->nullable()->comment('內容');
        $table->longText('content_3')->nullable()->comment('內容');
    }

    /**
     * @param Blueprint $table
     * @param bool $multi_content
     * @return void
     */
    public function addContentField(Blueprint $table, bool $multi_content = false): void
    {
        $table->longText('content')->nullable()->comment('內容');
    }

    /**
     * @param Blueprint $table
     * @return void
     */
    public function addKeyField(Blueprint $table): void
    {
        $table->string('key', 50)->unique()->comment('唯一值');
    }

    public function addBackgroundField(Blueprint $table): void
    {
        $table->string('background', 1024)->nullable();
        $table->string('background_blur', 1024)->nullable();
        $table->string('background_mobile', 1024)->nullable();
        $table->string('background_mobile_blur', 1024)->nullable();
    }

    public function addCoverField(Blueprint $table): void
    {
        $table->string('cover', 1024)->nullable();
        $table->string('cover_blur', 1024)->nullable();
        $table->string('cover_mobile', 1024)->nullable();
        $table->string('cover_mobile_blur', 1024)->nullable();
    }

    /**
     * @param Blueprint $table
     * @return void
     */
    public function addSortField(Blueprint $table): void
    {
        $table->unsignedMediumInteger('sort')->default(0)->comment('排序');
    }

    /**
     * @param Blueprint $table
     * @return void
     */
    public function addStatusField(Blueprint $table): void
    {
        $table->unsignedTinyInteger('status')->default(0)->comment('狀態，0 是關閉，1 是開啟');
    }

    /**
     * @param Blueprint $table
     * @return void
     */
    public function addLanguageField(Blueprint $table): void
    {
        $table->string('lang', 10)->comment('語言類別代碼');
    }

    /**
     * @param Blueprint $table
     * @return void
     */
    public function addCountField(Blueprint $table): void
    {
        $table->unsignedInteger('count_total')->comment('計算總數')->default(0);
    }

    /**
     * @param Blueprint $table
     * @return void
     */
    public function addLogoField(Blueprint $table): void
    {
        $table->string('logo', 1024)->comment('logo圖片');
    }

    /**
     * @param Blueprint $table
     * @return void
     */
    public function addIsHottestField(Blueprint $table): void
    {
        $table->boolean('is_hottest');
    }

    /**
     * @param Blueprint $table
     * @return void
     */
    public function addSlugField(Blueprint $table): void
    {
        $table->string('slug', 128); // Slug
    }

    /**
     * @param Blueprint $table
     * @return void
     */
    public function addCtaFields(Blueprint $table): void
    {
        $table->string('cta_title', 100)->nullable()->comment('CTA 標題');
        $table->string('cta_link', 1024)->nullable()->comment('CTA 連結');
    }
}
