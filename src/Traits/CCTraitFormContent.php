<?php

namespace Stephenchenorg\BaseFilamentPlugin\Traits;

use Stephenchenorg\BaseFilamentPlugin\Service\ServiceLanguage;
use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\Tabs\Tab;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Illuminate\Support\Str;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormDate as CCTraitFormDateBase;

trait CCTraitFormContent
{
    use CCTraitFormDateBase;

    /**
     *
     * @return TextInput
     */
    public static function getFormTitle(): TextInput
    {
        return TextInput::make("title")
            ->label("標題")
            ->maxLength(100)
            ->required();
    }

    /**
     *
     * @return Textarea
     */
    public static function getFormDescription(): Textarea
    {
        return Textarea::make("description")
            ->label("純文字內容 (選填)")
            ->maxLength(1024)
            ->helperText('若無設定時 會使用自動產生的內容');
    }

    /**
     * @param bool $isUnique
     * @return TextInput
     */
    public static function getFormKey(bool $isUnique = true): TextInput
    {
        $form = TextInput::make('key')
            ->label('唯一 key')
            ->default(Str::random(8))
            ->placeholder('請輸入英文')
            ->maxLength(30)
            ->helperText('每個語言版本只使用一個唯一的 key，唯一值設計來給程式使用，會默認產生，如想更動請輸入英文字符、數字、破折號及底線的組合。')
            ->rule('regex:/^[a-zA-Z0-9-_]+$/', '只能包含英文字符、數字、破折號及底線')
            ->disabled(fn($record) => $record !== null);

        if ($isUnique) {
            $form = $form->unique(ignoreRecord: true)
                ->required();
        }

        return $form;
    }

    /**
     * @return TextInput
     */
    public static function getFormSlug(): TextInput
    {
        return TextInput::make('slug')
            ->label('自定義網址路徑')
            ->unique(ignoreRecord: true)
            ->default(Str::random(8))
            ->maxLength(128)
            ->required();
    }


    /**
     * @param bool $nullable
     * @return Section
     */
    public static function getFormTime(bool $nullable = true): Section
    {
        return Section::make()
            ->heading('時間設定')
            ->description('如有填寫時間，將會按照時間進行前台的顯示，而下架時間 不填寫則永久有效')
            ->schema([
                self::getFormDate('started_at', '開始日期')
                    ->default(null)
                    ->nullable($nullable),

                self::getFormDate('ended_at', '結束日期')
                    ->default(null)
                    ->nullable($nullable),
            ]);
    }



    /**
     * @param array $schema
     * @param bool $section
     * @return Section
     */
    public static function getTabLanguage(array $schema, bool $section = true): mixed
    {

        $tabs = [];
        foreach (ServiceLanguage::getLanguages() as $lang) {

            $tabs[] = Tab::make($lang)
                ->label(ServiceLanguage::getLanguageNativeName($lang))
                ->schema([

                    Repeater::make($lang)
                        ->relationship('translations', function ($query) use ($lang) {
                            $query->where('lang', $lang);
                        })
                        ->schema([
                            Hidden::make('lang')->default($lang),
                            ...$schema,
                        ])
                        ->afterStateHydrated(function ($state, $set) use ($lang) {
                            if (empty($state)) {
                                $set($lang, [
                                    ['lang' => $lang],
                                ]);
                            }
                        })
                        ->addable(false)
                        ->deletable(false)
                        ->defaultItems(1)
                        ->maxItems(1)
                        ->minItems(1)
                        ->label(''),

                ]);

        }

        if($section)
        {

            return Section::make('languages')
                ->heading('多語系設定')
                ->schema([
                    Tabs::make('translations')
                        ->persistTabInQueryString('tab')
                        ->columnSpanFull()
                        ->schema($tabs),
                ]);

        }

        return Tabs::make('translations')
            ->persistTabInQueryString('tab')
            ->columnSpanFull()
            ->schema($tabs);
    }


    public static function getFormToggle($name): Toggle
    {
        return Toggle::make($name)
            ->onIcon('heroicon-o-check')
            ->offIcon('heroicon-o-x-circle')
            ->inline(false)
            ->default(true)
            ->dehydrateStateUsing(fn ($state) => (int) $state);
    }

}
