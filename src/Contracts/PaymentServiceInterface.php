<?php

namespace Stephenchenorg\BaseFilamentPlugin\Contracts;

use Stephen<PERSON>org\BaseFilamentPlugin\Enum\EnumPaymentMethod;
use Stephenchenorg\BaseFilamentPlugin\Models\Order;

interface PaymentServiceInterface
{
    /**
     * 結帳處理
     *
     * @param Order $order
     * @param EnumPaymentMethod $paymentMethod
     * @return mixed
     */
    public function checkout(Order $order, EnumPaymentMethod $paymentMethod);
}
