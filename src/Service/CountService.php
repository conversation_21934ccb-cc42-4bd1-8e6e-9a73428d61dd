<?php


namespace Stephenchenorg\BaseFilamentPlugin\Service;

use Stephen<PERSON>org\BaseFilamentPlugin\Enum\EnumContactStatus;
use Stephenchenorg\BaseFilamentPlugin\Enum\EnumTagType;
use Stephen<PERSON>org\BaseFilamentPlugin\Models\Article;
use Stephen<PERSON>org\BaseFilamentPlugin\Models\ArticleCategory;
use Stephenchenorg\BaseFilamentPlugin\Models\Banner;
use Stephenchenorg\BaseFilamentPlugin\Models\Contact;
use Stephenchenorg\BaseFilamentPlugin\Models\Faq;
use Stephenchenorg\BaseFilamentPlugin\Models\FaqCategory;
use Stephenchenorg\BaseFilamentPlugin\Models\Product;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductCategory;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductSpecification;
use Stephenchenorg\BaseFilamentPlugin\Models\Tag;

/**
 * Sidebar, dashboard, etc., services for total count calculations
 */
class CountService
{

    /**
     * Get the article category count.
     *
     * @return string
     */
    public static function getArticleCategoryCount(): string
    {
        return (string) ArticleCategory::query()->select('id')->count();
    }

    /**
     * Get the article count.
     *
     * @return string
     */
    public static function getArticleCount(): string
    {
        return (string) Article::query()->select('id')->count();
    }

    /**
     * Get the article tag count.
     *
     * @return string
     */
    public static function getTotalTagCount(): string
    {
        return (string) Tag::query()->select('id')->count();
    }


    /**
     * Get the article tag count.
     *
     * @return string
     */
    public static function getArticleTagCount(): string
    {
        return (string) Tag::query()
            ->where('type', '=', config('cs.share_tag') ? EnumTagType::SHARED : EnumTagType::ARTICLE)
            ->select('*')
            ->count();
    }

    /**
     * Get the banner count.
     *
     * @return string
     */
    public static function getBannerCount(): string
    {
        return (string) Banner::query()->select('id')->count();
    }

    /**
     * Get the FAQ category count.
     *
     * @return string
     */
    public static function getFaqCategoryCount(): string
    {
        return (string) FaqCategory::query()->select('id')->count();
    }

    /**
     * Get the FAQ count.
     *
     * @return string
     */
    public static function getFaqCount(): string
    {
        return (string) Faq::query()->select('id')->count();
    }

    /**
     * Get the product category count.
     *
     * @return string
     */
    public static function getProductCategoryCount(): string
    {
        return (string) ProductCategory::query()->select('id')->count();
    }

    /**
     * Get the product count.
     *
     * @return string
     */
    public static function getProductCount(): string
    {
        return (string) Product::query()->select('id')->count();
    }

    /**
     * Get the product tag count.
     *
     * @return string
     */
    public static function getProductTagCount(): string
    {
        return (string) Tag::query()
            ->where('type', '=', config('cs.share_tag') ? EnumTagType::SHARED : EnumTagType::PRODUCT)
            ->select('*')
            ->count();
    }

    /**
     * Get the product specification count.
     *
     * @return string
     */
    public static function getProductSpecificationCount(): string
    {
        return (string) ProductSpecification::query()->select('id')->count();
    }


    /**
     * Get the contact unread count.
     *
     * @return string
     */
    public static function getContactUnreadCount(): string
    {
        return (string) Contact::query()->select('id')->where('status', '=', EnumContactStatus::UNREAD)->count();
    }

    /**
     * Get the contact read count.
     *
     * @return string
     */
    public static function getContactReadCount(): string
    {
        return (string) Contact::query()->select('id')->where('status', '=', EnumContactStatus::READ)->count();
    }


    /**
     * Get the contact replied count.
     *
     * @return string
     */
    public static function getContactRepliedCount(): string
    {
        return (string) Contact::query()->select('id')->where('status', '=', EnumContactStatus::REPLIED)->count();
    }

    /**
     *
     * @param $key
     * @return string
     */
    public static function getUnit($key): string
    {
        $unitTable = [
            'article_category'      => '種',
            'article'               => '篇',
            'article_tag'           => '種',
            'banner'                => '個',
            'faq_category'          => '種',
            'faq'                   => '個',
            'product_category'      => '種',
            'product'               => '個',
            'product_specification' => '種',
            'product_tag'           => '種',
            'contact_unread'        => '則未讀',
            'contact_read'          => '則已讀',
            'contact_replied'       => '則已回覆',
        ];

        return $unitTable[$key];
    }

}
