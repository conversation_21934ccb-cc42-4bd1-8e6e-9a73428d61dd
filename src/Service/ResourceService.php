<?php

namespace Stephenchenorg\BaseFilamentPlugin\Service;

use Filament\Actions\Action;
use Filament\Support\Enums\IconSize;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ViewAction;

/**
 * 抽出 Resource 共同部分
 */
class ResourceService
{

    /**
     * @param  string  $url
     * @return Action
     */
    public static function getRedirectAction(string $url): Action
    {
        return Action::make('redirect')
            ->label('返回')
            ->url($url);
    }

}
