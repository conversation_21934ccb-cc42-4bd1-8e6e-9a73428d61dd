<?php

namespace Stephenchenorg\BaseFilamentPlugin\Service;

use Closure;
use CodeWithDennis\FilamentSelectTree\SelectTree;
use Filament\Forms\Components\Component;
use Stephenchenorg\BaseFilamentPlugin\Models\Product;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductCategory;

final class ServiceProductCategory
{
    public static function getColumnSelectTree(string $column, Closure|string $type = null, string $relation = null): SelectTree
    {
        return SelectTree::make($column)
            ->label('上一層類別 (可選)')
            ->enableBranchNode()
            ->relationship($relation, 'name', 'parent_id', function ($query, Component $component) use ($type) {
                if (empty($type)) return $query;
                if ($type instanceof Closure) {
                    $type = $component->evaluate($type);
                }
                return $query->where('type', '=', $type);
            })
            ->columnSpanFull()
            ->independent(false)
            ->expandSelected()
            ->parentNullValue(-1)
            ->default(-1);

    }

    public static function getSequence(ProductCategory $productCategory): string
    {
        if ($productCategory->parent_id === -1 || empty($productCategory->parent)) return $productCategory->id;

        return self::getSequence($productCategory->parent) . '-' . $productCategory->id;
    }


    public static function hasProducts(ProductCategory $productCategory): bool
    {
        $categoryIds = $productCategory->descendantsWithSelf()->pluck('id')->toArray();
        return Product::query()->whereIn('product_category_id', $categoryIds)->exists();
    }



    public static function disabledRelatedProducts(ProductCategory $productCategory): void
    {
        $categoryIds = $productCategory->descendantsWithSelf()->where('status', '=', 1)->pluck('id')->toArray();
        Product::query()->whereIn('product_category_id', $categoryIds)->update(['is_all_ancestors_enabled' => 0]);
    }

    public static function enabledRelatedProducts(ProductCategory $productCategory): void
    {
        $descendants = $productCategory->getDescendants()->toTree()->toArray();
        $categoryIds = self::filterAllEnabledCategoriesFromTree($descendants);
        $categoryIds[] = $productCategory->id;

        Product::query()->whereIn('product_category_id', $categoryIds)->update(['is_all_ancestors_enabled' => 1]);
    }

    public static function isAncestorsAllEnabled(ProductCategory $productCategory): bool
    {
        return $productCategory->status === 1 && $productCategory->ancestors()->where('status', '=', 0)->count() === 0;
    }

    /**
     * 先從類別樹中 找出所有合法的路徑 只有該路徑上的類別 其底下產品的is_all_ancestors_enabled會變成 1
     * 找出所有 categoryIds 一次update所有product 而不用一個一個product檢查其合法性
     *
     * @param array $nodes
     * @return array
     */
    public static function filterAllEnabledCategoriesFromTree(array $nodes): array
    {
        $ids = [];

        foreach ($nodes as $node) {
            if ($node['status'] != 1) {
                continue; // 節點無效，整個分支跳過
            }


            $ids[] = $node['id'];

            $children = $node['children'] ?? [];
            $childIds = self::filterAllEnabledCategoriesFromTree($children);

            $ids = array_merge($ids, $childIds);
        }

        return $ids;
    }


}
