<?php

namespace Stephenchenorg\BaseFilamentPlugin\Service;

use Exception;
use Illuminate\Support\Facades\Storage;
use Stephenchenorg\CsCoreFilamentPlugin\CCUtility;

class ServiceStorage
{
    public static string $defaultDir = 'image/default';
    public static string $defaultLogo = 'default_logo.png';
    public static string $defaultImage = 'default_image.jpg';

    /**
     * Copy all files from a source directory to a target directory on the specified disk.
     *
     * @param  string  $sourcePath  源文件路徑
     * @param  string  $targetPath  目標文件路徑
     * @return void
     */
    public static function copyServerDirectory(string $sourcePath, string $targetPath): void
    {
        $disk = Storage::disk(CCUtility::isLocal() ? 'local' : config('filament.default_filesystem_disk'));

        if (!$disk->exists($targetPath)) {
            $disk->makeDirectory($targetPath);
        }

        $files = scandir($sourcePath);
        foreach ($files as $file) {
            if ($file === '.' || $file === '..') {
                continue;
            }

            $fullSourcePath = $sourcePath.DIRECTORY_SEPARATOR.$file;
            $fullTargetPath = $targetPath.'/'.$file;

            if (is_file($fullSourcePath)) {
                $disk->put($fullTargetPath, file_get_contents($fullSourcePath));
            } elseif (is_dir($fullSourcePath)) {
                self::copyServerDirectory($fullSourcePath, $fullTargetPath);
            }
        }
    }

    public static function checkFileExist(string $path): bool
    {
        $disk = Storage::disk(config('filament.default_filesystem_disk'));
        return $disk->exists($path);
    }

    public static function commitTempFile(string $tempPath, string $targetPath): void
    {
        $disk = Storage::disk(config('filament.default_filesystem_disk'));

        // 檢查檔案是否存在於 temp 資料夾
        if (!$disk->exists($tempPath)) {
            throw new Exception('找不到圖片');
        }

        $disk->move($tempPath, $targetPath);
    }


    /**
     * 產生系統預設圖片
     *
     * @return void
     */
    public static function buildDefaultImage(): void
    {
        $sourcePath = __DIR__ . '/../../database/seeders/images';
        ServiceStorage::copyServerDirectory($sourcePath, self::$defaultDir);
    }

    public static function getDefaultImagePath(): string
    {
        $dir = self::$defaultDir;
        $name = self::$defaultImage;
        return "$dir/$name";
    }

    public static function getDefaultLogoPath(): string
    {
        $dir = self::$defaultDir;
        $name = self::$defaultLogo;
        return "$dir/$name";
    }


}
