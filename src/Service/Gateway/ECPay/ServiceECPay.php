<?php

namespace <PERSON><PERSON>org\BaseFilamentPlugin\Service\Gateway\ECPay;

use Exception;
use Stephenchenorg\BaseFilamentPlugin\Models\Order;
use Stephenchenorg\BaseFilamentPlugin\Enum\EnumPaymentMethod;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceLanguage;
use Stephenchenorg\BaseFilamentPlugin\Contracts\PaymentServiceInterface;

/**
 * ECPay 付款服務
 */
class ServiceECPay implements PaymentServiceInterface
{
    protected CustomCheckout $checkout;

    public function __construct(CustomCheckout $checkout)
    {
        $this->checkout = $checkout;
    }

    /**
     * 結帳處理
     *
     * @param Order $order
     * @param EnumPaymentMethod $paymentMethod
     * @return mixed
     * @throws Exception
     */
    public function checkout(Order $order,EnumPaymentMethod $paymentMethod)
    {
        try {
            // 取得語言代碼
            $languageCode = $this->getLanguageCode();

            // 準備商品項目資料
            $displayName = [];
            foreach ($order->items as $orderItem) {
                $displayName[] = $orderItem->title . 'x' .  (int)$orderItem->quantity;
            }
            $itemName = implode('#', $displayName);


            // 準備表單資料
            $formData = [
                'UserId' => $order->orderable_id, // 用戶ID, Optional
                'OrderId' => $order->order_key,
                'ItemDescription' => $this->getOrderDescription($order, $languageCode), // 訂單描述
                'ItemName' => $itemName,
                'TotalAmount' => (int)$order->total_amount_taxed,
                'PaymentMethod' => $paymentMethod->toECPayValue(), // 轉換為 ECPay 格式
            ];

            return $this->checkout->setPostData($formData)->send();

        } catch (Exception $e) {
            throw new Exception('ECPay 訂單建立失敗：' . $e->getMessage());
        }
    }



    /**
     * 取得語言代碼
     *
     * @return string
     */
    private function getLanguageCode(): string
    {
        $request = request();

        if ($request && $request->hasHeader('Content-Language')) {
            return $request->header('Content-Language');
        }

        return ServiceLanguage::getDefaultLanguage();
    }

    /**
     * 取得訂單描述
     *
     * @param Order $order
     * @param string $languageCode
     * @return string
     */
    private function getOrderDescription(Order $order, string $languageCode): string
    {
        $itemCount = $order->items->count();
        $firstItem = $order->items->first();

        if ($itemCount === 1) {
            return $firstItem->title;
        }

        return $firstItem->title . " 等 {$itemCount} 種商品";
    }

}
