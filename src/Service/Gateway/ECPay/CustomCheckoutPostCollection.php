<?php

namespace Stephenchenorg\BaseFilamentPlugin\Service\Gateway\ECPay;

use Exception;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use TsaiYiHua\ECPay\Collections\CheckoutPostCollection;
use TsaiYiHua\ECPay\Collections\InvoicePostCollection;
use TsaiYiHua\ECPay\Services\StringService;
use TsaiYiHua\ECPay\Validations\InstallmentValidation;
use TsaiYiHua\ECPay\Validations\ItemValidation;
use TsaiYiHua\ECPay\Validations\PeriodAmountValidator;

class CustomCheckoutPostCollection extends Collection
{
    public $merchantId;
    public $attributes;
    public $notifyUrl;
    public $returnUrl;
    public $paymentInfoUrl;

    public function __construct()
    {
        parent::__construct();
        $this->merchantId = config('ecpay.MerchantId');
        $this->notifyUrl = route('ecpay.notify');
        $this->returnUrl = route('ecpay.return');
        $this->paymentInfoUrl = route('ecpay.paymentInfo');
    }

    /**
     * @param array $formData
     * @return $this
     */
    public function setData(array $formData)
    {
        $this->attributes = $formData;
        return $this;
    }

    /**
     * @return $this
     * @throws Exception
     */
    public function setBasicInfo()
    {
        if (empty($this->attributes)) {
            throw new Exception('attributes must be set');
        }
        $this->put('MerchantID', $this->merchantId);
        $this->put('MerchantTradeDate', $this->attributes['MerchantTradeDate'] ?? Carbon::now()->format('Y/m/d H:i:s'));
        $this->put('PaymentType', 'aio');
        $this->put('ReturnURL', $this->notifyUrl);
        $this->put('OrderResultURL', $this->returnUrl);
        $this->put('PaymentInfoURL', $this->paymentInfoUrl);
        $this->put('ChoosePayment', $this->attributes['PaymentMethod']);
        $this->put('EncryptType' ,1); // 一律使用 SHA256 加密

        return $this;
    }

    /**
     * @return $this
     * @throws Exception
     */
    public function setOrderInfo()
    {
        if (empty($this->attributes)) {
            throw new Exception('attributes must be set');
        }
        if (isset($this->attributes['ItemName']) && isset($this->attributes['TotalAmount'])) {
            $itemNamePayment = $this->attributes['ItemName'];
            $amount = $this->attributes['TotalAmount'];
        } else {
            if (!isset($this->attributes['Items'])) {
                throw new Exception('Items attribute must be set while ItemName is empty (for multi-item)');
            }
            $itemValidation = new ItemValidation;
            $items = $this->attributes['Items'];
            $validateItem = $itemValidation->ItemValidation($items);
            if ($validateItem->count() > 0) {
                throw new Exception($validateItem->getMessageBag());
            }
            $amount = 0;
            $displayName = [];
            foreach ($items as $item) {
                $displayName[] = $item['name'] . 'x' . $item['qty'];
                $amount += $item['qty'] * $item['price'];
            }
            $itemNamePayment = implode('#', $displayName);

            if (strlen($itemNamePayment) > 200) {
                throw new Exception('Composed Item Name can not more then 200 characters');
            }
        }
        $this->put('MerchantTradeNo', $this->attributes['OrderId'] ?? StringService::identifyNumberGenerator('O'));
        $this->put('TotalAmount' ,$amount);
        $this->put('TradeDesc' , urlencode($this->attributes['ItemDescription']));
        $this->put('ItemName' , $itemNamePayment);
        return $this;
    }

    /**
     * @return $this
     * @throws Exception
     */
    public function setOptionalInfo()
    {
        if (empty($this->attributes)) {
            throw new Exception('attributes must be set');
        }
        $optionParams = [
            'StoreId', 'ClientBackURL', 'ItemURL', 'Remark', 'ChooseSubPayment',
            'NeedExtraPaidInfo', 'IgnorePayment', 'PlatformID', 'InvoiceMark', 'CustomField1',
            'CustomField2', 'CustomField3', 'CustomField4', 'ExpireDate', 'PaymentInfoURL', 'ClientRedirectURL',
            'Language'
        ];
        foreach($optionParams as $param) {
            if (isset($this->attributes[$param]) && !empty($this->attributes[$param])) {
                $this->put($param, $this->attributes[$param]);
            }
        }
        return $this;
    }

    /**
     * @return $this
     * @throws Exception
     */
    public function optimize()
    {
        if (empty($this->attributes)) {
            throw new Exception('attributes must be set');
        }
        if ($this->attributes['PaymentMethod'] == 'Credit' || $this->attributes['PaymentMethod'] == 'ALL') {
            /** 欲使用 BindingCard、MerchantMemberID 這兩個參數功能,特店必須有會員系統。 */
            if (isset($this->attributes['UserId'])) {
                $this->put('BindingCard',
                    (isset($this->attributes['BindingCard'])) ? $this->attributes['BindingCard'] : 0
                );
                if (isset($this->attributes['PlatformID'])) {
                    $this->put('MerchantMemberID', $this->attributes['PlatformID'] . $this->attributes['UserId']);
                } else {
                    $this->put('MerchantMemberID', $this->merchantId . $this->attributes['UserId']);
                }
            }
            if (isset($this->attributes['CreditInstallment'])) {
                $this->put('CreditInstallment', $this->attributes['CreditInstallment']);
            } else {
                if (isset($this->attributes['Redeem'])) {
                    $this->put('Redeem', $this->attributes['Redeem']);
                }
                if (config('app.env') == 'production') {
                    $this->put('UnionPay', (isset($this->attributes['UnionPay'])) ? $this->attributes['UnionPay'] : 0);
                }
            }
        }
        return $this;
    }

    /**
     * @param string $installmentData
     * @return $this;
     * @throws Exception
     */
    public function setInstallment(string $installmentData)
    {
        $validator = InstallmentValidation::installmentValidator($installmentData);
        if ($validator->fails()) {
            throw new Exception($validator->getMessageBag());
        }
        $this->put('CreditInstallment', $installmentData);
        return $this;
    }

    /**
     * @param array $data
     * @return $this
     * @throws Exception
     */
    public function setPeriodAmount(array $data)
    {
        $validator = PeriodAmountValidator::periodAmtValidator($data);
        if ($validator->fails()) {
            throw new Exception($validator->getMessageBag());
        }
        $params = [
            'PeriodAmount', 'PeriodType', 'Frequency', 'ExecTimes', 'PeriodReturnURL'
        ];
        if ($data['PeriodAmount'] != $this->get('TotalAmount')) {
            throw new Exception('PeriodAmount must the same as TotalAmount');
        }
        foreach($params as $param) {
            if (isset($data[$param]) && !empty($data[$param])) {
                $this->put($param, $data[$param]);
            }
        }
        return $this;
    }

    /**
     * @param array $data
     * @return $this
     * @throws Exception
     */
    public function setInvoice(array $data)
    {
        $invPostData = new InvoicePostCollection;
        $invPostData->setData($data)->setPostDataForCheckout()->each(function($invData, $key) {
            $this->put($key, $invData);
        });
        return $this;
    }
}
