<?php

namespace Stephenchenorg\BaseFilamentPlugin\Service;

use Stephen<PERSON>org\BaseFilamentPlugin\Contracts\ServiceProductSpecificationInterface;
use Stephenchenorg\BaseFilamentPlugin\Models\Product;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductAttributeItem;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductSpecification;

class ServiceProductSpecification implements ServiceProductSpecificationInterface
{
    public function getCombinationName(?string $combinationKey, string $lang = null): ?string
    {
        if(empty($combinationKey)) return null;

        $attributeItemIds = explode('-', $combinationKey);

        $items = ProductAttributeItem::query()
            ->with([
                'translations' => function ($query) use ($lang) {
                    $query->where('lang', '=', $lang ?? ServiceLanguage::getDefaultLanguage());
                }
            ])
            ->whereIn('id', $attributeItemIds)
            ->get();

        return implode(", ", $items->map(function ($item) {
            return $item->translations->first()->title;
        })->toArray());
    }

    public function validateCombinationKey(string $combinationKey, Product $product): bool
    {
        $attributesLen = $product->attributes->count();
        $combinationKeyLen = count(explode('-', $combinationKey));

        if ($attributesLen !== $combinationKeyLen) return false;

        $specCount = $product
            ->specifications
            ->where('combination_key', $combinationKey)
            ->count();

        if($specCount > 1) return false;

        return true;
    }


    public function getSellingPrice(ProductSpecification $productSpecification): float
    {
        return $productSpecification->selling_price;
    }

    public function getListingPrice(ProductSpecification $productSpecification): float
    {
        return $productSpecification->listing_price;
    }


}
