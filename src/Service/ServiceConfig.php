<?php

namespace Stephenchenorg\BaseFilamentPlugin\Service;

use Althinect\FilamentSpatieRolesPermissions\FilamentSpatieRolesPermissionsPlugin;
use Stephenchenorg\Audispa\AudispaPlugin;
use Stephenchenorg\PowenArt\PowenArtPlugin;
use Stephenchenorg\Pfl\PflPlugin;

final class ServiceConfig
{
    static public function getPlugins(): array
    {
        $plugins = [
            FilamentSpatieRolesPermissionsPlugin::make(),
        ];

        // 是否是 紅野畫廊
        if (self::isPowenArt()) {
            $plugins[] = PowenArtPlugin::make();
        }

        // 是否是 AudiSpa
        if (self::isAudiSpa()) {
            $plugins[] = AudispaPlugin::make();
        }

        // 是否是 AudiSpa
        if (self::isPfl() || self::isTsutakoro()) {
            $plugins[] = PflPlugin::make();
        }

        return $plugins;
    }


    /**
     * @return bool
     */
    public static function isPowenArt(): bool
    {
        return config('cs.extra_plugin_name') === 'powen.art';
    }

    /**
     * @return bool
     */
    public static function isAudiSpa(): bool
    {
        return config('cs.extra_plugin_name') === 'audispa';
    }

    /**
     * @return bool
     */
    public static function isPfl(): bool
    {
        return config('cs.extra_plugin_name') === 'pfl';
    }

    public static function isTsutakoro(): bool
    {
        return config('cs.extra_plugin_name') === 'tsutakoro';
    }
}
