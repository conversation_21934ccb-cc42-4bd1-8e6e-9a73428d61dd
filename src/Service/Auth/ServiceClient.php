<?php

namespace Stephen<PERSON>org\BaseFilamentPlugin\Service\Auth;

use Illuminate\Contracts\Auth\Authenticatable;
use Stephenchenorg\BaseFilamentPlugin\Enum\EnumVerification;
use Stephenchenorg\BaseFilamentPlugin\Exceptions\ApiErrorException;
use Stephenchenorg\BaseFilamentPlugin\Mail\Client\ClientVerificationEmail;
use Stephenchenorg\BaseFilamentPlugin\Mail\Client\ClientWelcomeRegister;
use Stephenchenorg\BaseFilamentPlugin\Models\Client;
use Stephenchenorg\BaseFilamentPlugin\Models\Customer;
use Stephenchenorg\BaseFilamentPlugin\Models\Order;
use Stephenchenorg\BaseFilamentPlugin\Models\Verification;
use Stephenchenorg\BaseFilamentPlugin\Notifications\Client\ClientForgotPasswordNotification;
use Carbon\Carbon;
use Exception;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;
use Stephenchenorg\CsCoreFilamentPlugin\Services\CCServiceAuthentication;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\Request\CCTraitRequestSort;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\Response\CCTraitResponsePaginate;
use Throwable;

final class ServiceClient
{
    use CCTraitRequestSort;
    use CCTraitResponsePaginate;

    const string GUARD_NAME = 'clients';

    /**
     * @var CCServiceAuthentication
     */
    private CCServiceAuthentication $authenticationService;

    /**
     * Create a new instance.
     *
     * @param CCServiceAuthentication $authenticationService
     */
    public function __construct(CCServiceAuthentication $authenticationService)
    {
        $this->authenticationService = $authenticationService;
    }


    /**
     * 註冊前先驗證信箱
     *
     * @param string $email 使用者電子信箱
     * @return void
     * @throws AuthenticationException 當信箱已被註冊時拋出
     * @throws ApiErrorException 當短時間內重複請求時拋出
     */
    public function validateEmail(string $email): void
    {
        $user = Client::query()->firstWhere('email', $email);

        if ($user) {
            throw new AuthenticationException(__('base-filament-plugin::auth.email_already_registered'));
        }

        $cacheKey = 'client_verification_email_throttle:' . $email;

        // 如果快取裡有紀錄，代表剛剛寄過
        if (Cache::has($cacheKey)) {
            throw new ApiErrorException(__('base-filament-plugin::auth.verification_throttle'));
        }

        // 將該 email 之前發送的所有驗證碼都標記為失效
        Verification::query()
            ->where('target', $email)
            ->where('verifiable_type', Client::class)
            ->where('type', EnumVerification::EMAIL->value)
            ->whereNull('verified_at')
            ->update(['expired_at' => Carbon::now()]);

        $code = Str::random(6);
        // 設置過期時間為 30 分鐘後
        $expiresAt = Carbon::now()->addMinutes(30);
        Verification::query()->create([
            'verifiable_id' => 0,
            'verifiable_type' => Client::class,
            'target' => $email,
            'type' => EnumVerification::EMAIL->value,
            'code' => $code,
            'expired_at' => $expiresAt,
        ]);

        // 設置快取，防止頻繁發送
        Cache::put($cacheKey, true, now()->addMinute());

        Mail::to($email)->send(new ClientVerificationEmail($code));
    }

    /**
     * 註冊新用戶
     *
     * @param array $args 註冊參數，包含 email（信箱）、password（密碼）和 code（驗證碼）
     * @return void
     * @throws AuthenticationException 當驗證碼錯誤或過期時拋出
     * @throws Throwable
     */
    public function register(array $args): void
    {
        try {

            DB::beginTransaction();


            $code = $args['code'];
            $first = Verification::query()
                ->where('target', $args['email'])
                ->where('verifiable_type', '=', Client::class)
                ->where('code', $code)
                ->first();

            if (!$first) {
                throw new AuthenticationException(__('base-filament-plugin::auth.verification_code_error'));
            }

            if (!empty($first->verified_at)) {
                throw new AuthenticationException(__('base-filament-plugin::auth.verification_code_used'));
            }

            // 檢查驗證碼是否過期
            if ($first->expired_at && Carbon::now()->isAfter($first->expired_at)) {
                throw new AuthenticationException(__('base-filament-plugin::auth.verification_code_expired'));
            }


            // 檢查信箱是否已被註冊
            $existingUser = Client::query()->firstWhere('email', $args['email']);
            if ($existingUser) {
                throw new AuthenticationException(__('base-filament-plugin::auth.email_already_registered'));
            }

            // 標記驗證碼已被使用
            $first->verified_at = Carbon::now();

            $client = Client::query()->create([
                'email' => $args['email'],
                'password' => $args['password'],
                'email_verified_at' => Carbon::now()->toDateTimeString(),
            ]);

            $client = $this->collectOrders($client);

            $first->verifiable_id = $client->id;
            $first->save();

            DB::commit();

            Mail::to($client->email)->send(new ClientWelcomeRegister($client));

        } catch (Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    /**
     * 用戶登入
     *
     * @param array $args 登入參數，包含 email（信箱）或 phone（手機），以及 password（密碼）
     * @return array 返回包含 token 和 client 資料的陣列
     * @throws AuthenticationException 當信箱/手機或密碼不正確時拋出
     * @throws Throwable
     */
    public function login(array $args): array
    {
        $type = !empty($args['email']) ? 'email' : 'phone';
        $identifier = !empty($args['email']) ? $args['email'] : $args['phone'];

        $attempts[$type] = $identifier;
        $attempts['password'] = $args['password'];

        if ($token = $this->authenticationService->attempt($this::GUARD_NAME, $attempts)) {
            return $this->me($token);
        }


        if ($type === 'email') {
            throw new AuthenticationException(__('base-filament-plugin::auth.email_password_incorrect'));
        } else {
            throw new AuthenticationException(__('base-filament-plugin::auth.phone_password_incorrect'));
        }
    }

    /**
     * 獲取當前登入用戶資訊
     *
     * @param array|null $token transformJWTToken 回傳的格式
     * @param Client|null $client 用戶模型，如果提供則使用此用戶登入
     * @return array 返回包含 token 和 client 資料的陣列
     * @throws AuthenticationException 當用戶未授權時拋出
     */
    public function me(array $token = null, Client $client = null): array
    {
        $client = $client
            ? $this->authenticationService->loginBy($this::GUARD_NAME, $client)
            : $this->authenticationService->getAuthUser($this::GUARD_NAME);

        if (!$client) {
            throw new AuthenticationException(__('base-filament-plugin::auth.not_authorized'));
        }

        $token = $token ?? $this->authenticationService->authenticate($client, $this::GUARD_NAME);

        return [
            'token' => $token,
            'client' => $client,
        ];
    }

    /**
     * 登出並使 token 失效
     *
     * @return bool 登出成功返回 true
     * @throws Exception
     */
    public function logout(): bool
    {
        return $this->authenticationService->logout($this::GUARD_NAME);
    }


    /**
     * 刷新 JWT token
     *
     * @return array 返回新的 token 資訊
     * @throws Exception 當 token 刷新失敗時拋出
     */
    public function refresh(): array
    {
        return $this->authenticationService->refresh($this::GUARD_NAME);
    }


    /**
     * 重設密碼
     *
     * @param array $args 重設密碼參數，包含 old_password（舊密碼）、password（新密碼）和 password_confirmation（確認新密碼）
     * @return void
     * @throws AuthenticationException 當用戶未授權或舊密碼錯誤時拋出
     * @throws Exception
     */
    public function resetPassword($args): void
    {
        $client = $this->authenticationService->getAuthUser($this::GUARD_NAME);

        if (!$client) {
            throw new AuthenticationException('Not authorized');
        }

        // Check old password
        $oldPassword = $args['old_password'];
        if (!Hash::check($oldPassword, $client->password)) {
            throw new AuthenticationException('舊密碼錯誤');
        }

        // Update new password
        $client->password = $args['password'];
        $client->save();

    }

    /**
     * 忘記密碼寄信驗證信箱連結
     *
     * @param array $args 參數，包含 email（電子信箱）
     * @return void
     * @throws ValidationException 當查無此帳號時拋出
     * @throws ApiErrorException 當短時間內重複請求時拋出
     */
    public function readyToForgetPassword($args): void
    {
        $email = $args['email'];
        $client = Client::query()->firstWhere('email', $email);

        if (!$client) {
            throw ValidationException::withMessages([
                'email' => [__('base-filament-plugin::auth.account_not_found')],
            ]);
        }

        $cacheKey = 'client_password_reset_throttle:' . $email;

        // 如果快取裡有紀錄，代表剛剛寄過
        if (Cache::has($cacheKey)) {
            throw new ApiErrorException('請稍後再試，密碼重設信已寄出');
        }

        Cache::put($cacheKey, true, now()->addMinutes(1));
        $token = Password::broker('clients')->createToken($client);
        $client->notify(new ClientForgotPasswordNotification($token));
    }

    /**
     * 忘記密碼重設
     *
     * @param array $args 重設密碼參數，包含 email（電子信箱）、password（新密碼）、password_confirmation（確認新密碼）和 token（重設密碼 token）
     * @return void
     * @throws ApiErrorException 當重設密碼失敗時拋出
     */

    public function forgetPassword($args): void
    {
        $broker = Password::broker('clients');

        $status = $broker->reset(
            Arr::only($args, ['email', 'password', 'password_confirmation', 'token']),
            function (Client $client, string $password) {
                \Illuminate\Support\Facades\Log::info('reset password', [
                    'email' => $client->email,
                    'password' => $password,
                    '$user' => $client->toArray(),
                ]);
                $client->forceFill([
                    'password' => $password,
                ]);

                $client->save();
            }
        );

        if ($status !== Password::PASSWORD_RESET) {
            throw new AuthenticationException(__($status));
        }
    }

    /**
     * @param $args
     * @return Authenticatable
     * @throws AuthenticationException
     */
    public function update($args): Authenticatable
    {
        $client = $this->authenticationService->getAuthUser($this::GUARD_NAME);

        if (!$client) {
            throw new AuthenticationException('Not authorized');
        }

        $client->update($args);
        return $client;
    }


    public function collectOrders(Client $client): Client
    {
        Order::query()
            ->where(function ($query) {
                return $query->whereNull('orderable_type')
                    ->orWhereNull('orderable_id');
            })
            ->where(function ($query) use ($client) {
                return $query
                    ->where(function ($query) use ($client) {
                        return $query
                            ->whereNotNull('phone')
                            ->where('phone', $client->phone ?? null);
                    })
                    ->orWhere(function ($query) use ($client) {
                        return $query
                            ->whereNotNull('email')
                            ->where('email', $client->email ?? null);
                    });
            })
            ->update([
                'orderable_type' => Client::class,
                'orderable_id' => $client->id,
            ]);


        return $client->load('orders');
    }
}
