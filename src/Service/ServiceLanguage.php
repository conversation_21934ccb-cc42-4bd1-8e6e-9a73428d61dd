<?php

namespace Stephenchenorg\BaseFilamentPlugin\Service;

/**
 *  語言設定服務
 */
class ServiceLanguage
{
    /**
     * @var array[]
     */
    public static $dictionary = [
        'zh_TW' => [
            'name'     => 'Chinese (Traditional)', 'script' => 'Hant', 'native' => '繁體中文',
            'regional' => 'zh_TW',
        ],
        'en'    => ['name' => 'English', 'script' => 'Latn', 'native' => 'English', 'regional' => 'en_GB'],
        'ja'    => ['name' => 'Japanese', 'script' => 'Jpan', 'native' => '日本語', 'regional' => 'ja_JP'],
    ];

    /**
     * @param  string  $lang
     * @return string
     */
    public static function getLanguageNativeName(string $lang): string
    {
        return self::$dictionary[$lang]['native'];
    }

    /**
     * @return bool
     */
    public static function isMultiLanguage(): bool
    {
        return count(self::getLanguages()) > 1;
    }

    /**
     * @return array
     */
    public static function getLanguages(): array
    {

        return array_keys(collect(self::$dictionary)
            ->only(config('cs.languages'))
            ->toArray());
    }

    /**
     * @return string
     */
    public static function getDefaultLanguage(): string
    {
        return self::getLanguages()[0] ?? 'zh_TW';
    }
}
