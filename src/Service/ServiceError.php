<?php

namespace Stephenchenorg\BaseFilamentPlugin\Service;

use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Response;
use Illuminate\Validation\ValidationException;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitResponse;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Throwable;
use TypeError;


final class ServiceError
{
    use CCTraitResponse;

    public static function renderApiError(Throwable $e): Response
    {
        // 預設錯誤碼
        $code = $e->getCode() === 0 ?: 500;
        $custom = [];


        if ($e instanceof TypeError) {
            $code = 400;
        }
        if ($e instanceof ValidationException) {
            $code = 422;
            $custom = [
                'validation' => $e->validator->errors()->getMessages()
            ];
        }
        if ($e instanceof ModelNotFoundException | $e instanceof NotFoundHttpException) {
            $code = 404;
        }


        return self::jsonFail($e->getMessage(), $code, $code,$custom);
    }


}
