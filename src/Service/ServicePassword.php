<?php

namespace Stephenchenorg\BaseFilamentPlugin\Service;

use Illuminate\Validation\Rules\Password;

/**
 *  整個專案的驗證機制
 */
final class ServicePassword
{
    /**
     * @return Password
     */
    public static function getPasswordRules(): Password
    {
        return Password::min(5) // 最小字數
        ->max(100)  // 最長100
        ->mixedCase()  // 大小寫
        ->letters()    // 字母
        ->numbers()    // 数字
        ->symbols();    // 符號
    }

    /**
     * @return string
     */
    public static function getPasswordText(): string
    {
        return '長度 5 ~ 100，並且符合1個大小寫字母、1個數字、1個特殊符號';
    }
}
