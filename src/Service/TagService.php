<?php

namespace Stephenchenorg\BaseFilamentPlugin\Service;

use Stephenchenorg\BaseFilamentPlugin\Enum\EnumTagType;
use Stephenchenorg\BaseFilamentPlugin\Models\Tag;
use Stephenchenorg\BaseFilamentPlugin\Traits\CCTraitColumn;
use Stephenchenorg\BaseFilamentPlugin\Traits\CCTraitFormContent;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Tables\Columns\TextColumn;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormAdmin;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormTimestamps;

/**
 *  標籤服務
 */
class TagService
{
    use CCTraitColumn;
    use CCTraitFormAdmin;
    use CCTraitFormTimestamps;
    use CCTraitFormContent;

    /**
     * @return Select
     */
    public static function getArticleTagSelectForm(): Select
    {
        return Select::make('tags')
            ->relationship('tags', null, function ($query)
            {
                return $query->with('translations');
            })
            ->options(function ()
            {
                return Tag::query()
                    ->where('type', config('cs.share_tag') ? EnumTagType::SHARED : EnumTagType::ARTICLE)
                    ->get()
                    ->mapWithKeys(fn($tag) => [
                        $tag->id => $tag->translations->where('lang',
                            ServiceLanguage::getDefaultLanguage())->first()->title,
                    ]);
            })
            ->getOptionLabelFromRecordUsing(fn($record) => $record->translations()->where('lang',
                ServiceLanguage::getDefaultLanguage())->first()->title)
            ->label('標籤')
            ->multiple()
            ->createOptionForm(self::getArticleTagForm());
    }

    /**
     * @return array
     * @throws \Exception
     */
    public static function getArticleTagForm(): array
    {
        return [
            Section::make('basic_settings')
                ->heading('基本設定')
                ->schema([
                    Hidden::make('type')
                        ->default(function ()
                        {
                            return config('cs.share_tag') ? EnumTagType::SHARED : EnumTagType::ARTICLE;
                        }),
                    self::getFormKey(),
                    self::getFormSlug(),
                ]),
            self::getTabLanguage([
                self::getFormTitle(),
            ]),
            self::getFormTimestamps(),
            self::getFormSectionAdminId(),
        ];
    }

    /**
     * @return Select
     */
    public static function getProductTagSelectForm(): Select
    {
        return Select::make('tags')
            ->relationship('tags', null, function ($query)
            {
                return $query->with('translations');
            })
            ->options(function ()
            {
                return Tag::query()
                    ->where('type', config('cs.share_tag') ? EnumTagType::SHARED : EnumTagType::PRODUCT)
                    ->get()
                    ->mapWithKeys(fn($tag) => [
                        $tag->id => $tag->translations->where('lang',
                            ServiceLanguage::getDefaultLanguage())->first()->title,
                    ]);
            })
            ->getOptionLabelFromRecordUsing(fn($record) => $record->translations()->where('lang',
                ServiceLanguage::getDefaultLanguage())->first()->title)
            ->label('標籤')
            ->multiple()
            ->createOptionForm(self::getProductTagForm());
    }

    /**
     * @return array
     * @throws \Exception
     */
    public static function getProductTagForm(): array
    {
        return [
            Section::make('basic_settings')
                ->heading('基本設定')
                ->schema([
                    Hidden::make('type')
                        ->default(function ()
                        {
                            return config('cs.share_tag') ? EnumTagType::SHARED : EnumTagType::PRODUCT;
                        }),
                    self::getFormKey(),
                    self::getFormSlug(),
                ]),
            self::getTabLanguage([
                self::getFormTitle(),
            ]),
            self::getFormTimestamps(),
            self::getFormSectionAdminId(),
        ];
    }

    /**
     * @return array
     */
    public static function getArticleTagTable(): array
    {
        return [
            self::getColumnTextKey(),
            self::getColumnTranslation('title')->label('標題'),
            self::getColumnTextSlug(),
            TextColumn::make('article_count')
                ->label('文章總數'),
        ];
    }

    /**
     * @return array
     */
    public static function getAllTagTable(): array
    {
        return [
            self::getColumnTextKey(),
            self::getColumnTranslation('title')->label('標題'),
            self::getColumnTextSlug(),
            TextColumn::make('article_count')
                ->hidden(function ($livewire)
                {
                    if (empty($livewire->activeTab) || $livewire->activeTab === 'all') {
                        return !config('cs.article_visible');
                    }
                    return $livewire->activeTab !== EnumTagType::ARTICLE->value;
                })
                ->label('文章總數'),
            TextColumn::make('product_count')
                ->hidden(function ($livewire)
                {
                    if (empty($livewire->activeTab) || $livewire->activeTab === 'all') {
                        return !config('cs.product_visible');
                    }
                    return $livewire->activeTab !== EnumTagType::PRODUCT->value;
                })
                ->label('產品總數'),
        ];
    }

    /**
     * @return array
     */
    public static function getProductTagTable(): array
    {
        return [
            self::getColumnTextKey(),
            self::getColumnTranslation('title')->label('標題'),
            self::getColumnTextSlug(),
            TextColumn::make('product_count')
                ->label('產品總數'),
        ];
    }

    /**
     * @return array
     * @throws \Exception
     */
    public static function getAllTagForm(): array
    {
        if (config('cs.share_tag')) {
            return [
                Section::make('basic_settings')
                    ->heading('基本設定')
                    ->schema([
                        Hidden::make('type')
                            ->default(EnumTagType::SHARED),
                        self::getFormKey(),
                        self::getFormSlug(),
                    ]),
                self::getTabLanguage([
                    self::getFormTitle(),
                ]),

                self::getFormTimestamps(),
                self::getFormSectionAdminId(),
            ];
        }


        return [
            Section::make('basic_settings')
                ->heading('基本設定')
                ->schema([
                    Select::make('type')
                        ->label('標籤分類')
                        ->options(collect(EnumTagType::cases())
                            ->filter(function ($case)
                            {
                                if ($case->value === 'article') {
                                    return config('cs.article_visible');
                                }
                                if ($case->value === 'product') {
                                    return config('cs.product_visible');
                                }
                                return $case->value !== EnumTagType::SHARED->value;
                            })
                            ->mapWithKeys(fn($case) => [$case->value => $case->getLabel()])
                            ->toArray())
                        ->required(),
                    self::getFormKey(),
                    self::getFormSlug(),
                ]),
            self::getTabLanguage([
                self::getFormTitle(),
            ]),

            self::getFormTimestamps(),
            self::getFormSectionAdminId(),
        ];
    }


}
