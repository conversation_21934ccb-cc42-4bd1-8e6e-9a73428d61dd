<?php

namespace Stephenchenorg\BaseFilamentPlugin\Service;

use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Illuminate\Validation\Rules\Password;

/**
 *  整個專案的密碼服務
 */
final class PasswordService
{
    /**
     * @return TextInput
     */
    function getPassword(): TextInput
    {
        return TextInput::make('password')
            ->label('密碼')
            ->password()
            ->rule(
                Password::min(5) // 最小字數
                ->max(100)  // 最長100
                ->mixedCase()  // 大小寫
                ->letters()    // 字母
                ->numbers()    // 数字
                ->symbols()    // 符號
            )
            ->revealable(filament()->arePasswordsRevealable())
            ->autocomplete('new-password')
            ->helperText('長度 5 ~ 100，並且符合1個大小寫字母、1個數字、1個特殊符號')
            ->dehydrated(fn($state): bool => filled($state))
            ->live(debounce: 500)
            ->same('passwordConfirmation')
            ->required(fn (string $context) => $context === 'create');
    }

    /**
     * @return TextInput
     */
    function getPasswordConfirmation(): TextInput
    {
        return TextInput::make('passwordConfirmation')
            ->label('確認密碼')
            ->password()
            ->rule(
                Password::min(5) // 最小字數
                ->max(10)  // 最長10
                ->mixedCase()  // 大小寫
                ->letters()    // 字母
                ->numbers()    // 数字
                ->symbols()    // 符號
            )
            ->revealable(filament()->arePasswordsRevealable())
            ->required()
            ->visible(fn(Get $get): bool => filled($get('password')))
            ->dehydrated(false);
    }
}
