<?php

namespace Stephenchenorg\BaseFilamentPlugin\Service;

use Spatie\Activitylog\LogOptions;

class ServiceLogActivity
{
    public static function getDefaultOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->useLogName(__('base-filament-plugin::logger.default'))
            ->logExcept([
                'created_at',
                'updated_at',
                'updated_by_admin_id',
                'created_by_admin_id',
            ])
            ->logOnly(['*'])
            ->setDescriptionForEvent(fn(string $eventName) => __("base-filament-plugin::logger.$eventName"));
    }
}
