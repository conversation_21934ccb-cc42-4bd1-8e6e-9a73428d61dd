<?php

namespace Stephenchenorg\BaseFilamentPlugin\Service\Log;

use Illuminate\Support\Facades\Log;

/**
 *
 */
class LogService
{
    /**
     * 易沛的 callback
     * @param  string  $title
     * @param  array  $context
     * @return void
     */
    static function test(string $title, array $context): void
    {
        Log::channel('test')
            ->info("\n\n----------- $title ----------- \n\n", $context);
    }

    /**
     *
     * @param  string  $title
     * @param  array  $context
     * @return void
     */
    static function apiFlowRequest(array $context, string $title): void
    {
        Log::channel('api-flow-request')
            ->info("\n\n----------- $title ----------- \n\n", $context);
    }
}