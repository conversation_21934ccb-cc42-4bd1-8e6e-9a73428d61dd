<?php

namespace Stephen<PERSON>org\BaseFilamentPlugin\Service;

use Stephenchenorg\BaseFilamentPlugin\Models\ActivityLog;
use Stephenchenorg\BaseFilamentPlugin\Models\Admin;
use Stephenchenorg\BaseFilamentPlugin\Models\Article;
use Stephenchenorg\BaseFilamentPlugin\Models\ArticleCategory;
use Stephenchenorg\BaseFilamentPlugin\Models\Banner;
use Stephenchenorg\BaseFilamentPlugin\Models\Brand;
use Stephenchenorg\BaseFilamentPlugin\Models\Client;
use Stephenchenorg\BaseFilamentPlugin\Models\CompanySetting;
use Stephenchenorg\BaseFilamentPlugin\Models\Contact;
use Stephenchenorg\BaseFilamentPlugin\Models\Customer;
use Stephenchenorg\BaseFilamentPlugin\Models\Faq;
use Stephenchenorg\BaseFilamentPlugin\Models\FaqCategory;
use Stephenchenorg\BaseFilamentPlugin\Models\Page;
use Stephenchenorg\BaseFilamentPlugin\Models\Permission;
use Stephenchenorg\BaseFilamentPlugin\Models\Product;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductCategory;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductSpecification;
use Stephenchenorg\BaseFilamentPlugin\Models\SystemSetting;
use Stephenchenorg\BaseFilamentPlugin\Models\Tag;
use Stephenchenorg\BaseFilamentPlugin\Policies\ActivityLogPolicy;
use Stephenchenorg\BaseFilamentPlugin\Policies\AdminPolicy;
use Stephenchenorg\BaseFilamentPlugin\Policies\ArticlePolicy;
use Stephenchenorg\BaseFilamentPlugin\Policies\BannerPolicy;
use Stephenchenorg\BaseFilamentPlugin\Policies\BrandPolicy;
use Stephenchenorg\BaseFilamentPlugin\Policies\ClientPolicy;
use Stephenchenorg\BaseFilamentPlugin\Policies\CompanySettingPolicy;
use Stephenchenorg\BaseFilamentPlugin\Policies\ContactPolicy;
use Stephenchenorg\BaseFilamentPlugin\Policies\CustomerPolicy;
use Stephenchenorg\BaseFilamentPlugin\Policies\FaqPolicy;
use Stephenchenorg\BaseFilamentPlugin\Policies\PagePolicy;
use Stephenchenorg\BaseFilamentPlugin\Policies\PermissionPolicy;
use Stephenchenorg\BaseFilamentPlugin\Policies\ProductPolicy;
use Stephenchenorg\BaseFilamentPlugin\Policies\RolePolicy;
use Stephenchenorg\BaseFilamentPlugin\Policies\TagPolicy;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Gate;
use Spatie\Permission\Models\Role;

/**
 *  系統設定服務
 */
class ServiceSystem
{
    /**
     * @param $settings
     * @return array
     */
    public static function cacheSystemSettings($settings): array
    {
        $cachedSettings = [];

        foreach ($settings as $key => $value) {
            Cache::put("system_setting_{$key}", $value, now()->addMinutes(60));
            $cachedSettings[$key] = $value;
        }

        return $cachedSettings;
    }

    /**
     * @return bool
     */
    public static function isSeoEnabled(): bool
    {
        $cacheKey = 'system_setting_seo_enabled';

        if (Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        $setting = SystemSetting::query()->firstOrFail();

        Cache::put($cacheKey, $setting->seo_enabled, now()->addMinutes(60));

        return $setting->seo_enabled;
    }

    public static function registerPolicies(): void
    {
        Gate::before(function (Admin $admin, string $ability)
        {
            return $admin->isSuperAdmin() ? true : null;
        });

        // 帳號權限相關
        Gate::policy(Admin::class, AdminPolicy::class);
        Gate::policy(Permission::class, PermissionPolicy::class);
        Gate::policy(Role::class, RolePolicy::class);

        // 靜態頁面相關
        Gate::policy(Page::class, PagePolicy::class);

        // 公司基本設定相關
        Gate::policy(CompanySetting::class, CompanySettingPolicy::class);

        // 聯絡人相關
        if (config('cs.contact_visible')) {
            Gate::policy(Contact::class, ContactPolicy::class);
        }

        // Banner相關
        if (config('cs.banner_visible')) {
            Gate::policy(Banner::class, BannerPolicy::class);
        }

        // Faq相關
        if (config('cs.faq_visible')) {
            Gate::policy(Faq::class, FaqPolicy::class);
            Gate::policy(FaqCategory::class, FaqPolicy::class);
        }

        // 產品相關
        if (config('cs.product_visible')) {
            Gate::policy(Product::class, ProductPolicy::class);
            Gate::policy(ProductCategory::class, ProductPolicy::class);
            Gate::policy(ProductSpecification::class, ProductPolicy::class);
        }

        // 品牌相關
        if (config('cs.brand_visible')) {
            Gate::policy(Brand::class, BrandPolicy::class);
        }

        // 標籤相關
        Gate::policy(Tag::class, TagPolicy::class);

        // 活動日誌相關
        Gate::policy(ActivityLog::class, ActivityLogPolicy::class);

        // 文章相關
        if (config('cs.article_visible')) {
            Gate::policy(ArticleCategory::class, ArticlePolicy::class);
            Gate::policy(Article::class, ArticlePolicy::class);
        }

        // 會員相關
        if (config('cs.customer_visible')) {
            Gate::policy(Customer::class, CustomerPolicy::class);
        }
        if (config('cs.client_visible')) {
            Gate::policy(Client::class, ClientPolicy::class);
        }

    }

}
