<?php

namespace Stephen<PERSON>org\BaseFilamentPlugin\Service;

use Filament\Facades\Filament;
use Filament\Navigation\NavigationItem;
use Filament\Tables\Columns\SelectColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\TextInputColumn;
use Filament\Tables\Columns\ToggleColumn;
use Illuminate\Console\Events\CommandFinished;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Str;
use Livewire\Livewire;
use Spatie\Permission\Models\Role;
use Stephenchenorg\BaseFilamentPlugin\BaseFilamentPluginServiceProvider;
use Stephenchenorg\BaseFilamentPlugin\Contracts\PaymentServiceInterface;
use Stephenchenorg\BaseFilamentPlugin\Contracts\ServiceProductSpecificationInterface;
use Stephenchenorg\BaseFilamentPlugin\Database\Seeders\BaseSeeder;
use Stephenchenorg\BaseFilamentPlugin\Events\ProductUpdated;
use Stephenchenorg\BaseFilamentPlugin\Models\ActivityLog;
use Stephenchenorg\BaseFilamentPlugin\Models\Admin;
use Stephenchenorg\BaseFilamentPlugin\Models\Article;
use Stephenchenorg\BaseFilamentPlugin\Models\ArticleCategory;
use Stephenchenorg\BaseFilamentPlugin\Models\Banner;
use Stephenchenorg\BaseFilamentPlugin\Models\Brand;
use Stephenchenorg\BaseFilamentPlugin\Models\Client;
use Stephenchenorg\BaseFilamentPlugin\Models\CompanySetting;
use Stephenchenorg\BaseFilamentPlugin\Models\Contact;
use Stephenchenorg\BaseFilamentPlugin\Models\Customer;
use Stephenchenorg\BaseFilamentPlugin\Models\Faq;
use Stephenchenorg\BaseFilamentPlugin\Models\FaqCategory;
use Stephenchenorg\BaseFilamentPlugin\Models\Page;
use Stephenchenorg\BaseFilamentPlugin\Models\Permission;
use Stephenchenorg\BaseFilamentPlugin\Models\Product;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductCategory;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductSpecification;
use Stephenchenorg\BaseFilamentPlugin\Models\Tag;
use Stephenchenorg\BaseFilamentPlugin\Observers\AdminFieldsObserver;
use Stephenchenorg\BaseFilamentPlugin\Policies\ActivityLogPolicy;
use Stephenchenorg\BaseFilamentPlugin\Policies\AdminPolicy;
use Stephenchenorg\BaseFilamentPlugin\Policies\ArticlePolicy;
use Stephenchenorg\BaseFilamentPlugin\Policies\BannerPolicy;
use Stephenchenorg\BaseFilamentPlugin\Policies\BrandPolicy;
use Stephenchenorg\BaseFilamentPlugin\Policies\ClientPolicy;
use Stephenchenorg\BaseFilamentPlugin\Policies\CompanySettingPolicy;
use Stephenchenorg\BaseFilamentPlugin\Policies\ContactPolicy;
use Stephenchenorg\BaseFilamentPlugin\Policies\CustomerPolicy;
use Stephenchenorg\BaseFilamentPlugin\Policies\FaqPolicy;
use Stephenchenorg\BaseFilamentPlugin\Policies\PagePolicy;
use Stephenchenorg\BaseFilamentPlugin\Policies\PermissionPolicy;
use Stephenchenorg\BaseFilamentPlugin\Policies\ProductPolicy;
use Stephenchenorg\BaseFilamentPlugin\Policies\RolePolicy;
use Stephenchenorg\BaseFilamentPlugin\Policies\TagPolicy;
use Stephenchenorg\BaseFilamentPlugin\Service\Gateway\ECPay\ServiceECPay;
use Stephenchenorg\CsCoreFilamentPlugin\CCUtility;
use Symfony\Component\Console\Output\ConsoleOutput;

class ServiceRegister
{
    public static function isSameDomain(): bool
    {
        $url = config('app.url');
        return request()->getHost() === CCUtility::getDomain($url);
    }


    public static function registerObservers(): void
    {
        if (!self::isSameDomain()) return;

        // 從配置中獲取觀察者類
        $observerClass = config('cs.observer_class', AdminFieldsObserver::class);
        $excludes = [
            ActivityLog::class,
        ];

        // 註冊 plugin 本身的 model
        $modelPath = __DIR__ . '/../Models/';
        $allFiles = File::allFiles($modelPath);
        foreach ($allFiles as $file) {
            $className = pathinfo($file, PATHINFO_FILENAME);
            $namespace = 'Stephenchenorg\\BaseFilamentPlugin\\Models\\';
            $fullClassName = $namespace . $className;
            if (in_array($fullClassName, $excludes)) {
                continue;
            }
            if (class_exists($fullClassName)) {
                $fullClassName::observe($observerClass);
            }
        }

        // 註冊額外的名單
        $additionalModels = config('cs.additional_observable_models', []);
        foreach ($additionalModels as $modelConfig) {

            if (!isset($modelConfig['path']) || !isset($modelConfig['namespace'])) {
                continue;
            }

            $modelPath = base_path($modelConfig['path']);
            if (!is_dir($modelPath)) {
                continue;
            }

            $allFiles = File::allFiles($modelPath);
            foreach ($allFiles as $file) {
                $className = pathinfo($file, PATHINFO_FILENAME);
                $namespace = $modelConfig['namespace'];
                $fullClassName = $namespace . $className;

                if (in_array($fullClassName, $excludes)) {
                    continue;
                }

                if (class_exists($fullClassName)) {
                    $fullClassName::observe($observerClass);
                }
            }
        }
    }

    /**
     * Register all config files from the configs directory
     */
    public static function registerConfigs(BaseFilamentPluginServiceProvider $serviceProvider): void
    {
        $configsPath = __DIR__ . '/../../config/';

        if (is_dir($configsPath)) {
            $files = scandir($configsPath);

            foreach ($files as $file) {
                $filePath = $configsPath . $file;

                // Skip directories and non-PHP files
                if (is_file($filePath) && pathinfo($file, PATHINFO_EXTENSION) === 'php') {
                    $configName = pathinfo($file, PATHINFO_FILENAME);
                    $serviceProvider->mergeConfigFrom($filePath, $configName);
                }
            }
        }

        self::registerAuthConfig();
    }


    /**
     * 動態設定認證配置
     */
    public static function registerAuthConfig(): void
    {
        $customerModel = config('cs.models.CustomerModel', Customer::class);
        $clientModel = config('cs.models.ClientModel', Client::class);

        // 設定 auth guards
        config([
            'auth.guards.customers' => [
                'driver' => 'jwt',
                'provider' => 'customers',
            ],
            'auth.guards.clients' => [
                'driver' => 'jwt',
                'provider' => 'clients',
            ],
        ]);

        // 設定 auth providers
        config([
            'auth.providers.customers' => [
                'driver' => 'eloquent',
                'model' => $customerModel,
            ],
            'auth.providers.clients' => [
                'driver' => 'eloquent',
                'model' => $clientModel,
            ],
        ]);

        // 設定 auth passwords
        config([
            'auth.passwords.customers' => [
                'provider' => 'customers',
                'table' => env('AUTH_PASSWORD_RESET_TOKEN_TABLE', 'password_reset_tokens'),
                'expire' => 60,
                'throttle' => 60,
            ],
            'auth.passwords.clients' => [
                'provider' => 'clients',
                'table' => env('AUTH_PASSWORD_RESET_TOKEN_TABLE', 'password_reset_tokens'),
                'expire' => 60,
                'throttle' => 60,
            ],
        ]);
    }

    /**
     * Register all livewire files from the Livewire directory
     */
    public static function registerLivewireComponents(): void
    {
        $livewirePath = __DIR__ . '/../Livewire/';
        $baseNamespace = 'Stephenchenorg\\BaseFilamentPlugin\\Livewire\\';

        if (is_dir($livewirePath)) {
            $files = scandir($livewirePath);

            foreach ($files as $file) {
                $filePath = $livewirePath . $file;

                if (is_file($filePath) && pathinfo($file, PATHINFO_EXTENSION) === 'php') {
                    $classBaseName = pathinfo($file, PATHINFO_FILENAME);
                    $alias = Str::kebab($classBaseName);
                    $className = $baseNamespace . $classBaseName;
                    Livewire::component($alias, $className);
                }
            }
        }
    }

    /**
     * Register all policies
     */
    public static function registerPolicies(): void
    {
        Gate::before(function (Admin $admin, string $ability) {
            return $admin->isSuperAdmin() ? true : null;
        });

        // 帳號權限相關
        Gate::policy(Admin::class, AdminPolicy::class);
        Gate::policy(Permission::class, PermissionPolicy::class);
        Gate::policy(Role::class, RolePolicy::class);

        // 靜態頁面相關
        Gate::policy(Page::class, PagePolicy::class);

        // 公司基本設定相關
        Gate::policy(CompanySetting::class, CompanySettingPolicy::class);

        // 聯絡人相關
        if (config('cs.contact_visible')) {
            Gate::policy(Contact::class, ContactPolicy::class);
        }

        // Banner相關
        if (config('cs.banner_visible')) {
            Gate::policy(Banner::class, BannerPolicy::class);
        }

        // Faq相關
        if (config('cs.faq_visible')) {
            Gate::policy(Faq::class, FaqPolicy::class);
            Gate::policy(FaqCategory::class, FaqPolicy::class);
        }

        // 產品相關
        if (config('cs.product_visible')) {
            Gate::policy(Product::class, ProductPolicy::class);
            Gate::policy(ProductCategory::class, ProductPolicy::class);
            Gate::policy(ProductSpecification::class, ProductPolicy::class);
        }

        // 品牌相關
        if (config('cs.brand_visible')) {
            Gate::policy(Brand::class, BrandPolicy::class);
        }

        // 標籤相關
        Gate::policy(Tag::class, TagPolicy::class);

        // 活動日誌相關
        Gate::policy(ActivityLog::class, ActivityLogPolicy::class);

        // 文章相關
        if (config('cs.article_visible')) {
            Gate::policy(ArticleCategory::class, ArticlePolicy::class);
            Gate::policy(Article::class, ArticlePolicy::class);
        }

        // 會員相關
        if (config('cs.customer_visible')) {
            Gate::policy(config('cs.models.CustomerModel', Customer::class), CustomerPolicy::class);
        }
        if (config('cs.client_visible')) {
            Gate::policy(config('cs.models.ClientModel', Client::class), ClientPolicy::class);
        }
    }

    /**
     * 註冊 BaseSeeder 在 migrate --seed 後自動執行
     */
    public static function registerBaseSeederAfterMigration(): void
    {
        if (self::isConsoleCommandContains(['--seed'], ['--class', 'help', '-h'])) {
            Event::listen(CommandFinished::class, function (CommandFinished $event) {
                if ($event->output instanceof ConsoleOutput) {
                    $seederPath = BaseSeeder::class;
                    echo "\033[1;33mSeeding:\033[0m $seederPath\n";
                    $startTime = microtime(true);
                    Artisan::call('db:seed', ['--class' => $seederPath]);
                    $runTime = round(microtime(true) - $startTime, 2);
                    echo "\033[0;32mSeeded:\033[0m $seederPath ({$runTime} seconds)\n";
                }
            });
        }
    }

    /**
     * 檢查控制台命令是否包含指定的字串
     *
     * @param string|array $contain_options
     * @param string|array $exclude_options
     *
     * @return bool
     */
    public static function isConsoleCommandContains($contain_options, $exclude_options = null): bool
    {
        $args = request()->server('argv', null);
        if (is_array($args)) {
            $command = implode(' ', $args);
            if (
                Str::contains($command, $contain_options) &&
                ($exclude_options == null || !Str::contains($command, $exclude_options))
            ) {
                return true;
            }
        }
        return false;
    }

    /**
     * 註冊導航項目
     *
     * @return void
     */
    public static function registerNavigationItems(): void
    {
        Filament::serving(function () {
            Filament::registerNavigationItems([
                NavigationItem::make('前往前台')
                    ->url(config('app.app_frontend_url'), shouldOpenInNewTab: true)
                    ->icon('heroicon-o-arrow-up-right')
                    ->group('系統管理'),
            ]);
        });
    }

    /**
     * 設定 Filament 表格列的宏
     *
     * @return void
     */
    public static function macroFilamentColumn(): void
    {
        TextColumn::macro('abbr', function (?string $abbr = null, bool $asTooltip = false) {
            if ($this instanceof TextColumn) {
                $label = $this->getLabel();
                $abbr = $abbr ?? $label;
                $classes = $this->isSortable() ? 'cursor-pointer' : 'cursor-help';

                $attributes = $asTooltip ? 'x-tooltip.raw="' . $abbr . '" title=""' : 'title="' . $abbr . '"';

                return $this->label(new HtmlString("<abbr class=\"$classes\" $attributes>$label</abbr>"));
            }

            return $this;
        });


        ToggleColumn::macro('abbr', function (?string $abbr = null, bool $asTooltip = false) {
            if ($this instanceof ToggleColumn) {
                $label = $this->getLabel();
                $abbr = $abbr ?? $label;
                $classes = $this->isSortable() ? 'cursor-pointer' : 'cursor-help';

                $attributes = $asTooltip ? 'x-tooltip.raw="' . $abbr . '" title=""' : 'title="' . $abbr . '"';

                return $this->label(new HtmlString("<abbr class=\"$classes\" $attributes>$label</abbr>"));
            }

            return $this;
        });


        TextInputColumn::configureUsing(function (TextInputColumn $column) {
            $column->extraAttributes(['style' => 'min-width: 8rem !important;']);
        });

        TextInputColumn::macro('abbr', function (?string $abbr = null, bool $asTooltip = false) {
            if ($this instanceof TextInputColumn) {
                $label = $this->getLabel();
                $abbr = $abbr ?? $label;
                $classes = $this->isSortable() ? 'cursor-pointer' : 'cursor-help';

                $attributes = $asTooltip ? 'x-tooltip.raw="' . $abbr . '" title=""' : 'title="' . $abbr . '"';

                return $this->label(new HtmlString("<abbr class=\"$classes\" $attributes>$label</abbr>"));
            }
            return $this;
        });

        SelectColumn::macro('abbr', function (?string $abbr = null, bool $asTooltip = false) {
            if ($this instanceof SelectColumn) {
                $label = $this->getLabel();
                $abbr = $abbr ?? $label;
                $classes = $this->isSortable() ? 'cursor-pointer' : 'cursor-help';

                $attributes = $asTooltip ? 'x-tooltip.raw="' . $abbr . '" title=""' : 'title="' . $abbr . '"';

                return $this->label(new HtmlString("<abbr class=\"$classes\" $attributes>$label</abbr>"));
            }
            return $this;
        });

        SelectColumn::configureUsing(function (SelectColumn $column) {
            $column->extraAttributes(['style' => 'min-width: 8rem !important;']);
        });
    }

    public static function registerRoutes(BaseFilamentPluginServiceProvider $provider): void
    {
        $ecpayRoutesPath = __DIR__ . '/../../routes/ecpay.php';


        if (config('cs.use_default_payment_routes')) {
            $provider->loadRoutesFrom($ecpayRoutesPath);
        }
    }

    /**
     * 註冊事件監聽器
     */
    public static function registerListeners(): void
    {
        if (!self::isSameDomain()) return;

        // 根據 config 中的 listeners 配置註冊事件監聽器
        $listeners = config('cs.listeners', []);

        foreach ($listeners as $eventClass => $listenerClasses) {
            if (!is_array($listenerClasses)) {
                $listenerClasses = [$listenerClasses];
            }

            foreach ($listenerClasses as $listenerClass) {
                Event::listen($eventClass, $listenerClass);
            }
        }
    }

    /**
     * 註冊服務
     */
    public static function registerServices($app): void
    {
        // 註冊 Facade
        $app->singleton(\Stephenchenorg\BaseFilamentPlugin\BaseFilamentPlugin::class, function () {
            return new \Stephenchenorg\BaseFilamentPlugin\BaseFilamentPlugin();
        });

        // 根據 config 中的 implements 配置綁定服務介面
        $implements = config('cs.implements', []);

        foreach ($implements as $interface => $config) {
            $implementation = is_string($config) ? $config : ($config['implementation'] ?? null);
            $bindingType = is_array($config) ? ($config['binding_type'] ?? 'singleton') : 'singleton';

            if (!$implementation) {
                continue;
            }

            $binder = in_array($bindingType, ['singleton', 'bind']) ? $bindingType : null;

            if (!$binder) {
                throw new \InvalidArgumentException("不支援的綁定類型: {$bindingType}");
            }

            $app->$binder($interface, fn($app) => $app->make($implementation));
        }

    }

}
