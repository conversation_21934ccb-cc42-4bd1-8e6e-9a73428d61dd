<?php

namespace Stephenchenorg\BaseFilamentPlugin\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceLanguage;
use Symfony\Component\HttpFoundation\Response;

class LanguageMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {

        $lang = $request->header('Content-Language') ?? ServiceLanguage::getDefaultLanguage();
        App::setLocale($lang);


        return $next($request);
    }
}
