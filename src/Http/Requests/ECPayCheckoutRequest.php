<?php

namespace Stephen<PERSON>org\BaseFilamentPlugin\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Stephenchenorg\BaseFilamentPlugin\Enum\EnumInvoiceMethod;
use Stephenchenorg\BaseFilamentPlugin\Enum\EnumPaymentMethod;
use Stephenchenorg\BaseFilamentPlugin\Enum\EnumShippingMethod;

class ECPayCheckoutRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [

            // 購物車項目
            'items' => 'required|array|min:1',
            'items.*.product_specification_id' => 'required|integer|min:1|exists:product_specifications,id',
            'items.*.quantity' => 'required|integer|min:1',

            // 基本資訊
            'name' => 'required|string|max:255',
            'phone' => 'required|string|max:20',
            'email' => 'required|email|max:255',

            // 付款方式 (必填)
            'payment_method' => [
                'required',
                Rule::in(EnumPaymentMethod::getAvailableMethodValues())
            ],

            // 運送方式 (必填)
            'shipping_method' => [
                'required',
                Rule::in(EnumShippingMethod::getAvailableMethodValues())
            ],

            // 地址資訊 (二選一：store_address_id 或詳細地址)
            'store_address_id' => 'nullable|integer|exists:store_addresses,id',
            'country_code' => 'nullable|string|max:10',
            'state' => 'nullable|string|max:100',
            'district' => 'required_without:store_address_id|string|max:100',
            'postal_code' => 'required_without:store_address_id|string|max:10',
            'address_line1' => 'required_without:store_address_id|string|max:255',
            'address_line2' => 'nullable|string|max:255',

            // 發票資訊
            'invoice_method' => [
                'required',
                Rule::in(EnumInvoiceMethod::getAvailableMethodValues())
            ],

            // 載具相關欄位 (當 invoice_method 為載具類型時必填)
            'carrier_value' => [
                'required_if:invoice_method,' . EnumInvoiceMethod::MOBILE_BARCODE->value . ',' . EnumInvoiceMethod::CITIZEN_CARD->value,
                'max:50'
            ],

            // 二聯/三聯發票相關欄位 (當 invoice_method 為 duplicate 或 triplicate 時必填)
            'invoice_address' => [
                'required_if:invoice_method,' . EnumInvoiceMethod::DUPLICATE->value . ',' . EnumInvoiceMethod::TRIPLICATE->value,
                'max:255'
            ],
            'vat' => [
                'nullable',
                'string',
                'max:20'
            ],
            'invoice_title' => [
                'nullable',
                'string',
                'max:255'
            ],

            // 捐贈相關欄位 (當 invoice_method 為 donation 時必填)
            'love_code' => [
                'required_if:invoice_method,donation',
                'max:20'
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            // 購物車項目
            'items.required' => '購物車項目不能為空',
            'items.array' => '購物車項目格式錯誤',
            'items.min' => '至少需要一個購物車項目',
            'items.*.product_specification_id.required' => '商品規格ID為必填項目',
            'items.*.product_specification_id.integer' => '商品規格ID必須為整數',
            'items.*.product_specification_id.min' => '商品規格ID必須大於0',
            'items.*.product_specification_id.exists' => '商品規格不存在',
            'items.*.quantity.required' => '商品數量為必填項目',
            'items.*.quantity.integer' => '商品數量必須為整數',
            'items.*.quantity.min' => '商品數量必須大於0',

            // 基本資訊
            'name.required' => '姓名為必填項目',
            'name.string' => '姓名格式錯誤',
            'name.max' => '姓名長度不能超過255個字元',
            'phone.required' => '電話號碼為必填項目',
            'phone.string' => '電話號碼格式錯誤',
            'phone.max' => '電話號碼長度不能超過20個字元',
            'email.required' => '電子郵件為必填項目',
            'email.email' => '電子郵件格式錯誤',
            'email.max' => '電子郵件長度不能超過255個字元',

            // 付款方式
            'payment_method.required' => '付款方式為必填項目',
            'payment_method.in' => '系統不接受此付款方式',

            // 運送方式
            'shipping_method.required' => '運送方式為必填項目',
            'shipping_method.in' => '系統不接受此運送方式',

            // 地址資訊
            'store_address_id.integer' => '門市地址ID必須為整數',
            'store_address_id.exists' => '門市地址不存在',
            'country_code.string' => '國家代碼格式錯誤',
            'country_code.max' => '國家代碼長度不能超過10個字元',
            'state.string' => '縣市格式錯誤',
            'state.max' => '縣市長度不能超過100個字元',
            'district.required_without' => '未選擇門市地址時，區域為必填項目',
            'district.string' => '區域格式錯誤',
            'district.max' => '區域長度不能超過100個字元',
            'postal_code.required_without' => '未選擇門市地址時，郵遞區號為必填項目',
            'postal_code.string' => '郵遞區號格式錯誤',
            'postal_code.max' => '郵遞區號長度不能超過10個字元',
            'address_line1.required_without' => '未選擇門市地址時，地址第一行為必填項目',
            'address_line1.string' => '地址第一行格式錯誤',
            'address_line1.max' => '地址第一行長度不能超過255個字元',
            'address_line2.string' => '地址第二行格式錯誤',
            'address_line2.max' => '地址第二行長度不能超過255個字元',

            // 發票資訊
            'invoice_method.required' => '發票開立方式為必填項目',
            'invoice_method.in' => '系統不接受此發票開立方式',

            // 載具相關欄位
            'carrier_value.required_if' => '選擇載具類型時，載具號碼為必填項目',
            'carrier_value.string' => '載具號碼格式錯誤',
            'carrier_value.max' => '載具號碼長度不能超過50個字元',

            // 紙本發票相關欄位
            'invoice_address.required_if' => '選擇紙本發票時，發票地址為必填項目',
            'invoice_address.string' => '發票地址格式錯誤',
            'invoice_address.max' => '發票地址長度不能超過255個字元',
            'vat.string' => '統一編號格式錯誤',
            'vat.max' => '統一編號長度不能超過20個字元',
            'invoice_title.string' => '客戶發票標題格式錯誤',
            'invoice_title.max' => '客戶發票標題長度不能超過255個字元',

            // 捐贈相關欄位
            'love_code.required_if' => '選擇捐贈發票時，愛心碼為必填項目',
            'love_code.string' => '愛心碼格式錯誤',
            'love_code.max' => '愛心碼長度不能超過20個字元',
        ];
    }
}
