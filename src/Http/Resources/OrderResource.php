<?php

namespace Stephenchenorg\BaseFilamentPlugin\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class OrderResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            // 基本資訊
            'id' => $this->id,

            // 關聯會員資訊
            'orderable_type' => $this->orderable_type,
            'orderable_id' => $this->orderable_id,

            // 訂購人資訊
            'name' => $this->name,
            'phone' => $this->phone,
            'email' => $this->email,

            // 金額資訊
            'order_key' => $this->order_key,
            'item_amount' => (int)$this->item_amount,
            'shipping_cost' => (int)$this->shipping_cost,
            'total_amount_untaxed' => (int)$this->total_amount_untaxed,
            'total_amount_taxed' => (int)$this->total_amount_taxed,

            // 付款與運送資訊
            'payment_method' => $this->payment_method?->value,
            'payment_status' => $this->payment_status,
            'shipping_method' => $this->shipping_method?->value,
            'shipping_status' => $this->shipping_status,
            'status' => $this->status,
            'bk_rtn_code' => $this->bk_rtn_code,
            'ft_rtn_code' => $this->ft_rtn_code,

            // 地址資訊
            'store_address_id' => $this->store_address_id,
            'country_code' => $this->country_code,
            'state' => $this->state,
            'city' => $this->city,
            'district' => $this->district,
            'postal_code' => $this->postal_code,
            'address_line1' => $this->address_line1,
            'address_line2' => $this->address_line2,

            // 發票資訊
            'invoice_method' => $this->invoice_method?->value,
            'carrier_value' => $this->carrier_value,
            'invoice_address' => $this->invoice_address,
            'vat' => $this->vat,
            'invoice_title' => $this->invoice_title,

            // 其他資訊
            'payload' => $this->payload,
            'remark' => $this->remark,

            // CVS/BARCODE 付款資訊
            'bank_code' => $this->bank_code,
            'v_account' => $this->v_account,
            'payment_no' => $this->payment_no,
            'expire_date' => $this->expire_date?->format('Y/m/d H:i:s'),
            'barcode1' => $this->barcode1,
            'barcode2' => $this->barcode2,
            'barcode3' => $this->barcode3,

            // 時間戳記
            'created_at' => $this->created_at?->toISOString(),
            'updated_at' => $this->updated_at?->toISOString(),

            // 關聯資料
            'items' => OrderItemResource::collection($this->items),
        ];
    }
}
