<?php
namespace Stephenchenorg\BaseFilamentPlugin\Notifications\Customer;

use Illuminate\Auth\Notifications\ResetPassword as ResetPasswordNotification;

final class CustomerForgotPasswordNotification extends ResetPasswordNotification
{
    public $token;


    public function __construct(string $token)
    {
        parent::__construct($token);
        $this->token = $token;
    }

    protected function resetUrl($notifiable)
    {
        if (static::$createUrlCallback) {
            return call_user_func(static::$createUrlCallback, $notifiable, $this->token);
        }
        $email = urlencode($notifiable->getEmailForPasswordReset());
        $url = config('app.app_frontend_url');

        return "$url/forgot_password?token=$this->token&email=$email";
    }
}
