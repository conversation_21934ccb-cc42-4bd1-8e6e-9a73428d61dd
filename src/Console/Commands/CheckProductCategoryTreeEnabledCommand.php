<?php

namespace Stephenchenorg\BaseFilamentPlugin\Console\Commands;

use Illuminate\Console\Command;
use <PERSON><PERSON>org\BaseFilamentPlugin\Models\Product;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductCategory;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceProductCategory;

class CheckProductCategoryTreeEnabledCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'base:check-product-category-tree-enabled';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check and update product is_all_ancestors_enabled based on category tree status';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('開始檢查產品分類樹狀結構啟用狀態...');

        // 1. 獲取所有產品分類的樹狀結構
        $this->info('正在獲取產品分類樹狀結構...');
        $categoryTree = ProductCategory::query()
            ->whereNull('parent_id')
            ->with('children')
            ->get()
            ->toArray();

        // 2. 使用 ServiceProductCategory 篩選出所有啟用的分類 ID
        $this->info('正在篩選啟用的分類...');
        $enabledCategoryIds = ServiceProductCategory::filterAllEnabledCategoriesFromTree($categoryTree);

        $this->info('找到 ' . count($enabledCategoryIds) . ' 個完全啟用的分類');

        // 3. 獲取所有分類 ID
        $allCategoryIds = ProductCategory::query()->pluck('id')->toArray();

        // 4. 計算未啟用的分類 ID
        $disabledCategoryIds = array_diff($allCategoryIds, $enabledCategoryIds);

        $this->info('找到 ' . count($disabledCategoryIds) . ' 個未完全啟用的分類');

        // 5. 更新啟用分類下的產品
        if (!empty($enabledCategoryIds)) {
            $enabledProductsCount = Product::query()
                ->whereIn('product_category_id', $enabledCategoryIds)
                ->update(['is_all_ancestors_enabled' => 1]);

            $this->info("已將 {$enabledProductsCount} 個產品的 is_all_ancestors_enabled 設為 1");
        }

        // 6. 更新未啟用分類下的產品
        if (!empty($disabledCategoryIds)) {
            $disabledProductsCount = Product::query()
                ->whereIn('product_category_id', $disabledCategoryIds)
                ->update(['is_all_ancestors_enabled' => 0]);

            $this->info("已將 {$disabledProductsCount} 個產品的 is_all_ancestors_enabled 設為 0");
        }

        // 7. 顯示統計資訊
        $totalProducts = Product::query()->count();
        $enabledProducts = Product::query()->where('is_all_ancestors_enabled', 1)->count();
        $disabledProducts = Product::query()->where('is_all_ancestors_enabled', 0)->count();

        $this->info('');
        $this->info('=== 統計資訊 ===');
        $this->info("總產品數量: {$totalProducts}");
        $this->info("啟用產品數量: {$enabledProducts}");
        $this->info("未啟用產品數量: {$disabledProducts}");
        $this->info('');
        $this->info('產品分類樹狀結構啟用狀態檢查完成！');

        return Command::SUCCESS;
    }
}
