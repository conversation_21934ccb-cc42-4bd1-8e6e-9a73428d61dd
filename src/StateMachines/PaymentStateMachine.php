<?php

namespace <PERSON><PERSON>org\BaseFilamentPlugin\StateMachines;

use <PERSON><PERSON>ban<PERSON>\LaravelEloquentStateMachines\StateMachines\StateMachine;
use <PERSON><PERSON>org\BaseFilamentPlugin\Enum\EnumPaymentStatus;

class PaymentStateMachine extends StateMachine
{
    public function recordHistory(): bool
    {
        return true;
    }

    public function transitions(): array
    {
        return [
            EnumPaymentStatus::UNPAID->value        => [EnumPaymentStatus::PAID->value, EnumPaymentStatus::FAILED->value],
            EnumPaymentStatus::FAILED->value        => [EnumPaymentStatus::PAID->value], // 失敗後可以重新付款
            EnumPaymentStatus::PAID->value          => [EnumPaymentStatus::REFUNDING->value],
            EnumPaymentStatus::REFUNDING->value     => [EnumPaymentStatus::REFUNDED->value, EnumPaymentStatus::REFUND_FAILED->value],
            EnumPaymentStatus::REFUNDED->value      => [], // 已退款為最終狀態
            EnumPaymentStatus::REFUND_FAILED->value => [EnumPaymentStatus::REFUNDING->value], // 退款失敗可以重新退款
        ];
    }

    public function defaultState(): ?string
    {
        return EnumPaymentStatus::UNPAID->value;
    }
}
