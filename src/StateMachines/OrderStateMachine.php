<?php

namespace <PERSON><PERSON>org\BaseFilamentPlugin\StateMachines;

use As<PERSON>ban<PERSON>\LaravelEloquentStateMachines\StateMachines\StateMachine;
use <PERSON><PERSON>org\BaseFilamentPlugin\Enum\EnumOrderStatus;

class OrderStateMachine extends StateMachine
{
    public function recordHistory(): bool
    {
        return true;
    }

    public function transitions(): array
    {
        return [
            EnumOrderStatus::PENDING->value   => [EnumOrderStatus::CANCELED->value, EnumOrderStatus::COMPLETED->value, EnumOrderStatus::FAILED->value],
            EnumOrderStatus::CANCELED->value  => [],
            EnumOrderStatus::COMPLETED->value => [],
            EnumOrderStatus::FAILED->value    => [],
        ];
    }

    public function defaultState(): ?string
    {
        return EnumOrderStatus::PENDING->value;
    }
}
