<?php

namespace <PERSON><PERSON>org\BaseFilamentPlugin\StateMachines;

use <PERSON><PERSON>ban<PERSON>\LaravelEloquentStateMachines\StateMachines\StateMachine;
use <PERSON><PERSON>org\BaseFilamentPlugin\Enum\EnumShippingStatus;

class ShippingStateMachine extends StateMachine
{
    public function recordHistory(): bool
    {
        return true;
    }

    public function transitions(): array
    {
        return [
            EnumShippingStatus::UNSHIPPED->value     => [EnumShippingStatus::SHIPPED->value],
            EnumShippingStatus::SHIPPED->value       => [EnumShippingStatus::DELIVERED->value],
            EnumShippingStatus::DELIVERED->value     => [EnumShippingStatus::COMPLETED->value, EnumShippingStatus::PICKUP_FAILED->value],
            EnumShippingStatus::COMPLETED->value     => [EnumShippingStatus::RETURNED->value],
            EnumShippingStatus::PICKUP_FAILED->value => [EnumShippingStatus::SHIPPED->value, EnumShippingStatus::RETURNED->value],
            EnumShippingStatus::RETURNED->value      => [EnumShippingStatus::SHIPPED->value],
        ];
    }

    public function defaultState(): ?string
    {
        return EnumShippingStatus::UNSHIPPED->value;
    }
}
