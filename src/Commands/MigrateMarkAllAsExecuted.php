<?php


namespace Stephenchenorg\BaseFilamentPlugin\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;

class MigrateMarkAllAsExecuted extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'base:migrate-mark-all-executed';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Mark all migrations as executed without running them';

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {
        DB::table('migrations')->truncate();

        $migrationFiles = File::files(database_path('migrations'));

        foreach ($migrationFiles as $file) {
            $migrationName = pathinfo($file)['filename'];

            DB::table('migrations')->insert([
                'migration' => $migrationName,
                'batch'     => 1,
            ]);

            $this->info("Migration {$migrationName} marked as executed.");
        }

        $this->info('All migrations have been marked as executed.');
    }
}
