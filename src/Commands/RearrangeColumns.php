<?php

namespace Stephenchenorg\BaseFilamentPlugin\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;

class RearrangeColumns extends Command
{
    protected $signature = 'base:rearrange-columns';
    protected $description = 'Rearrange created_at column to be after created_by_admin_id in all tables';

    public function handle()
    {
        $tables = DB::select('SHOW TABLES');

        foreach ($tables as $table) {
            $tableName = $table->{"Tables_in_" . env('DB_DATABASE')}; // Get the table name from the result

            if (!Schema::hasTable($tableName)) {
                $this->warn("Table {$tableName} does not exist, skipping...");
                continue;
            }

            $columns = DB::select("SHOW COLUMNS FROM {$tableName}");

            $hasCreatedAt = false;
            $hasCreatedByAdminId = false;
            $createdAtColumn = '';
            $updatedByAdminIdColumn = '';

            foreach ($columns as $column) {
                if ($column->Field === 'created_at') {
                    $hasCreatedAt = true;
                    $createdAtColumn = $column->Field;
                }

                if ($column->Field === 'updated_by_admin_id') {
                    $hasCreatedByAdminId = true;
                    $updatedByAdminIdColumn = $column->Field;
                }
            }

            // If both columns exist, move created_at after created_by_admin_id
            if ($hasCreatedAt && $hasCreatedByAdminId) {
                $this->info("Rearranging columns in table {$tableName}...");

                // Disable foreign key checks
                DB::statement('SET foreign_key_checks = 0;');

                // Use Schema::table to modify created_at column and move it after created_by_admin_id
                Schema::table($tableName, function (Blueprint $table) use ($updatedByAdminIdColumn, $createdAtColumn) {
                    // Using timestamp() for created_at column
                    $table->timestamp('created_at')->nullable()->after($updatedByAdminIdColumn)->change();
                    $table->timestamp('updated_at')->nullable()->after('created_at')->change();
                });

                // Enable foreign key checks
                DB::statement('SET foreign_key_checks = 1;');
            } else {
                $this->info("Skipping table {$tableName}, it does not have both created_at and created_by_admin_id columns.");
            }
        }

        $this->info('Column rearrangement completed.');
    }
}
