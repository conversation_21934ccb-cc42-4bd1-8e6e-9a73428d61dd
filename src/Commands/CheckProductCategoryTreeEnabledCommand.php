<?php

namespace <PERSON>chenorg\BaseFilamentPlugin\Commands;

use Illuminate\Console\Command;
use <PERSON><PERSON>org\BaseFilamentPlugin\Models\Product;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductCategory;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceProductCategory;

class CheckProductCategoryTreeEnabledCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'base:check-product-category-tree-enabled';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check and update product is_all_ancestors_enabled based on category tree status';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('開始檢查產品分類樹狀結構啟用狀態...');

        $rootCategories = ProductCategory::query()
            ->where('parent_id', -1)
            ->get();

        foreach ($rootCategories as $rootCategory) {
            $this->handleRootCategory($rootCategory);
        }

    }



    public function handleRootCategory(ProductCategory $category): void
    {
        if($category->status === 0)
        {
            $categoryIds = $category->descendantsWithSelf()->pluck('id')->toArray();
            $disabledProductsCount = Product::query()->whereIn('product_category_id', $categoryIds)->update(['is_all_ancestors_enabled' => 0]);
            $enabledProductsCount = 0;
        }
        else
        {
            $this->info("檢查根類別 {$category->translations()->where('lang', 'zh_TW')->first()->title}");

            $categoryIds = $category->descendantsWithSelf()->pluck('id')->toArray();
            $descendantTree = $category->getDescendants()->toTree()->toArray();
            $enabledCategoryIds = ServiceProductCategory::filterAllEnabledCategoriesFromTree($descendantTree);
            $enabledCategoryIds[] = $category->id;
            $disabledCategoryIds = array_diff($categoryIds, $enabledCategoryIds);

            // 更新符合條件的產品
            $enabledProductsCount = Product::query()
                ->whereIn('product_category_id', $enabledCategoryIds)
                ->update(['is_all_ancestors_enabled' => 1]);

            // 更新不符合條件的產品
            $disabledProductsCount = Product::query()
                ->whereIn('product_category_id', $disabledCategoryIds)
                ->update(['is_all_ancestors_enabled' => 0]);
        }


        $this->info("已將 {$enabledProductsCount} 個產品的 is_all_ancestors_enabled 設為 1");
        $this->info("已將 {$disabledProductsCount} 個產品的 is_all_ancestors_enabled 設為 0");
    }


}
