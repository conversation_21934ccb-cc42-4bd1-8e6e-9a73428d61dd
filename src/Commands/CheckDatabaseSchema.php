<?php

namespace Stephenchenorg\BaseFilamentPlugin\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;
use Illuminate\Database\Migrations\Migration;

class CheckDatabaseSchema extends Command
{
    protected $signature = 'base:check-schema';
    protected $description = '檢查 migrations 與資料庫結構的差異';

    public function handle()
    {
        $this->info('開始檢查資料庫結構...');

        $migrationFiles = File::files(database_path('migrations'));
        $migrationTables = [];

        foreach ($migrationFiles as $file) {
            include_once $file->getPathname();
            
            // 找出 migration class
            $migration = $this->getMigrationInstance($file);
            if (!$migration) {
                continue;
            }

            $queries = DB::connection()->pretend(function () use ($migration) {
                $migration->up();
            });

            foreach ($queries as $query) {
                $sql = $query['query'];
                
                // 只處理 CREATE TABLE 語句，忽略大小寫
                if (preg_match('/create table `?([^`\s]+)`?/i', $sql, $matches)) {
                    $tableName = $matches[1];
                    $columns = $this->parseCreateTableColumns($sql);
                    $migrationTables[$tableName] = $columns;
                }
            }
        }

        // 檢查資料庫中的表結構
        $this->checkTableStructures($migrationTables);
    }

    protected function getMigrationInstance($file)
    {
        $content = file_get_contents($file->getPathname());
        
        // 檢查是否包含匿名類別
        if (strpos($content, 'return new class extends Migration') !== false) {
            $migration = include $file->getPathname();
            if ($migration instanceof Migration) {
                return $migration;
            }
        }
        
        return null;
    }

    protected function parseCreateTableColumns($sql)
    {
        $columns = [];
        
        // 提取欄位定義，忽略大小寫
        if (preg_match('/create table[^(]+\((.*)\)/i', $sql, $matches)) {
            $columnDefinitions = explode(',', $matches[1]);
            foreach ($columnDefinitions as $definition) {
                $definition = trim($definition);
                
                // 跳過主鍵定義
                if (preg_match('/primary key\s*\(/i', $definition)) {
                    continue;
                }
                
                // 先判斷是否有 decimal
                if (preg_match('/`([^`]+)`\s+decimal/i', $definition, $matches)) {
                    $columnName = $matches[1];
                    $columns[$columnName] = 'decimal';
                    continue;
                }
                
                // 處理一般欄位定義
                else if (preg_match('/`([^`]+)`\s+([a-zA-Z]+(?:\(\d+(?:,\d+)?\))?)(?:\s+(unsigned))?/i', $definition, $matches)) {
                    $columnName = $matches[1];
                    $columnType = $matches[2];
                    $unsigned = isset($matches[3]) ? $matches[3] : '';
                    $columns[$columnName] = $this->normalizeType($columnType, $unsigned);
                }
            }
        }
        
        return $columns;
    }

    protected function normalizeType($type, $unsigned = '')
    {
        $type = strtolower($type);
        $unsigned = strtolower($unsigned);
        
        // 如果是 decimal 類型，直接返回原始定義
        if (preg_match('/decimal\((\d+),(\d+)\)/i', $type)) {
            return $type;
        }
        
        // 處理整數類型
        if (preg_match('/(tiny|small|medium|big)?int/i', $type, $matches)) {
            $size = isset($matches[1]) ? $matches[1] : '';
            return $size . 'int' . ($unsigned ? ' unsigned' : '');
        }
        
        // 處理字串類型
        if (preg_match('/(varchar|char)\((\d+)\)/i', $type, $matches)) {
            return $matches[1] . '(' . $matches[2] . ')';
        }
        
        // 處理小數類型
        if (preg_match('/(decimal|numeric)\((\d+),(\d+)\)/i', $type, $matches)) {
            return $matches[1] . '(' . $matches[2] . ',' . $matches[3] . ')';
        }
        
        // 處理其他類型
        if (str_contains($type, 'text')) return 'text';
        if (str_contains($type, 'datetime')) return 'datetime';
        if (str_contains($type, 'timestamp')) return 'timestamp';
        
        return $type;
    }

    protected function checkTableStructures($migrationTables)
    {
        foreach ($migrationTables as $tableName => $migrationColumns) {
            if (!Schema::hasTable($tableName)) {
                $this->error("表 {$tableName} 在資料庫中不存在");
                continue;
            }

            // 獲取資料庫中的欄位
            $dbColumns = Schema::getColumnListing($tableName);
            $dbColumnTypes = [];
            
            foreach ($dbColumns as $column) {
                $columnInfo = DB::select("SHOW COLUMNS FROM {$tableName} WHERE Field = '{$column}'")[0];
                $type = $columnInfo->Type;
                $dbColumnTypes[$column] = $this->normalizeType($type, str_contains($type, 'unsigned') ? 'unsigned' : '');
            }

            // 檢查欄位差異
            $this->checkColumnDifferences($tableName, $migrationColumns, $dbColumnTypes);
        }
    }

    protected function checkColumnDifferences($tableName, $migrationColumns, $dbColumnTypes)
    {
        $missingColumns = array_diff_key($migrationColumns, $dbColumnTypes);
        $extraColumns = array_diff_key($dbColumnTypes, $migrationColumns);
        $typeMismatches = [];

        // 檢查欄位類型
        foreach ($migrationColumns as $column => $type) {
            if (isset($dbColumnTypes[$column])) {
                $dbType = $dbColumnTypes[$column];
                
                // 如果是 decimal 類型，不考慮精度
                if (str_contains($type, 'decimal') && str_contains($dbType, 'decimal')) {
                    continue;
                }
                
                if ($dbType !== $type) {
                    $typeMismatches[$column] = [
                        'migration' => $type,
                        'database' => $dbType
                    ];
                }
            }
        }

        if (!empty($missingColumns)) {
            $this->error("表 {$tableName} 缺少以下欄位：");
            foreach ($missingColumns as $column => $type) {
                $this->line("- {$column} ({$type})");
            }
        }

        if (!empty($extraColumns)) {
            $this->warn("表 {$tableName} 有多餘的欄位：");
            foreach ($extraColumns as $column => $type) {
                $this->line("- {$column} ({$type})");
            }
        }

        if (!empty($typeMismatches)) {
            $this->error("表 {$tableName} 有以下欄位類型不匹配：");
            foreach ($typeMismatches as $column => $types) {
                $this->line("- {$column}: migration 定義為 {$types['migration']}，資料庫中為 {$types['database']}");
            }
        }

        if (empty($missingColumns) && empty($extraColumns) && empty($typeMismatches)) {
            $this->info("表 {$tableName} 結構完全匹配！");
        }
    }
} 