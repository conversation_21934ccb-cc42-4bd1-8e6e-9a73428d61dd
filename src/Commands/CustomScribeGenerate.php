<?php

namespace Stephenchenorg\BaseFilamentPlugin\Commands;

use Illuminate\Console\Command;
use Stephenchenorg\CsCoreFilamentPlugin\CCUtility;

class CustomScribeGenerate extends Command
{
    protected $signature = 'base:scribe-generate';
    protected $description = 'Generate API documentation with Scribe only if not in production';

    public function handle(): void
    {
        if (CCUtility::isProduction()) {
            $this->info('Skipping Scribe generation in production.');
            return;
        }

        $this->call('scribe:generate');
    }
}
