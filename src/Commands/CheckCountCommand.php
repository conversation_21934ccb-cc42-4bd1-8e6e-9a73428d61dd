<?php

namespace Stephen<PERSON>org\BaseFilamentPlugin\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Stephenchenorg\BaseFilamentPlugin\Models\Article;
use Stephenchenorg\BaseFilamentPlugin\Models\ArticleCategory;
use Stephen<PERSON>org\BaseFilamentPlugin\Models\Faq;
use Stephen<PERSON>org\BaseFilamentPlugin\Models\FaqCategory;
use Stephenchenorg\BaseFilamentPlugin\Models\Product;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductCategory;
use Stephenchenorg\BaseFilamentPlugin\Models\Tag;

class CheckCountCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'base:check-count';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check and update article and FAQ counts for categories and tags';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('開始檢查和更新計數...');

        // 更新文章相關計數
        if(config('cs.article_visible'))
        {
            $this->updateArticleCounts();
        }


        // 更新 FAQ 相關計數
        if(config('cs.faq_visible'))
        {
            $this->updateFaqCounts();
        }

        // 更新產品相關計數
        if(config('cs.product_visible'))
        {
            $this->updateProductCounts();
        }

        $this->info('計數更新完成！');

        return Command::SUCCESS;
    }

    /**
     * 更新文章相關計數
     */
    protected function updateArticleCounts(): void
    {
        $this->line('正在更新文章分類計數...');

        // 使用 group by 獲取每個分類的文章數量
        $articleCounts = Article::query()
            ->selectRaw('article_category_id, COUNT(*) as count')
            ->groupBy('article_category_id')
            ->pluck('count', 'article_category_id')
            ->toArray();

        // 批量更新分類計數
        foreach ($articleCounts as $categoryId => $count) {
            $category = ArticleCategory::query()->find($categoryId);
            if ($category) {
                $category->update(['count_total' => $count]);
                $this->info("更新類別 {$category->name} 的文章數量為 {$count}");
            }
        }

        // 更新 Tag 的 article_count
        $this->line('正在更新標籤的文章計數...');

        // 獲取所有標籤，逐個查詢文章數量
        $tags = Tag::query()->get();

        foreach ($tags as $tag) {
            // 查詢該標籤的文章數量
            $articleCount = DB::table('tag_pivot')
                ->where('tag_id', $tag->id)
                ->where('taggable_type', 'like', "%Article%")
                ->count();

            // 更新標籤的 article_count
            $tag->update(['article_count' => $articleCount]);
            $this->info("更新標籤 {$tag->name} 的文章數量為 {$articleCount}");
        }

    }

    /**
     * 更新 FAQ 相關計數
     */
    protected function updateFaqCounts(): void
    {
        // 更新 FaqCategory 的 count_total
        $this->line('正在更新 FAQ 分類計數...');

        // 先將所有分類的計數重置為 0
        FaqCategory::query()->update(['count_total' => 0]);

        // 使用 group by 獲取每個分類的 FAQ 數量
        $faqCounts = Faq::query()
            ->selectRaw('faq_category_id, COUNT(*) as count')
            ->groupBy('faq_category_id')
            ->pluck('count', 'faq_category_id')
            ->toArray();

        // 批量更新分類計數
        foreach ($faqCounts as $categoryId => $count) {
            $category = FaqCategory::query()->find($categoryId);
            if ($category) {
                $category->update(['count_total' => $count]);
                $this->info("更新類別 {$category->name} Faq數量為 {$count}");
            }
        }
    }


    /**
     * 更新產品分類的計數
     */
    public function updateProductCounts(): void
    {
        $this->info('開始更新產品分類計數...');

        // 獲取所有根分類
        $rootCategories = ProductCategory::query()
            ->where('parent_id', -1)
            ->get();

        foreach ($rootCategories as $rootCategory) {

            $this->info("處理根分類 {$rootCategory->translations()->where('lang', 'zh_TW')->first()?->title}");
            // 建構子樹
            $descendantTree = $rootCategory->getDescendants()->toTree()->toArray();
            // 使用 post-order 遍歷計算並更新計數
            $totalCount = $this->calculatePostOrderCounts($rootCategory, $descendantTree);
            $this->info("根分類總計數: {$totalCount}");
        }



        // 更新 Tag 的 product_count
        $this->line('正在更新標籤的產品計數...');

        // 獲取所有標籤，逐個查詢產品數量
        $tags = Tag::query()->get();

        foreach ($tags as $tag) {
            // 查詢該標籤的產品數量
            $productCount = DB::table('tag_pivot')
                ->where('tag_id', $tag->id)
                ->where('taggable_type', 'like', "%Product%")
                ->count();

            // 更新標籤的 product_count
            $tag->update(['product_count' => $productCount]);
            $this->info("更新標籤 {$tag->name} 的產品數量為 {$productCount}");
        }

        $this->info('產品分類計數更新完成！');
    }

    /**
     * 使用 post-order 遍歷計算分類的產品計數
     *
     * @param ProductCategory $category 當前分類
     * @param array $children 子分類樹狀結構
     * @return int 返回該子樹的總產品數量
     */
    private function calculatePostOrderCounts(ProductCategory $category, array $children): int
    {
        // 獲取當前分類的直接產品數量
        $directCount = Product::query()
            ->where('product_category_id', $category->id)
            ->count();

        // 計算所有子分類的產品數量總和
        $childrenCount = 0;

        foreach ($children as $childData) {
            // 重新構建 ProductCategory 實例
            $childCategory = ProductCategory::query()->find($childData['id']);
            if ($childCategory) {
                // 遞歸計算子分類的計數
                $childSubtreeCount = $this->calculatePostOrderCounts(
                    $childCategory,
                    $childData['children'] ?? []
                );
                $childrenCount += $childSubtreeCount;
            }
        }

        // 當前分類的總計數 = 直接產品數量 + 所有子分類的產品數量
        $totalCount = $directCount + $childrenCount;

        // 更新資料庫中的 count_total
        $category->update(['count_total' => $totalCount]);

        $categoryName = $category->translations()->where('lang', 'zh_TW')->first()?->title ?? "ID:{$category->id}";
        $this->info("更新分類 {$categoryName} 的產品數量為 {$totalCount} (直接: {$directCount}, 子分類: {$childrenCount})");

        // 返回該子樹的總產品數量
        return $totalCount;
    }
}
