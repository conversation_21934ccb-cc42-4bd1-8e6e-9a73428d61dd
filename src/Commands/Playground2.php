<?php

namespace Stephenchenorg\BaseFilamentPlugin\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;

class Playground2 extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'base:playground2';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Playground area for miko';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // GraphQL 端點
        $url = 'http://127.0.0.1:8000';

        // 要測試的 GraphQL 查詢，這裡測試 articleCategory
        $query = '
            query {
                articleCategory(id: 1) {
                    id
                    key
                    status
                }
            }
        ';

        // 發送 GraphQL 請求
        $response = Http::post($url, [
            'query' => $query,
        ]);

        // 確認請求是否成功
        if ($response->successful()) {
            // 顯示查詢結果
            $this->info('GraphQL Query Result:');
            $this->line(json_encode($response->json(), JSO<PERSON>_PRETTY_PRINT));
        } else {
            // 顯示錯誤訊息
            $this->error('GraphQL Query Failed!');
            $this->line('Error: '.$response->body());
        }

        return 0;

    }
}
