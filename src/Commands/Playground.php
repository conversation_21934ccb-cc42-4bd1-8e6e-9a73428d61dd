<?php

namespace Stephenchenorg\BaseFilamentPlugin\Commands;

use Illuminate\Console\Command;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class Playground extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'base:playground';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Playground area';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        // add $table->string('tg_link')->nullable();
        if (!Schema::hasColumn('company_settings', 'tg_link')) {
            Schema::table('company_settings', function (Blueprint $table) {
                $table->string('tg_link')->nullable();
            });
        }

       


        return;

        $migrations = DB::table('migrations')->pluck('migration')->toArray();

        foreach ($migrations as $migration) {
            $tableName = $this->getTableFromMigrationName($migration);

            if (Schema::hasTable($tableName)) {


                if (!in_array($tableName, ['migrations', 'password_resets', 'failed_jobs', 'cache', 'cache_locks', 'sessions', 'jobs', 'job_batches'])) {
                    Schema::table($tableName, function (Blueprint $table) use ($tableName) {
                        if (!Schema::hasColumn($table->getTable(), 'created_by_admin_id')) {
                            $table->unsignedSmallInteger('created_by_admin_id')->nullable()->comment('創建者 ID');
                            $this->info("Successfully added created_by_admin_id to $tableName.");
                        }
                        if (!Schema::hasColumn($table->getTable(), 'updated_by_admin_id')) {
                            $table->unsignedSmallInteger('updated_by_admin_id')->nullable()->comment('最後修改者 ID');
                            $this->info("Successfully added updated_by_admin_id to $tableName.");
                        }
                    });
                }

                $this->addMobileColumns($tableName);
            }
        }


        foreach (['permissions', 'roles'] as $tableName) {

            Schema::table($tableName, function (Blueprint $table) use ($tableName) {
                if (!Schema::hasColumn($table->getTable(), 'created_by_admin_id')) {
                    $table->unsignedSmallInteger('created_by_admin_id')->nullable()->comment('創建者 ID');
                    $this->info("Successfully added created_by_admin_id to $tableName.");
                }
                if (!Schema::hasColumn($table->getTable(), 'updated_by_admin_id')) {
                    $table->unsignedSmallInteger('updated_by_admin_id')->nullable()->comment('最後修改者 ID');
                    $this->info("Successfully added updated_by_admin_id to $tableName.");
                }
            });

            $this->addMobileColumns($tableName);
        }

        $this->info('All migrations have been processed!');
    }

    private function getTableFromMigrationName($migrationName): string
    {
        if (preg_match('/create_(.*?)_table/', $migrationName, $matches)) {
            return $matches[1];
        }

        return $migrationName;
    }

    private function addMobileColumns($tableName): void
    {
        Schema::table($tableName, function (Blueprint $table) use ($tableName) {
            $columns = ['image', 'cover', 'background'];

            foreach ($columns as $column) {
                if (Schema::hasColumn($tableName, $column)) {
                    $mobileColumn = $column . '_mobile';

                    if (!Schema::hasColumn($tableName, $mobileColumn)) {
                        $table->string($mobileColumn)->nullable()->comment("Mobile version of $column");
                        // 显示成功添加的消息
                        $this->info("Successfully added $mobileColumn to $tableName.");
                    }
                }
            }
        });
    }
}
