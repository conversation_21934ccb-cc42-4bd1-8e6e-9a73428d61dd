<?php

namespace Stephenchenorg\BaseFilamentPlugin\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;

class UpdateColumnLengths extends Command
{
    protected $signature = 'base:update-column-lengths';
    protected $description = '更新所有資料表欄位的長度限制';

    public function handle()
    {
        $this->info('開始更新資料表欄位長度...');

        $columnLengths = [
            'admins' => [
                'name' => 30,
                'email' => 70,
                'password' => 256,
                'remember_token' => 100,
            ],
            'contacts' => [
                'type' => 20,
                'name' => 100,
                'phone' => 20,
                'email' => 70,
                'line' => 30,
                'address' => 200,
                'title' => 80,
            ],
            'product_specifications' => [
                'combination_key' => 40,
                'type' => 20,
                'sku' => 64,
                'ean' => 16,
            ],
            'company_settings' => [
                'lang' => 10,
                'logo' => 1024,
                'name' => 80,
                'address_1' => 100,
                'address_2' => 100,
                'phone_1' => 20,
                'phone_2' => 20,
                'email_1' => 70,
                'email_2' => 70,
                'line_link' => 1024,
                'fb_link' => 1024,
                'ig_link' => 1024,
                'twitter_link' => 1024,
                'threads_link' => 1024,
                'tg_link' => 1024,
            ],
            'members' => [
                'name' => 60,
                'email' => 70,
                'password' => 256,
                'phone' => 20,
                'address' => 100,
                'profile_picture' => 1024,
            ],
            'article_categories' => [
                'key' => 50,
                'image' => 1024,
                'image_mobile' => 1024,
                'slug' => 128,
            ],
            'article_category_translations' => [
                'title' => 100,
                'seo_title' => 100,
                'seo_keyword' => 100,
                'og_title' => 100,
                'og_image' => 1024,
                'lang' => 10,
            ],
            'articles' => [
                'key' => 50,
                'slug' => 128,
                'background' => 1024,
                'background_blur' => 1024,
                'background_mobile' => 1024,
                'background_mobile_blur' => 1024,
                'cover' => 1024,
                'cover_blur' => 1024,
                'cover_mobile' => 1024,
                'cover_mobile_blur' => 1024,
            ],
            'article_translations' => [
                'author'=>100,
                'title' => 100,
                'seo_title' => 100,
                'seo_keyword' => 100,
                'og_title' => 100,
                'og_image' => 1024,
                'lang' => 10,
            ],
            'banners' => [
                'cta_title' => 100,
                'cta_link' => 1024,
                'title' => 100,
                'image' => 1024,
                'image_mobile' => 1024,
            ],
            'brand_translations' => [
                'title' => 100,
                'lang' => 10,
            ],
            'brands' => [
                'logo' => 1024,
                'key' => 50,
            ],
            'faq_categories' => [
                'key' => 50,
                'slug' => 128,
            ],
            'faq_category_translations' => [
                'seo_title' => 100,
                'seo_keyword' => 100,
                'og_title' => 100,
                'og_image' => 1024,
                'title' => 100,
                'lang' => 10,
            ],
            'faq_translations' => [
                'seo_title' => 100,
                'seo_keyword' => 100,
                'og_title' => 100,
                'og_image' => 1024,
                'title' => 100,
                'lang' => 10,
            ],
            'faqs' => [
                'key' => 50,
            ],
            'order_items' => [
                // 沒有需要更新長度的字串欄位
            ],
            'order_status_histories' => [
                'payment_status' => 20,
                'shipping_status' => 20,
            ],
            'page_field_translations' => [
                'image' => 1024,
                'image_mobile' => 1024,
                'lang' => 10,
            ],
            'page_fields' => [
                'key' => 50,
                'helper_text' => 256,
                'type' => 30,
            ],
            'page_translations' => [
                'lang' => 10,
                'title' => 100,
                'seo_title' => 100,
                'seo_keyword' => 100,
                'og_title' => 100,
                'og_image' => 1024,
            ],
            'pages' => [
                'key' => 50,
                'slug' => 128,
            ],
            'payments' => [
                'method' => 50,
                'status' => 20,
                'transaction_id' => 100,
            ],
            'product_attribute_item_translations' => [
                'title' => 100,
                'lang' => 10,
            ],
            'product_attribute_items' => [
                // 沒有需要更新長度的字串欄位
            ],
            'product_attribute_translations' => [
                'title' => 100,
                'lang' => 10,
            ],
            'product_attributes' => [
                // 沒有需要更新長度的字串欄位
            ],
            'product_categories' => [
                'sequence' => 1024,
                'code' => 64,
                'type' => 20,
                'image' => 1024,
                'image_mobile' => 1024,
            ],
            'product_category_translations' => [
                'title' => 100,
                'seo_title' => 100,
                'seo_keyword' => 100,
                'og_title' => 100,
                'og_image' => 1024,
                'lang' => 10,
            ],
            'product_detail_translations' => [
                'lang' => 10,
            ],
            'product_details' => [
                'type' => 20,
            ],
            'product_images' => [
                'image' => 1024,
                'image_mobile' => 1024,
            ],
            'product_specification_translations' => [
                'title' => 100,
                'lang' => 10,
            ],
            'product_translations' => [
                'seo_title' => 100,
                'seo_keyword' => 100,
                'og_title' => 100,
                'og_image' => 1024,
                'title' => 100,
                'lang' => 10,
            ],
            'products' => [
                'part_number' => 100,
                'type' => 20,
                'image' => 1024,
                'image_mobile' => 1024,
            ],
            'shipping_addresses' => [
                'address_type' => 20,
                'country_code' => 3,
                'state' => 30,
                'city' => 30,
                'district' => 30,
                'postal_code' => 10,
                'address_line1' => 100,
                'address_line2' => 100,
            ],
            'store_addresses' => [
                'store_type' => 20,
                'store_number' => 20,
                'mail_name' => 60,
                'store_address' => 100,
            ],
            'subscriptions' => [
                'email' => 70,
            ],
            'system_settings' => [
                // 沒有需要更新長度的字串欄位
            ],
            'tag_translations' => [
                'title' => 100,
                'lang' => 10,
            ],
            'tags' => [
                'type' => 20,
                'key' => 50,
                'slug' => 128,
            ],
            'artists' => [
                'cover' => 1024,
                'cover_blur' => 1024,
                'cover_mobile' => 1024,
                'cover_mobile_blur' => 1024,
                'background' => 1024,
                'background_blur' => 1024,
                'background_mobile' => 1024,
                'background_mobile_blur' => 1024,
                'avatar' => 1024,
                'slug' => 128,
            ],
            'artist_works' => [
                'slug' => 128,
                'background' => 1024,
                'background_blur' => 1024,
                'background_mobile' => 1024,
                'background_mobile_blur' => 1024,
                'cover' => 1024,
                'cover_blur' => 1024,
                'cover_mobile' => 1024,
                'cover_mobile_blur' => 1024,
            ],
            'artist_work_translations' => [
                'lang' => 10,
                'title' => 100,
                'seo_title' => 100,
                'seo_keyword' => 100,
                'seo_json_ld' => 1024,
                'og_title' => 100,
                'og_image' => 1024,
            ],
            'artist_videos' => [
                'url' => 1024,
            ],
            'artist_translations' => [
                'lang' => 10,
                'name' => 30,
                'seo_title' => 100,
                'seo_keyword' => 100,
                'seo_json_ld' => 1024,
                'og_title' => 100,
                'og_image' => 1024,
            ],
            'artist_exhibitions' => [
                'slug' => 128,
                'background' => 1024,
                'background_blur' => 1024,
                'background_mobile' => 1024,
                'background_mobile_blur' => 1024,
                'cover' => 1024,
                'cover_blur' => 1024,
                'cover_mobile' => 1024,
                'cover_mobile_blur' => 1024,
            ],
            'artist_exhibition_translations' => [
                'lang' => 10,
                'title' => 100,
                'address' => 100,
                'seo_title' => 100,
                'seo_keyword' => 100,
                'seo_json_ld' => 1024,
                'og_title' => 100,
                'og_image' => 1024,
            ],
            'artist_exhibition_images' => [
                'image' => 1024,
            ],
            'artist_comments' => [
                'cover' => 1024,
                'cover_blur' => 1024,
                'cover_mobile' => 1024,
                'cover_mobile_blur' => 1024,
            ],
            'artist_comment_translations' => [
                'lang' => 10,
                'title' => 100,
                'pdf' => 1024,
            ],

            'forum_images' => [
                'image' => 1024,
                'image_mobile' => 1024,
            ],
            'forum_translations' => [
                'lang' => 10,
                'title' => 100,
                'address' => 100,
                'seo_title' => 100,
                'seo_keyword' => 100,
                'og_title' => 100,
                'og_image' => 1024,
                'host' => 30,
            ],
            'forums' => [
                'slug' => 128,
                'background' => 1024,
                'background_blur' => 1024,
                'background_mobile' => 1024,
                'background_mobile_blur' => 1024,
                'cover' => 1024,
                'cover_blur' => 1024,
                'cover_mobile' => 1024,
                'cover_mobile_blur' => 1024,
            ],
            'media_partner_images' => [
                'image' => 1024,
                'image_mobile' => 1024,
            ],
            'media_partner_translations' => [
                'lang' => 10,
                'title' => 100,
                'seo_title' => 100,
                'seo_keyword' => 100,
                'og_title' => 100,
                'og_image' => 1024,
                'partner' => 255,
            ],
            'media_partners' => [
                'slug' => 128,
                'background' => 1024,
                'background_blur' => 1024,
                'background_mobile' => 1024,
                'background_mobile_blur' => 1024,
                'cover' => 1024,
                'cover_blur' => 1024,
                'cover_mobile' => 1024,
                'cover_mobile_blur' => 1024,
            ],
        ];
        $idTypes = [
            'admins' => ['id' => 'smallint unsigned'],
            'article_categories' => ['id' => 'mediumint unsigned'],
            'article_category_translations' => [
                'id' => 'mediumint unsigned',
                'article_category_id' => 'mediumint unsigned',
            ],
            'articles' => [
                'id' => 'mediumint unsigned',
                'article_category_id' => 'mediumint unsigned',
            ],
            'article_translations' => [
                'id' => 'mediumint unsigned',
                'article_id' => 'mediumint unsigned',
            ],
            'banners' => ['id' => 'smallint unsigned'],
            'brand_translations' => [
                'id' => 'smallint unsigned',
                'brand_id' => 'smallint unsigned',
            ],
            'brands' => ['id' => 'smallint unsigned'],
            'company_settings' => ['id' => 'tinyint unsigned'],
            'contacts' => [
                'id' => 'mediumint unsigned'
            ],
            'contact_files' => [
                'id' => 'mediumint unsigned',
                'contact_id' => 'mediumint unsigned',
            ],
            'faq_categories' => ['id' => 'smallint unsigned'],
            'faq_category_translations' => [
                'id' => 'smallint unsigned',
                'faq_category_id' => 'smallint unsigned',
            ],
            'faq_translations' => [
                'id' => 'smallint unsigned',
                'faq_id' => 'smallint unsigned',
            ],
            'faqs' => [
                'id' => 'smallint unsigned',
                'faq_category_id' => 'smallint unsigned',
            ],
            'members' => [
                'id' => 'mediumint unsigned'
            ],
            'orders' => [
                'id' => 'mediumint unsigned',
                'member_id' => 'mediumint unsigned',
            ],
            'order_items' => [
                'id' => 'mediumint unsigned',
                'order_id' => 'mediumint unsigned',
                'product_specification_id' => 'mediumint unsigned',
            ],
            'order_status_histories' => [
                'id' => 'mediumint unsigned',
                'order_id' => 'mediumint unsigned',
            ],
            'page_field_translations' => [
                'id' => 'smallint unsigned',
                'page_field_id' => 'smallint unsigned',
            ],
            'page_fields' => [
                'id' => 'smallint unsigned',
                'page_id' => 'smallint unsigned',
            ],
            'page_translations' => [
                'id' => 'smallint unsigned',
                'page_id' => 'smallint unsigned',
            ],
            'pages' => ['id' => 'smallint unsigned'],
            'payments' => [
                'id' => 'mediumint unsigned',
                'order_id' => 'mediumint unsigned',
            ],
            'product_attribute_item_translations' => [
                'id' => 'mediumint unsigned',
                'item_id' => 'mediumint unsigned',
            ],
            'product_attribute_items' => [
                'id' => 'mediumint unsigned',
                'product_attribute_id' => 'mediumint unsigned',
            ],
            'product_attribute_translations' => [
                'id' => 'mediumint unsigned',
                'product_attribute_id' => 'mediumint unsigned',
            ],
            'product_attributes' => [
                'id' => 'mediumint unsigned',
                'product_id' => 'mediumint unsigned',
            ],
            'product_categories' => [
                'id' => 'smallint unsigned'
            ],
            'product_category_closure' => [
                'closure_id' => 'mediumint unsigned',
                'ancestor' => 'smallint unsigned',
                'descendant' => 'smallint unsigned',
                'depth' => 'smallint unsigned',
            ],
            'product_category_translations' => [
                'id' => 'smallint unsigned',
                'product_category_id' => 'smallint unsigned',
            ],
            'product_detail_translations' => [
                'id' => 'mediumint unsigned',
                'product_detail_id' => 'mediumint unsigned',
            ],
            'product_details' => [
                'id' => 'mediumint unsigned',
                'product_id' => 'mediumint unsigned',
            ],
            'product_images' => [
                'id' => 'mediumint unsigned',
                'product_id' => 'mediumint unsigned',
            ],
            'product_specification_translations' => [
                'id' => 'mediumint unsigned',
                'product_specification_id' => 'mediumint unsigned',
            ],
            'product_specifications' => [
                'id' => 'mediumint unsigned',
                'product_id' => 'mediumint unsigned',
            ],
            'product_translations' => [
                'id' => 'mediumint unsigned',
                'product_id' => 'mediumint unsigned',
            ],
            'products' => [
                'id' => 'mediumint unsigned',
                'product_category_id' => 'smallint unsigned',
            ],
            'shipping_addresses' => [
                'id' => 'mediumint unsigned',
                'member_id' => 'mediumint unsigned',
                'store_address_id' => 'smallint unsigned',
            ],
            'store_addresses' => ['id' => 'smallint unsigned'],
            'subscriptions' => [
                'id' => 'mediumint unsigned',
                'user_id' => 'bigint unsigned',
            ],
            'system_settings' => ['id' => 'tinyint unsigned'],
            'tag_pivot' => [
                'id' => 'smallint unsigned',
                'tag_id' => 'smallint unsigned',
                'taggable_id' => 'bigint unsigned',
            ],
            'tag_translations' => [
                'id' => 'smallint unsigned',
                'tag_id' => 'smallint unsigned',
            ],
            'tags' => ['id' => 'smallint unsigned'],
            'artists' => [
                'id' => 'smallint unsigned',
                'created_by_admin_id' => 'smallint unsigned',
                'updated_by_admin_id' => 'smallint unsigned',
            ],
            'artist_works' => [
                'id' => 'smallint unsigned',
                'artist_id' => 'smallint unsigned',
                'created_by_admin_id' => 'smallint unsigned',
                'updated_by_admin_id' => 'smallint unsigned',
            ],
            'artist_work_translations' => [
                'id' => 'smallint unsigned',
                'artist_work_id' => 'smallint unsigned',
                'created_by_admin_id' => 'smallint unsigned',
                'updated_by_admin_id' => 'smallint unsigned',
            ],
            'artist_videos' => [
                'id' => 'smallint unsigned',
                'artist_id' => 'smallint unsigned',
                'created_by_admin_id' => 'smallint unsigned',
                'updated_by_admin_id' => 'smallint unsigned',
            ],
            'artist_translations' => [
                'id' => 'smallint unsigned',
                'artist_id' => 'smallint unsigned',
                'created_by_admin_id' => 'smallint unsigned',
                'updated_by_admin_id' => 'smallint unsigned',
            ],
            'artist_exhibitions' => [
                'id' => 'smallint unsigned',
                'artist_id' => 'smallint unsigned',
                'created_by_admin_id' => 'smallint unsigned',
                'updated_by_admin_id' => 'smallint unsigned',
            ],
            'artist_exhibition_translations' => [
                'id' => 'smallint unsigned',
                'artist_exhibition_id' => 'smallint unsigned',
                'created_by_admin_id' => 'smallint unsigned',
                'updated_by_admin_id' => 'smallint unsigned',
            ],
            'artist_exhibition_images' => [
                'id' => 'smallint unsigned',
                'artist_exhibition_id' => 'smallint unsigned',
                'created_by_admin_id' => 'smallint unsigned',
                'updated_by_admin_id' => 'smallint unsigned',
            ],
            'artist_comments' => [
                'id' => 'smallint unsigned',
                'artist_id' => 'smallint unsigned',
                'created_by_admin_id' => 'smallint unsigned',
                'updated_by_admin_id' => 'smallint unsigned',
            ],
            'artist_comment_translations' => [
                'id' => 'smallint unsigned',
                'artist_comment_id' => 'smallint unsigned',
                'created_by_admin_id' => 'smallint unsigned',
                'updated_by_admin_id' => 'smallint unsigned',
            ],
            'forum_images' => [
                'id' => 'smallint unsigned',
                'forum_id' => 'smallint unsigned',
                'created_by_admin_id' => 'smallint unsigned',
                'updated_by_admin_id' => 'smallint unsigned',
            ],
            'forum_translations' => [
                'id' => 'smallint unsigned',
                'forum_id' => 'smallint unsigned',
                'created_by_admin_id' => 'smallint unsigned',
                'updated_by_admin_id' => 'smallint unsigned',
            ],
            'forums' => [
                'id' => 'smallint unsigned',
                'created_by_admin_id' => 'smallint unsigned',
                'updated_by_admin_id' => 'smallint unsigned',
            ],
            'media_partner_images' => [
                'id' => 'smallint unsigned',
                'media_partner_id' => 'smallint unsigned',
                'created_by_admin_id' => 'smallint unsigned',
                'updated_by_admin_id' => 'smallint unsigned',
            ],
            'media_partner_translations' => [
                'id' => 'smallint unsigned',
                'media_partner_id' => 'smallint unsigned',
                'created_by_admin_id' => 'smallint unsigned',
                'updated_by_admin_id' => 'smallint unsigned',
            ],
            'media_partners' => [
                'id' => 'smallint unsigned',
                'created_by_admin_id' => 'smallint unsigned',
                'updated_by_admin_id' => 'smallint unsigned',
            ],
        ];
        $foreignKeys = array (
            0 =>
                array (
                    'CONSTRAINT_NAME' => 'article_category_translations_article_category_id_foreign',
                    'TABLE_NAME' => 'article_category_translations',
                    'COLUMN_NAME' => 'article_category_id',
                    'REFERENCED_TABLE_NAME' => 'article_categories',
                    'REFERENCED_COLUMN_NAME' => 'id',
                    'UPDATE_RULE' => 'NO ACTION',
                    'DELETE_RULE' => 'CASCADE',
                ),
            1 =>
                array (
                    'CONSTRAINT_NAME' => 'article_translations_article_id_foreign',
                    'TABLE_NAME' => 'article_translations',
                    'COLUMN_NAME' => 'article_id',
                    'REFERENCED_TABLE_NAME' => 'articles',
                    'REFERENCED_COLUMN_NAME' => 'id',
                    'UPDATE_RULE' => 'NO ACTION',
                    'DELETE_RULE' => 'CASCADE',
                ),
            2 =>
                array (
                    'CONSTRAINT_NAME' => 'articles_article_category_id_foreign',
                    'TABLE_NAME' => 'articles',
                    'COLUMN_NAME' => 'article_category_id',
                    'REFERENCED_TABLE_NAME' => 'article_categories',
                    'REFERENCED_COLUMN_NAME' => 'id',
                    'UPDATE_RULE' => 'NO ACTION',
                    'DELETE_RULE' => 'CASCADE',
                ),
            3 =>
                array (
                    'CONSTRAINT_NAME' => 'artist_comment_translations_artist_comment_id_foreign',
                    'TABLE_NAME' => 'artist_comment_translations',
                    'COLUMN_NAME' => 'artist_comment_id',
                    'REFERENCED_TABLE_NAME' => 'artist_comments',
                    'REFERENCED_COLUMN_NAME' => 'id',
                    'UPDATE_RULE' => 'NO ACTION',
                    'DELETE_RULE' => 'CASCADE',
                ),
            4 =>
                array (
                    'CONSTRAINT_NAME' => 'artist_comments_artist_id_foreign',
                    'TABLE_NAME' => 'artist_comments',
                    'COLUMN_NAME' => 'artist_id',
                    'REFERENCED_TABLE_NAME' => 'artists',
                    'REFERENCED_COLUMN_NAME' => 'id',
                    'UPDATE_RULE' => 'NO ACTION',
                    'DELETE_RULE' => 'CASCADE',
                ),
            5 =>
                array (
                    'CONSTRAINT_NAME' => 'artist_exhibition_images_artist_exhibition_id_foreign',
                    'TABLE_NAME' => 'artist_exhibition_images',
                    'COLUMN_NAME' => 'artist_exhibition_id',
                    'REFERENCED_TABLE_NAME' => 'artist_exhibitions',
                    'REFERENCED_COLUMN_NAME' => 'id',
                    'UPDATE_RULE' => 'NO ACTION',
                    'DELETE_RULE' => 'CASCADE',
                ),
            6 =>
                array (
                    'CONSTRAINT_NAME' => 'artist_exhibition_translations_artist_exhibition_id_foreign',
                    'TABLE_NAME' => 'artist_exhibition_translations',
                    'COLUMN_NAME' => 'artist_exhibition_id',
                    'REFERENCED_TABLE_NAME' => 'artist_exhibitions',
                    'REFERENCED_COLUMN_NAME' => 'id',
                    'UPDATE_RULE' => 'NO ACTION',
                    'DELETE_RULE' => 'CASCADE',
                ),
            7 =>
                array (
                    'CONSTRAINT_NAME' => 'artist_exhibitions_artist_id_foreign',
                    'TABLE_NAME' => 'artist_exhibitions',
                    'COLUMN_NAME' => 'artist_id',
                    'REFERENCED_TABLE_NAME' => 'artists',
                    'REFERENCED_COLUMN_NAME' => 'id',
                    'UPDATE_RULE' => 'NO ACTION',
                    'DELETE_RULE' => 'CASCADE',
                ),
            8 =>
                array (
                    'CONSTRAINT_NAME' => 'artist_translations_artist_id_foreign',
                    'TABLE_NAME' => 'artist_translations',
                    'COLUMN_NAME' => 'artist_id',
                    'REFERENCED_TABLE_NAME' => 'artists',
                    'REFERENCED_COLUMN_NAME' => 'id',
                    'UPDATE_RULE' => 'NO ACTION',
                    'DELETE_RULE' => 'CASCADE',
                ),
            9 =>
                array (
                    'CONSTRAINT_NAME' => 'artist_videos_artist_id_foreign',
                    'TABLE_NAME' => 'artist_videos',
                    'COLUMN_NAME' => 'artist_id',
                    'REFERENCED_TABLE_NAME' => 'artists',
                    'REFERENCED_COLUMN_NAME' => 'id',
                    'UPDATE_RULE' => 'NO ACTION',
                    'DELETE_RULE' => 'CASCADE',
                ),
            10 =>
                array (
                    'CONSTRAINT_NAME' => 'artist_work_translations_artist_work_id_foreign',
                    'TABLE_NAME' => 'artist_work_translations',
                    'COLUMN_NAME' => 'artist_work_id',
                    'REFERENCED_TABLE_NAME' => 'artist_works',
                    'REFERENCED_COLUMN_NAME' => 'id',
                    'UPDATE_RULE' => 'NO ACTION',
                    'DELETE_RULE' => 'CASCADE',
                ),
            11 =>
                array (
                    'CONSTRAINT_NAME' => 'artist_works_artist_id_foreign',
                    'TABLE_NAME' => 'artist_works',
                    'COLUMN_NAME' => 'artist_id',
                    'REFERENCED_TABLE_NAME' => 'artists',
                    'REFERENCED_COLUMN_NAME' => 'id',
                    'UPDATE_RULE' => 'NO ACTION',
                    'DELETE_RULE' => 'CASCADE',
                ),
            12 =>
                array (
                    'CONSTRAINT_NAME' => 'brand_translations_brand_id_foreign',
                    'TABLE_NAME' => 'brand_translations',
                    'COLUMN_NAME' => 'brand_id',
                    'REFERENCED_TABLE_NAME' => 'brands',
                    'REFERENCED_COLUMN_NAME' => 'id',
                    'UPDATE_RULE' => 'NO ACTION',
                    'DELETE_RULE' => 'CASCADE',
                ),
            13 =>
                array (
                    'CONSTRAINT_NAME' => 'contact_files_contact_id_foreign',
                    'TABLE_NAME' => 'contact_files',
                    'COLUMN_NAME' => 'contact_id',
                    'REFERENCED_TABLE_NAME' => 'contacts',
                    'REFERENCED_COLUMN_NAME' => 'id',
                    'UPDATE_RULE' => 'NO ACTION',
                    'DELETE_RULE' => 'CASCADE',
                ),
            14 =>
                array (
                    'CONSTRAINT_NAME' => 'exhibition_images_exhibition_id_foreign',
                    'TABLE_NAME' => 'exhibition_images',
                    'COLUMN_NAME' => 'exhibition_id',
                    'REFERENCED_TABLE_NAME' => 'exhibitions',
                    'REFERENCED_COLUMN_NAME' => 'id',
                    'UPDATE_RULE' => 'NO ACTION',
                    'DELETE_RULE' => 'CASCADE',
                ),
            15 =>
                array (
                    'CONSTRAINT_NAME' => 'exhibition_translations_exhibition_id_foreign',
                    'TABLE_NAME' => 'exhibition_translations',
                    'COLUMN_NAME' => 'exhibition_id',
                    'REFERENCED_TABLE_NAME' => 'exhibitions',
                    'REFERENCED_COLUMN_NAME' => 'id',
                    'UPDATE_RULE' => 'NO ACTION',
                    'DELETE_RULE' => 'CASCADE',
                ),
            16 =>
                array (
                    'CONSTRAINT_NAME' => 'faq_category_translations_faq_category_id_foreign',
                    'TABLE_NAME' => 'faq_category_translations',
                    'COLUMN_NAME' => 'faq_category_id',
                    'REFERENCED_TABLE_NAME' => 'faq_categories',
                    'REFERENCED_COLUMN_NAME' => 'id',
                    'UPDATE_RULE' => 'NO ACTION',
                    'DELETE_RULE' => 'CASCADE',
                ),
            17 =>
                array (
                    'CONSTRAINT_NAME' => 'faq_translations_faq_id_foreign',
                    'TABLE_NAME' => 'faq_translations',
                    'COLUMN_NAME' => 'faq_id',
                    'REFERENCED_TABLE_NAME' => 'faqs',
                    'REFERENCED_COLUMN_NAME' => 'id',
                    'UPDATE_RULE' => 'NO ACTION',
                    'DELETE_RULE' => 'CASCADE',
                ),
            18 =>
                array (
                    'CONSTRAINT_NAME' => 'faqs_faq_category_id_foreign',
                    'TABLE_NAME' => 'faqs',
                    'COLUMN_NAME' => 'faq_category_id',
                    'REFERENCED_TABLE_NAME' => 'faq_categories',
                    'REFERENCED_COLUMN_NAME' => 'id',
                    'UPDATE_RULE' => 'NO ACTION',
                    'DELETE_RULE' => 'CASCADE',
                ),
            19 =>
                array (
                    'CONSTRAINT_NAME' => 'forum_images_forum_id_foreign',
                    'TABLE_NAME' => 'forum_images',
                    'COLUMN_NAME' => 'forum_id',
                    'REFERENCED_TABLE_NAME' => 'forums',
                    'REFERENCED_COLUMN_NAME' => 'id',
                    'UPDATE_RULE' => 'NO ACTION',
                    'DELETE_RULE' => 'CASCADE',
                ),
            20 =>
                array (
                    'CONSTRAINT_NAME' => 'forum_translations_forum_id_foreign',
                    'TABLE_NAME' => 'forum_translations',
                    'COLUMN_NAME' => 'forum_id',
                    'REFERENCED_TABLE_NAME' => 'forums',
                    'REFERENCED_COLUMN_NAME' => 'id',
                    'UPDATE_RULE' => 'NO ACTION',
                    'DELETE_RULE' => 'CASCADE',
                ),
            21 =>
                array (
                    'CONSTRAINT_NAME' => 'media_partner_images_media_partner_id_foreign',
                    'TABLE_NAME' => 'media_partner_images',
                    'COLUMN_NAME' => 'media_partner_id',
                    'REFERENCED_TABLE_NAME' => 'media_partners',
                    'REFERENCED_COLUMN_NAME' => 'id',
                    'UPDATE_RULE' => 'NO ACTION',
                    'DELETE_RULE' => 'CASCADE',
                ),
            22 =>
                array (
                    'CONSTRAINT_NAME' => 'media_partner_translations_media_partner_id_foreign',
                    'TABLE_NAME' => 'media_partner_translations',
                    'COLUMN_NAME' => 'media_partner_id',
                    'REFERENCED_TABLE_NAME' => 'media_partners',
                    'REFERENCED_COLUMN_NAME' => 'id',
                    'UPDATE_RULE' => 'NO ACTION',
                    'DELETE_RULE' => 'CASCADE',
                ),
            23 =>
                array (
                    'CONSTRAINT_NAME' => 'model_has_permissions_permission_id_foreign',
                    'TABLE_NAME' => 'model_has_permissions',
                    'COLUMN_NAME' => 'permission_id',
                    'REFERENCED_TABLE_NAME' => 'permissions',
                    'REFERENCED_COLUMN_NAME' => 'id',
                    'UPDATE_RULE' => 'NO ACTION',
                    'DELETE_RULE' => 'CASCADE',
                ),
            24 =>
                array (
                    'CONSTRAINT_NAME' => 'model_has_roles_role_id_foreign',
                    'TABLE_NAME' => 'model_has_roles',
                    'COLUMN_NAME' => 'role_id',
                    'REFERENCED_TABLE_NAME' => 'roles',
                    'REFERENCED_COLUMN_NAME' => 'id',
                    'UPDATE_RULE' => 'NO ACTION',
                    'DELETE_RULE' => 'CASCADE',
                ),
            25 =>
                array (
                    'CONSTRAINT_NAME' => 'order_items_order_id_foreign',
                    'TABLE_NAME' => 'order_items',
                    'COLUMN_NAME' => 'order_id',
                    'REFERENCED_TABLE_NAME' => 'orders',
                    'REFERENCED_COLUMN_NAME' => 'id',
                    'UPDATE_RULE' => 'NO ACTION',
                    'DELETE_RULE' => 'CASCADE',
                ),
            26 =>
                array (
                    'CONSTRAINT_NAME' => 'order_items_product_specification_id_foreign',
                    'TABLE_NAME' => 'order_items',
                    'COLUMN_NAME' => 'product_specification_id',
                    'REFERENCED_TABLE_NAME' => 'product_specifications',
                    'REFERENCED_COLUMN_NAME' => 'id',
                    'UPDATE_RULE' => 'NO ACTION',
                    'DELETE_RULE' => 'CASCADE',
                ),
            27 =>
                array (
                    'CONSTRAINT_NAME' => 'order_status_histories_order_id_foreign',
                    'TABLE_NAME' => 'order_status_histories',
                    'COLUMN_NAME' => 'order_id',
                    'REFERENCED_TABLE_NAME' => 'orders',
                    'REFERENCED_COLUMN_NAME' => 'id',
                    'UPDATE_RULE' => 'NO ACTION',
                    'DELETE_RULE' => 'CASCADE',
                ),
            28 =>
                array (
                    'CONSTRAINT_NAME' => 'orders_member_id_foreign',
                    'TABLE_NAME' => 'orders',
                    'COLUMN_NAME' => 'member_id',
                    'REFERENCED_TABLE_NAME' => 'members',
                    'REFERENCED_COLUMN_NAME' => 'id',
                    'UPDATE_RULE' => 'NO ACTION',
                    'DELETE_RULE' => 'SET NULL',
                ),
            29 =>
                array (
                    'CONSTRAINT_NAME' => 'page_field_translations_page_field_id_foreign',
                    'TABLE_NAME' => 'page_field_translations',
                    'COLUMN_NAME' => 'page_field_id',
                    'REFERENCED_TABLE_NAME' => 'page_fields',
                    'REFERENCED_COLUMN_NAME' => 'id',
                    'UPDATE_RULE' => 'NO ACTION',
                    'DELETE_RULE' => 'CASCADE',
                ),
            30 =>
                array (
                    'CONSTRAINT_NAME' => 'page_fields_page_id_foreign',
                    'TABLE_NAME' => 'page_fields',
                    'COLUMN_NAME' => 'page_id',
                    'REFERENCED_TABLE_NAME' => 'pages',
                    'REFERENCED_COLUMN_NAME' => 'id',
                    'UPDATE_RULE' => 'NO ACTION',
                    'DELETE_RULE' => 'CASCADE',
                ),
            31 =>
                array (
                    'CONSTRAINT_NAME' => 'page_translations_page_id_foreign',
                    'TABLE_NAME' => 'page_translations',
                    'COLUMN_NAME' => 'page_id',
                    'REFERENCED_TABLE_NAME' => 'pages',
                    'REFERENCED_COLUMN_NAME' => 'id',
                    'UPDATE_RULE' => 'NO ACTION',
                    'DELETE_RULE' => 'CASCADE',
                ),
            32 =>
                array (
                    'CONSTRAINT_NAME' => 'payments_order_id_foreign',
                    'TABLE_NAME' => 'payments',
                    'COLUMN_NAME' => 'order_id',
                    'REFERENCED_TABLE_NAME' => 'orders',
                    'REFERENCED_COLUMN_NAME' => 'id',
                    'UPDATE_RULE' => 'NO ACTION',
                    'DELETE_RULE' => 'CASCADE',
                ),
            33 =>
                array (
                    'CONSTRAINT_NAME' => 'product_attribute_item_translations_item_id_foreign',
                    'TABLE_NAME' => 'product_attribute_item_translations',
                    'COLUMN_NAME' => 'item_id',
                    'REFERENCED_TABLE_NAME' => 'product_attribute_items',
                    'REFERENCED_COLUMN_NAME' => 'id',
                    'UPDATE_RULE' => 'NO ACTION',
                    'DELETE_RULE' => 'CASCADE',
                ),
            34 =>
                array (
                    'CONSTRAINT_NAME' => 'product_attribute_items_product_attribute_id_foreign',
                    'TABLE_NAME' => 'product_attribute_items',
                    'COLUMN_NAME' => 'product_attribute_id',
                    'REFERENCED_TABLE_NAME' => 'product_attributes',
                    'REFERENCED_COLUMN_NAME' => 'id',
                    'UPDATE_RULE' => 'NO ACTION',
                    'DELETE_RULE' => 'CASCADE',
                ),
            35 =>
                array (
                    'CONSTRAINT_NAME' => 'product_attribute_translations_product_attribute_id_foreign',
                    'TABLE_NAME' => 'product_attribute_translations',
                    'COLUMN_NAME' => 'product_attribute_id',
                    'REFERENCED_TABLE_NAME' => 'product_attributes',
                    'REFERENCED_COLUMN_NAME' => 'id',
                    'UPDATE_RULE' => 'NO ACTION',
                    'DELETE_RULE' => 'CASCADE',
                ),
            36 =>
                array (
                    'CONSTRAINT_NAME' => 'product_attributes_product_id_foreign',
                    'TABLE_NAME' => 'product_attributes',
                    'COLUMN_NAME' => 'product_id',
                    'REFERENCED_TABLE_NAME' => 'products',
                    'REFERENCED_COLUMN_NAME' => 'id',
                    'UPDATE_RULE' => 'NO ACTION',
                    'DELETE_RULE' => 'CASCADE',
                ),
            37 =>
                array (
                    'CONSTRAINT_NAME' => 'product_category_closure_ancestor_foreign',
                    'TABLE_NAME' => 'product_category_closure',
                    'COLUMN_NAME' => 'ancestor',
                    'REFERENCED_TABLE_NAME' => 'product_categories',
                    'REFERENCED_COLUMN_NAME' => 'id',
                    'UPDATE_RULE' => 'NO ACTION',
                    'DELETE_RULE' => 'CASCADE',
                ),
            38 =>
                array (
                    'CONSTRAINT_NAME' => 'product_category_closure_descendant_foreign',
                    'TABLE_NAME' => 'product_category_closure',
                    'COLUMN_NAME' => 'descendant',
                    'REFERENCED_TABLE_NAME' => 'product_categories',
                    'REFERENCED_COLUMN_NAME' => 'id',
                    'UPDATE_RULE' => 'NO ACTION',
                    'DELETE_RULE' => 'CASCADE',
                ),
            39 =>
                array (
                    'CONSTRAINT_NAME' => 'product_category_translations_product_category_id_foreign',
                    'TABLE_NAME' => 'product_category_translations',
                    'COLUMN_NAME' => 'product_category_id',
                    'REFERENCED_TABLE_NAME' => 'product_categories',
                    'REFERENCED_COLUMN_NAME' => 'id',
                    'UPDATE_RULE' => 'NO ACTION',
                    'DELETE_RULE' => 'CASCADE',
                ),
            40 =>
                array (
                    'CONSTRAINT_NAME' => 'product_detail_translations_product_detail_id_foreign',
                    'TABLE_NAME' => 'product_detail_translations',
                    'COLUMN_NAME' => 'product_detail_id',
                    'REFERENCED_TABLE_NAME' => 'product_details',
                    'REFERENCED_COLUMN_NAME' => 'id',
                    'UPDATE_RULE' => 'NO ACTION',
                    'DELETE_RULE' => 'CASCADE',
                ),
            41 =>
                array (
                    'CONSTRAINT_NAME' => 'product_details_product_id_foreign',
                    'TABLE_NAME' => 'product_details',
                    'COLUMN_NAME' => 'product_id',
                    'REFERENCED_TABLE_NAME' => 'products',
                    'REFERENCED_COLUMN_NAME' => 'id',
                    'UPDATE_RULE' => 'NO ACTION',
                    'DELETE_RULE' => 'CASCADE',
                ),
            42 =>
                array (
                    'CONSTRAINT_NAME' => 'product_images_product_id_foreign',
                    'TABLE_NAME' => 'product_images',
                    'COLUMN_NAME' => 'product_id',
                    'REFERENCED_TABLE_NAME' => 'products',
                    'REFERENCED_COLUMN_NAME' => 'id',
                    'UPDATE_RULE' => 'NO ACTION',
                    'DELETE_RULE' => 'CASCADE',
                ),
            43 =>
                array (
                    'CONSTRAINT_NAME' => 'ps_trans_spec_id_foreign',
                    'TABLE_NAME' => 'product_specification_translations',
                    'COLUMN_NAME' => 'product_specification_id',
                    'REFERENCED_TABLE_NAME' => 'product_specifications',
                    'REFERENCED_COLUMN_NAME' => 'id',
                    'UPDATE_RULE' => 'NO ACTION',
                    'DELETE_RULE' => 'CASCADE',
                ),
            44 =>
                array (
                    'CONSTRAINT_NAME' => 'product_specifications_product_id_foreign',
                    'TABLE_NAME' => 'product_specifications',
                    'COLUMN_NAME' => 'product_id',
                    'REFERENCED_TABLE_NAME' => 'products',
                    'REFERENCED_COLUMN_NAME' => 'id',
                    'UPDATE_RULE' => 'NO ACTION',
                    'DELETE_RULE' => 'CASCADE',
                ),
            45 =>
                array (
                    'CONSTRAINT_NAME' => 'product_translations_product_id_foreign',
                    'TABLE_NAME' => 'product_translations',
                    'COLUMN_NAME' => 'product_id',
                    'REFERENCED_TABLE_NAME' => 'products',
                    'REFERENCED_COLUMN_NAME' => 'id',
                    'UPDATE_RULE' => 'NO ACTION',
                    'DELETE_RULE' => 'CASCADE',
                ),
            46 =>
                array (
                    'CONSTRAINT_NAME' => 'products_product_category_id_foreign',
                    'TABLE_NAME' => 'products',
                    'COLUMN_NAME' => 'product_category_id',
                    'REFERENCED_TABLE_NAME' => 'product_categories',
                    'REFERENCED_COLUMN_NAME' => 'id',
                    'UPDATE_RULE' => 'NO ACTION',
                    'DELETE_RULE' => 'CASCADE',
                ),
            47 =>
                array (
                    'CONSTRAINT_NAME' => 'role_has_permissions_permission_id_foreign',
                    'TABLE_NAME' => 'role_has_permissions',
                    'COLUMN_NAME' => 'permission_id',
                    'REFERENCED_TABLE_NAME' => 'permissions',
                    'REFERENCED_COLUMN_NAME' => 'id',
                    'UPDATE_RULE' => 'NO ACTION',
                    'DELETE_RULE' => 'CASCADE',
                ),
            48 =>
                array (
                    'CONSTRAINT_NAME' => 'role_has_permissions_role_id_foreign',
                    'TABLE_NAME' => 'role_has_permissions',
                    'COLUMN_NAME' => 'role_id',
                    'REFERENCED_TABLE_NAME' => 'roles',
                    'REFERENCED_COLUMN_NAME' => 'id',
                    'UPDATE_RULE' => 'NO ACTION',
                    'DELETE_RULE' => 'CASCADE',
                ),
            49 =>
                array (
                    'CONSTRAINT_NAME' => 'shipping_addresses_member_id_foreign',
                    'TABLE_NAME' => 'shipping_addresses',
                    'COLUMN_NAME' => 'member_id',
                    'REFERENCED_TABLE_NAME' => 'members',
                    'REFERENCED_COLUMN_NAME' => 'id',
                    'UPDATE_RULE' => 'NO ACTION',
                    'DELETE_RULE' => 'CASCADE',
                ),
            50 =>
                array (
                    'CONSTRAINT_NAME' => 'shipping_addresses_store_address_id_foreign',
                    'TABLE_NAME' => 'shipping_addresses',
                    'COLUMN_NAME' => 'store_address_id',
                    'REFERENCED_TABLE_NAME' => 'store_addresses',
                    'REFERENCED_COLUMN_NAME' => 'id',
                    'UPDATE_RULE' => 'NO ACTION',
                    'DELETE_RULE' => 'SET NULL',
                ),
            51 =>
                array (
                    'CONSTRAINT_NAME' => 'subscriptions_user_id_foreign',
                    'TABLE_NAME' => 'subscriptions',
                    'COLUMN_NAME' => 'user_id',
                    'REFERENCED_TABLE_NAME' => 'users',
                    'REFERENCED_COLUMN_NAME' => 'id',
                    'UPDATE_RULE' => 'NO ACTION',
                    'DELETE_RULE' => 'CASCADE',
                ),
            52 =>
                array (
                    'CONSTRAINT_NAME' => 'tag_pivot_tag_id_foreign',
                    'TABLE_NAME' => 'tag_pivot',
                    'COLUMN_NAME' => 'tag_id',
                    'REFERENCED_TABLE_NAME' => 'tags',
                    'REFERENCED_COLUMN_NAME' => 'id',
                    'UPDATE_RULE' => 'NO ACTION',
                    'DELETE_RULE' => 'CASCADE',
                ),
            53 =>
                array (
                    'CONSTRAINT_NAME' => 'tag_translations_tag_id_foreign',
                    'TABLE_NAME' => 'tag_translations',
                    'COLUMN_NAME' => 'tag_id',
                    'REFERENCED_TABLE_NAME' => 'tags',
                    'REFERENCED_COLUMN_NAME' => 'id',
                    'UPDATE_RULE' => 'NO ACTION',
                    'DELETE_RULE' => 'CASCADE',
                ),
        );


        foreach ($columnLengths as $table => $columns) {
            if (!Schema::hasTable($table)) {
                $this->warn("資料表 {$table} 不存在，跳過...");
                continue;
            }

            $this->info("正在更新資料表 {$table} 的欄位長度...");

            Schema::table($table, function (Blueprint $table) use ($columns) {
                foreach ($columns as $column => $length) {
                    // Check if the column exists in the table
                    if (Schema::hasColumn($table->getTable(), $column)) {
                        // Check if the column is nullable
                        $columnInfo = DB::select("SHOW COLUMNS FROM {$table->getTable()} WHERE Field = ?", [$column])[0];
                        $isNullable = $columnInfo->Null === 'YES';

                        // Update column length while keeping the nullable setting
                        $columnDefinition = $table->string($column, $length);
                        if ($isNullable) {
                            $columnDefinition->nullable();
                        }
                        $columnDefinition->change();

                        $this->info("已更新欄位 {$column} 的長度為 {$length}" . ($isNullable ? " (可為空)" : " (不可為空)"));
                    } else {
                        // If column does not exist, add it with the specified length
                        $table->string($column, $length);
                        $this->info("欄位 {$column} 不存在，已新增欄位，長度為 {$length}");
                    }
                }
            });
        }


        // 禁用外鍵檢查
        DB::statement('SET foreign_key_checks = 0;');


        // 取得所有外鍵資訊
        $tables = DB::select("SHOW TABLES");
        foreach ($tables as $tableRow) {

            $table = collect($tableRow)->first();

            $results = DB::select("
        SELECT
            kcu.CONSTRAINT_NAME,
            kcu.TABLE_NAME,
            kcu.COLUMN_NAME,
            kcu.REFERENCED_TABLE_NAME,
            kcu.REFERENCED_COLUMN_NAME,
            rc.UPDATE_RULE,
            rc.DELETE_RULE
        FROM information_schema.KEY_COLUMN_USAGE AS kcu
        JOIN information_schema.REFERENTIAL_CONSTRAINTS AS rc
            ON kcu.CONSTRAINT_NAME = rc.CONSTRAINT_NAME
            AND kcu.CONSTRAINT_SCHEMA = rc.CONSTRAINT_SCHEMA
        WHERE
            kcu.TABLE_SCHEMA = DATABASE()
            AND kcu.TABLE_NAME = ?
            AND kcu.REFERENCED_TABLE_NAME IS NOT NULL
    ", [$table]);

            foreach ($results as $fk) {
                // 刪除外鍵
                DB::statement("ALTER TABLE `{$fk->TABLE_NAME}` DROP FOREIGN KEY `{$fk->CONSTRAINT_NAME}`");
                $this->info("已移除外鍵 {$fk->CONSTRAINT_NAME} 於資料表 {$fk->TABLE_NAME}");
            }
        }


// 執行原本的 ID 與 foreign key 欄位更新
        foreach ($idTypes as $table => $columns) {
            if (!Schema::hasTable($table)) {
                $this->warn("資料表 {$table} 不存在，跳過ID類型更新...");
                continue;
            }

            $this->info("正在更新資料表 {$table} 的ID和外鍵類型...");

            Schema::table($table, function (Blueprint $table) use ($columns) {
                foreach ($columns as $column => $type) {
                    if (Schema::hasColumn($table->getTable(), $column)) {
                        $columnInfo = DB::select("SHOW COLUMNS FROM {$table->getTable()} WHERE Field = ?", [$column])[0];
                        $isNullable = $columnInfo->Null === 'YES';

                        $baseType = str_replace(' unsigned', '', $type);
                        switch ($baseType) {
                            case 'tinyint':
                                $col = $table->tinyInteger($column)->unsigned();
                                break;
                            case 'smallint':
                                $col = $table->smallInteger($column)->unsigned();
                                break;
                            case 'mediumint':
                                $col = $table->mediumInteger($column)->unsigned();
                                break;
                            case 'bigint':
                                $col = $table->bigInteger($column)->unsigned();
                                break;
                            default:
                                $col = $table->integer($column)->unsigned();
                        }

                        if ($column === 'id') {
                            $col->autoIncrement();
                        }

                        if ($isNullable) {
                            $col->nullable();
                        }

                        $col->change();

                        $this->info("已更新欄位 {$column} 的類型為 {$type}" . ($isNullable ? " (可為空)" : " (不可為空)"));
                    } else {
                        $this->warn("欄位 {$column} 不存在於資料表 {$table->getTable()} 中");
                    }
                }
            });
        }


// 重新加回所有外鍵
        foreach ($foreignKeys as $fk) {

            if (!Schema::hasTable($fk['TABLE_NAME'])) {
                $this->warn("資料表 {$fk['TABLE_NAME']} 不存在，跳過外鍵 {$fk['CONSTRAINT_NAME']}");
                continue;
            }

            Schema::table($fk['TABLE_NAME'], function (Blueprint $table) use ($fk) {
                $foreign = $table->foreign($fk['COLUMN_NAME'], $fk['CONSTRAINT_NAME'])
                    ->references($fk['REFERENCED_COLUMN_NAME'])
                    ->on($fk['REFERENCED_TABLE_NAME']);

                // 保留 onDelete 行為
                if (!empty($fk['DELETE_RULE']) && strtolower($fk['DELETE_RULE']) !== 'no action') {
                    $foreign->onDelete(strtolower($fk['DELETE_RULE']));
                }

                // 保留 onUpdate 行為
                if (!empty($fk['UPDATE_RULE']) && strtolower($fk['UPDATE_RULE']) !== 'no action') {
                    $foreign->onUpdate(strtolower($fk['UPDATE_RULE']));
                }
            });

            $this->info("已恢復外鍵 {$fk['CONSTRAINT_NAME']} 於資料表 {$fk['TABLE_NAME']} (onDelete: {$fk['DELETE_RULE']}, onUpdate: {$fk['UPDATE_RULE']})");
        }


// 恢復外鍵檢查
        DB::statement('SET foreign_key_checks = 1;');

        file_put_contents(
            base_path('database/backups/foreign_keys_backup.php'),
            '<?php return ' . var_export($foreignKeys, true) . ';'
        );



        $this->info('所有資料表欄位長度和類型更新完成，並已重新加回所有外鍵！');

    }


}
