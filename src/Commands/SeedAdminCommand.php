<?php

namespace Stephen<PERSON>org\BaseFilamentPlugin\Commands;

use Illuminate\Console\Command;
use Stephenchenorg\BaseFilamentPlugin\Models\Admin;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class SeedAdminCommand extends Command
{
    protected $signature = 'base:admin-create';
    protected $description = 'Create default hidden super admin and super admin based on config';

    public function handle(): void
    {
        $this->seedingSystemAdmin();
        $this->seedingAdmin();
    }

    /**
     * 建立系統隱藏最高管理員
     */
    protected function seedingSystemAdmin(): void
    {
        $role = Role::query()->firstOrCreate(['name' => 'Super Admin']);

        $email = config('app.admin.app_hidden_super_admin_account') ?: '<EMAIL>';
        $password = config('app.admin.app_hidden_super_admin_password') ?: 'admin';

        $admin = Admin::firstOrCreate(
            ['email' => $email],
            [
                'name'     => '系統最高隱藏管理員',
                'password' => $password,
            ]
        );

        $admin->assignRole($role);


        $role->permissions()->sync(Permission::all());

        $this->info("✅ 系統隱藏管理員建立完成：$email");

    }

    /**
     * 建立一般超級管理員
     */
    protected function seedingAdmin(): void
    {
        $role = Role::query()->firstOrCreate(['name' => 'admin']);

        $email = config('app.admin.app_super_admin_account') ?: '<EMAIL>';
        $password = config('app.admin.app_super_admin_password') ?: 'test';

        $user = Admin::firstOrCreate(
            ['email' => $email],
            [
                'name'     => '管理員',
                'password' => $password,
            ]
        );

        $user->assignRole($role);


        $role->permissions()->sync(Permission::all());

        $this->info("✅ 管理員建立完成：$email");
    }
}
