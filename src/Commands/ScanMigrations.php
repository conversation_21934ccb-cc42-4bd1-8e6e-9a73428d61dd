<?php

namespace Stephen<PERSON>org\BaseFilamentPlugin\Commands;

use Illuminate\Console\Command;
use Illuminate\Filesystem\Filesystem;

class ScanMigrations extends Command
{
    protected $signature = 'base:migrations-scan';
    protected $description = 'Scan all migrations and modify their content';

    public function handle(): void
    {
        $filesystem = new Filesystem();
        $migrationPath = database_path('migrations');
        $files = $filesystem->files($migrationPath);

        foreach ($files as $file) {
            $content = $filesystem->get($file);
            if (strpos($content, 'use CCTraitDatabaseContent;') !== false) {
                $this->info("Skipped: {$file->getFilename()} (Trait already exists)");
                continue;
            }
            $modifiedContent = $this->modifyMigrationContent($content);
            $filesystem->put($file, $modifiedContent);
            $this->info("Modified: {$file->getFilename()}");
        }
    }

    private function modifyMigrationContent(string $content): string
    {
        // Add "use CCTraitDatabaseAdmin;" after "return new class extends Migration"

        $content = preg_replace(
            '/(<?php)/',
            "$1\n\nuse Stephenchenorg\BaseFilamentPlugin\Traits\CCTraitDatabaseContent;",
            $content
        );


        $content = preg_replace(
            '/(extends Migration\s*\{)/',
            "$1\n\nuse CCTraitDatabaseContent;",
            $content
        );


        return $content;
    }
}
