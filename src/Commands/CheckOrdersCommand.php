<?php

namespace Stephenchenorg\BaseFilamentPlugin\Commands;

use Carbon\Carbon;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Stephenchenorg\BaseFilamentPlugin\Contracts\OrderServiceInterface;
use Stephenchenorg\BaseFilamentPlugin\Enum\EnumPaymentStatus;
use Stephenchenorg\BaseFilamentPlugin\Models\Order;
use TsaiYiHua\ECPay\QueryTradeInfo;

class CheckOrdersCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'base:check-orders';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '檢查兩天前未付款的訂單，查詢綠界API狀態並更新失敗的訂單';

    protected OrderServiceInterface $serviceOrder;
    protected QueryTradeInfo $queryTradeInfo;

    public function __construct(OrderServiceInterface $serviceOrder, QueryTradeInfo $queryTradeInfo)
    {
        parent::__construct();
        $this->serviceOrder = $serviceOrder;
        $this->queryTradeInfo = $queryTradeInfo;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('開始檢查訂單狀態...');

        try {
            // 查詢符合條件的訂單
            $orders = $this->getOrdersToCheck();

            if ($orders->isEmpty()) {
                $this->info('沒有需要檢查的訂單');
                return self::SUCCESS;
            }

            $this->info("找到 {$orders->count()} 筆需要檢查的訂單");

            $checkedCount = 0;
            $failedCount = 0;
            $errorCount = 0;

            foreach ($orders as $order) {
                try {
                    $this->line("檢查訂單: {$order->order_key}");

                    // 使用 TsaiYiHua\ECPay\QueryTradeInfo 查詢綠界API
                    $result = $this->queryTradeInfo->getData($order->order_key)->query();

                    if (empty($result)) {
                        // API查詢失敗，設定訂單為失敗狀態
                        $order->payment_status = EnumPaymentStatus::FAILED->value;
                        $order->save();
                        $this->warn("訂單 {$order->order_key} 查詢失敗，已設定為失敗狀態");
                        $failedCount++;
                    } else {
                        // API查詢成功，檢查交易狀態
                        $tradeStatus = $result['TradeStatus'] ?? null;

                        if ($tradeStatus === '1') {
                            // 交易成功，更新為已付款
                            $order->payment_status = EnumPaymentStatus::PAID->value;
                            $order->save();
                            $this->info("訂單 {$order->order_key} 已付款，狀態已更新");
                        } else {
                            // 交易失敗
                            $order->payment_status = EnumPaymentStatus::FAILED->value;
                            $order->save();
                            $this->warn("訂單 {$order->order_key} 交易失敗，已設定為失敗狀態");
                            $failedCount++;
                        }
                    }

                    $checkedCount++;

                    // 避免API請求過於頻繁
                    sleep(3);

                } catch (Exception $e) {
                    $this->error("處理訂單 {$order->order_key} 時發生錯誤: " . $e->getMessage());
                    Log::error("CheckOrdersCommand 處理訂單失敗", [
                        'order_key' => $order->order_key,
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);
                    $errorCount++;
                }
            }

            $this->info("檢查完成！");
            $this->info("總共檢查: {$checkedCount} 筆");
            $this->info("設定為失敗: {$failedCount} 筆");
            $this->info("處理錯誤: {$errorCount} 筆");

            return self::SUCCESS;

        } catch (Exception $e) {
            $this->error('執行失敗: ' . $e->getMessage());
            Log::error('CheckOrdersCommand 執行失敗', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return self::FAILURE;
        }
    }

    /**
     * 取得需要檢查的訂單
     */
    private function getOrdersToCheck()
    {
        $twoDaysAgo = Carbon::now()->subDays(2);

        return Order::query()
            ->whereNotNull('payment_gateway')
            ->where('payment_status', EnumPaymentStatus::UNPAID->value)
            ->where('created_at', '<=', $twoDaysAgo)
            ->get();
    }
}
