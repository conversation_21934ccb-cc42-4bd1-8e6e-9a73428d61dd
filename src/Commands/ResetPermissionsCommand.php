<?php

namespace Stephen<PERSON>org\BaseFilamentPlugin\Commands;

use <PERSON>chenorg\BaseFilamentPlugin\Database\Seeders\AdminSeeder;
use Stephenchenorg\BaseFilamentPlugin\Database\Seeders\PermissionSeeder;
use Stephenchenorg\BaseFilamentPlugin\Database\Seeders\RoleSeeder;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Artisan;
use Spatie\Permission\PermissionRegistrar;

class ResetPermissionsCommand extends Command
{
    protected $signature = 'base:permission-reset';
    protected $description = '清空權限相關表格並重新執行 Seeder';

    public function handle(): void
    {
        if ($this->confirm('⚠️ 你確定要重置權限？這將清空所有權限設定與帳號設定！', false)) {
            $this->reset();
        } else {
            $this->info('❌ 操作已取消');
        }


    }

    public function reset(): void
    {
        $this->info('🔄 開始清空權限相關表格...');

        // 暫時停用外鍵約束，確保能順利刪除資料
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');

        // 清空所有權限相關的表格
        DB::table('admins')->truncate();
        DB::table('model_has_permissions')->truncate();
        DB::table('model_has_roles')->truncate();
        DB::table('permissions')->truncate();
        DB::table('role_has_permissions')->truncate();
        DB::table('roles')->truncate();

        // 重新啟用外鍵約束
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        $this->info('✅ 清空完成！');

        $this->info('🗑️ 清除權限快取...');
        app()[PermissionRegistrar::class]->forgetCachedPermissions();


        // 重新執行 Seeder
        $this->info('🚀 重新執行 PermissionSeeder , RoleSeeder , AdminSeeder ...');
        (new PermissionSeeder())->run();
        (new RoleSeeder())->run();
        (new AdminSeeder())->run();
        $this->info('🎉 權限重置完成！');
    }
}
