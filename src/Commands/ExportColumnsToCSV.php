<?php

namespace Stephenchenorg\BaseFilamentPlugin\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use League\Csv\Writer;

class ExportColumnsToCSV extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'base:export-columns-to-csv';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {

        $outputFile = storage_path('app/file_output.csv');
        $csv = Writer::createFromPath($outputFile, 'w+');
        $csv->insertOne(['table', 'ID', 'column_name', 'value']);

        $imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.svg', '.webp'];
        $tables = DB::select('SHOW TABLES');

        foreach ($tables as $tableObj) {
            $table = array_values((array)$tableObj)[0];
            $columns = DB::getSchemaBuilder()->getColumnListing($table);

            if (empty($columns)) {
                continue;
            }

            $data = DB::table($table)->select($columns)->get();

            foreach ($data as $row) {
                foreach ($columns as $column) {

                    if (!isset($row->$column) || !is_string($row->$column) || str_contains($row->$column, 'http')) {
                        continue;
                    }

                    foreach ($imageExtensions as $ext) {
                        if (str_contains($row->$column, $ext)) {
                            $csv->insertOne([$table, $row->id , $column, $row->$column]);
                            break;
                        }
                    }
                }
            }
        }

        $this->info("Image references have been exported to {$outputFile}");
    }
}
