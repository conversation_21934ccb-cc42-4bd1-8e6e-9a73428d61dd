<?php

namespace Stephenchenorg\BaseFilamentPlugin\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class NullifyAdminIds extends Command
{
    protected $signature = 'base:nullify-admin-ids';
    protected $description = 'Set all created_by_admin_id and updated_by_admin_id fields to null in all tables';

    public function handle(): int
    {
        $tables = collect(DB::select('SHOW TABLES'))
            ->map(function ($row) {
                return array_values((array)$row)[0];
            })
            ->toArray();


        $nullifiedCount = 0;

        foreach ($tables as $table) {
            $columns = Schema::getColumnListing($table);

            $updates = [];
            if (in_array('created_by_admin_id', $columns)) {
                $updates['created_by_admin_id'] = null;
            }
            if (in_array('updated_by_admin_id', $columns)) {
                $updates['updated_by_admin_id'] = null;
            }

            if (!empty($updates)) {
                DB::table($table)->update($updates);
                $this->info("Updated `$table`");
                $nullifiedCount++;
            }
        }

        $this->info("✅ Done. Updated $nullifiedCount table(s).");
        return 0;
    }
}
