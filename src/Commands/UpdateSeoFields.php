<?php

namespace Stephenchenorg\BaseFilamentPlugin\Commands;

use Illuminate\Console\Command;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Schema;


class UpdateSeoFields extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'base:update-seo-fields';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check all migrations for $this->addSeoFields usage and extract table names';

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle(): void
    {
        $migrationsPath = database_path('migrations');
        $migrationFiles = File::allFiles($migrationsPath);

        $results = [];

        foreach ($migrationFiles as $file) {
            $content = File::get($file);

            if (preg_match('/\$this->addSeoFields\(\$table\);/', $content)) {
                // Extract table name from `create_table_name_table` or other migration patterns
                if (preg_match('/Schema::dropIfExists\(\'([^\']+)\'/', $content, $matches)) {
                    $tableName = $matches[1];
                    $results[] = $tableName;
                } else {
                    $this->warn("Could not determine table name in: {$file->getFilename()}");
                }
            }
        }

        if (empty($results)) {
            $this->info('No migrations using $this->addSeoFields were found.');
        } else {
            $this->info('Migrations using $this->addSeoFields were found for the following tables:');
            foreach ($results as $table) {
                $this->line("- {$table}");
                $this->addOgImageColumn($table);
            }
        }

    }

    /**
     * Dynamically add "og_image" column to the table.
     *
     * @param  string  $tableName
     * @return void
     */
    protected function addOgImageColumn(string $tableName): void
    {
        if (!Schema::hasTable($tableName)) {
            $this->warn("Table '{$tableName}' does not exist. Skipping.");
            return;
        }

        if (Schema::hasColumn($tableName, 'og_image')) {
            $this->warn("Column 'og_image' already exists in table '{$tableName}'. Skipping.");
            return;
        }

        Schema::table($tableName, function (Blueprint $table)
        {
            $table->string('og_image')->nullable()->comment('OG 圖片');
        });

        $this->info("Column 'og_image' has been added to table '{$tableName}'.");
    }
}
