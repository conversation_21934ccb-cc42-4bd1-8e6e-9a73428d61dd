<?php

namespace Stephenchenorg\BaseFilamentPlugin\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class ListImages extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'base:list-images';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '讀取指定資料夾中的圖片檔案並儲存結構為 JSON';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $directory = $this->ask('directory');

        if (!File::exists($directory)) {
            $this->error("資料夾 $directory 不存在");
            return;
        }


        // 讀取資料夾內的檔案及子資料夾，並過濾圖片檔案
        $fileStructure = $this->getImageFiles($directory);

        // 儲存成 JSON 檔案
        $jsonFilePath = database_path('seeders/prod/images_structure.json');
        file_put_contents($jsonFilePath, json_encode($fileStructure, JSON_PRETTY_PRINT));

        $this->info("圖片檔案結構已儲存至: $jsonFilePath");
    }

    private function getImageFiles($directory): array
    {
        $fileStructure = [];

        // 取得當前資料夾中的檔案
        $files = File::files($directory);

        // 只篩選出圖片檔案
        $imageFiles = array_filter($files, function ($file) {
            return in_array(strtolower($file->getExtension()), ['jpg', 'jpeg', 'png', 'gif']);
        });

        // 將圖片檔案名稱加入陣列
        $fileNames = array_map(function ($file) {
            return $file->getFilename();
        }, $imageFiles);

        // 如果有圖片檔案，將它們放入當前資料夾結構中
        if (!empty($fileNames)) {
            $fileStructure['files'] = $fileNames;
        }

        // 取得當前資料夾中的子資料夾
        $directories = File::directories($directory);
        foreach ($directories as $subDirectory) {
            // 遞迴處理子資料夾
            $fileStructure[basename($subDirectory)] = $this->getImageFiles($subDirectory);
        }

        return $fileStructure;
    }

}
