<?php

namespace <PERSON><PERSON>org\BaseFilamentPlugin\Listeners;

use <PERSON><PERSON>org\BaseFilamentPlugin\Events\OrderShippingStatusChanged;
use <PERSON><PERSON>org\BaseFilamentPlugin\Enum\EnumShippingStatus;
use Stephen<PERSON>org\BaseFilamentPlugin\Enum\EnumPaymentStatus;
use Stephenchenorg\BaseFilamentPlugin\Enum\EnumOrderStatus;
use Stephenchenorg\BaseFilamentPlugin\Models\Order;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductSpecification;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductSpecificationReservingItem;
use Illuminate\Support\Facades\DB;
use Exception;

class OrderShippingStatusListener
{

    /**
     * Handle the event.
     */
    public function handle(OrderShippingStatusChanged $event): void
    {
        $order = $event->order;
        $order = $order->refresh();

        try {
            DB::beginTransaction();

            // 當配送狀態為已發貨時，扣除庫存
            if ($order->shipping_status === EnumShippingStatus::SHIPPED->value) {
                $this->deductInventory($order);
            }

            // 當配送狀態為已完成且付款狀態為已付款時，將訂單狀態改為完成
            if ($order->shipping_status === EnumShippingStatus::COMPLETED->value &&
                $order->payment_status === EnumPaymentStatus::PAID->value
            ) {
                $order->status()->transitionTo(EnumOrderStatus::COMPLETED->value);
            }

            // 當配送狀態為無人取貨時，將訂單狀態改為失敗
            if ($order->shipping_status === EnumShippingStatus::PICKUP_FAILED->value) {
                $order->status()->transitionTo(EnumOrderStatus::FAILED->value);
            }

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            throw new Exception("處理配送狀態變更失敗: {$e->getMessage()}");
        }
    }

    /**
     * 扣除庫存
     *
     * @param Order $order 訂單物件
     * @throws Exception 如果庫存不足
     */
    private function deductInventory(Order $order): void
    {
        // 載入訂單項目
        $order->load('items');

        foreach ($order->items as $orderItem) {
            $productSpecificationId = $orderItem->product_specification_id;
            $quantity = $orderItem->quantity;

            // 使用 lockForUpdate 鎖定產品規格記錄
            $specification = ProductSpecification::query()
                ->where('id', $productSpecificationId)
                ->lockForUpdate()
                ->first();

            if (!$specification) {
                throw new Exception("產品規格不存在: ID {$productSpecificationId}");
            }

            // 檢查庫存是否足夠
            if ($specification->inventory < $quantity) {
                throw new Exception("產品規格 ID {$productSpecificationId} 庫存不足，當前庫存: {$specification->inventory}，需要: {$quantity}");
            }

            // 扣除實際庫存
            $specification->decrement('inventory', $quantity);
        }
    }


}
