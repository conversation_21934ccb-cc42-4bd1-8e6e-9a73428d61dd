<?php

namespace Stephenchenorg\BaseFilamentPlugin\Listeners;

use <PERSON>chenorg\BaseFilamentPlugin\Events\ProductCategorySaved;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceProductCategory;

class ProductCategorySequenceListener
{
    /**
     * Handle the event.
     */
    public function handle(ProductCategorySaved $event): void
    {
        $category = $event->category;

        if ($category->isDirty('parent_id')) {
            $targets = $category->descendantsWithSelf()->get();
            foreach ($targets as $target) {
                $target->sequence = ServiceProductCategory::getSequence($target);
                $target->save();
            }
        }
    }
}
