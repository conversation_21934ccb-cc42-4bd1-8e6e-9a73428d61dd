<?php

namespace <PERSON><PERSON>org\BaseFilamentPlugin\Listeners;

use <PERSON><PERSON>org\BaseFilamentPlugin\Events\FaqCreated;
use <PERSON><PERSON>or<PERSON>\BaseFilamentPlugin\Events\FaqDeleting;
use Stephen<PERSON>org\BaseFilamentPlugin\Events\FaqUpdated;
use <PERSON><PERSON>org\BaseFilamentPlugin\Models\FaqCategory;

class FaqCountListener
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(FaqCreated|FaqDeleting|FaqUpdated $event): void
    {
        match (true) {
            $event instanceof FaqCreated => $this->handleFaqCreated($event),
            $event instanceof FaqDeleting => $this->handleFaqDeleting($event),
            $event instanceof FaqUpdated => $this->handleFaqUpdated($event),
        };
    }

    /**
     * 處理 FaqCreated 事件
     */
    protected function handleFaqCreated(FaqCreated $event): void
    {
        $faq = $event->faq;
        FaqCategory::query()
            ->where('id', $faq->faq_category_id)
            ->increment('count_total');
    }

    /**
     * 處理 FaqDeleting 事件
     */
    protected function handleFaqDeleting(FaqDeleting $event): void
    {
        $faq = $event->faq;
        FaqCategory::query()
            ->where('id', $faq->faq_category_id)
            ->decrement('count_total');
    }

    /**
     * 處理 FaqUpdated 事件
     */
    protected function handleFaqUpdated(FaqUpdated $event): void
    {
        $faq = $event->faq;
        $originalCategoryId = $faq->getOriginal('faq_category_id');

        // 如果類別 ID 發生了變化，則更新計數
        if ($originalCategoryId !== $faq->faq_category_id) {
            // 更新舊類別，減少計數
            FaqCategory::query()
                ->where('id', $originalCategoryId)
                ->decrement('count_total');

            // 更新新類別，增加計數
            FaqCategory::query()
                ->where('id', $faq->faq_category_id)
                ->increment('count_total');
        }
    }
}
