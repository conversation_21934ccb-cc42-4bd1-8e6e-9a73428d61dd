<?php

namespace <PERSON><PERSON>org\BaseFilamentPlugin\Listeners;

use <PERSON><PERSON>org\BaseFilamentPlugin\Events\ArticleUpdated;
use <PERSON><PERSON>org\BaseFilamentPlugin\Events\ProductUpdated;
use Stephenchenorg\BaseFilamentPlugin\Models\Article;
use Stephenchenorg\BaseFilamentPlugin\Models\Product;

class ProductLimitListener
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(ProductUpdated $event): void
    {

        $product = $event->product;

        // 是否最熱門的數量檢查
        if ($product->isDirty('is_hottest')) {
            $this->handleIsHottestLimit();
        }

        // 是否最新的數量檢查
        if ($product->isDirty('is_newest')) {
            $this->handleIsNewestLimit();
        }

    }

    public function handleIsHottestLimit(): void
    {
        $selectedCount = Product::query()
            ->where('is_hottest', '=', true)
            ->count();


        if ($selectedCount > config('cs.product_hottest_limit')) {

            $oldest = Product::query()
                ->where('is_hottest', '=', true)
                ->oldest('updated_at')
                ->first();

            if ($oldest) {
                $oldest->update(['is_hottest' => false]);
            }
        }
    }

    public function handleIsNewestLimit(): void
    {
        $selectedCount = Product::query()
            ->where('is_newest', '=', true)
            ->count();


        if ($selectedCount > config('cs.product_newest_limit')) {

            $oldest = Product::query()
                ->where('is_newest', '=', true)
                ->oldest('updated_at')
                ->first();

            if ($oldest) {
                $oldest->update(['is_newest' => false]);
            }
        }
    }


}
