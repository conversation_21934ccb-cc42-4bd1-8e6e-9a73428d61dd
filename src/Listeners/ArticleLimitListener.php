<?php

namespace <PERSON>chenorg\BaseFilamentPlugin\Listeners;

use <PERSON><PERSON>org\BaseFilamentPlugin\Events\ArticleUpdated;
use Stephen<PERSON>org\BaseFilamentPlugin\Models\Article;

class ArticleLimitListener
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(ArticleUpdated $event): void
    {
        $article = $event->article;

        // 是否最熱門的數量檢查
        if ($article->isDirty('is_hottest')) {
            $this->handleIsHottestLimit();
        }

        // 是否最新的數量檢查
        if ($article->isDirty('is_newest')) {
            $this->handleIsNewestLimit();
        }
    }

    /**
     * 處理最熱門文章數量限制
     */
    protected function handleIsHottestLimit(): void
    {
        $selectedCount = Article::query()
            ->where('is_hottest', '=', true)
            ->count();

        if ($selectedCount > config('cs.article_hottest_limit')) {
            $oldest = Article::query()
                ->where('is_hottest', '=', true)
                ->oldest('updated_at')
                ->first();

            if ($oldest) {
                $oldest->update(['is_hottest' => false]);
            }
        }
    }

    /**
     * 處理最新文章數量限制
     */
    protected function handleIsNewestLimit(): void
    {
        $selectedCount = Article::query()
            ->where('is_newest', '=', true)
            ->count();

        if ($selectedCount > config('cs.article_newest_limit')) {
            $oldest = Article::query()
                ->where('is_newest', '=', true)
                ->oldest('updated_at')
                ->first();

            if ($oldest) {
                $oldest->update(['is_newest' => false]);
            }
        }
    }
}
