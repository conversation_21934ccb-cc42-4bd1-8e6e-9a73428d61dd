<?php

namespace Stephenchenorg\BaseFilamentPlugin\Listeners;

use Stephenchenorg\BaseFilamentPlugin\Events\ProductCategoryUpdated;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductCategory;

class ProductCategoryLimitListener
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(ProductCategoryUpdated $event): void
    {
        $productCategory = $event->category;

        // 是否最熱門的數量檢查
        if ($productCategory->isDirty('is_hottest')) {
            $this->handleIsHottestLimit($productCategory);
        }

        // 是否最新的數量檢查
        if ($productCategory->isDirty('is_newest')) {
            $this->handleIsNewestLimit($productCategory);
        }
    }

    /**
     * 處理最熱門產品分類數量限制
     */
    protected function handleIsHottestLimit(ProductCategory $category): void
    {
        $selectedCount = ProductCategory::query()
            ->where('is_hottest', '=', true)
            ->count();

        if ($selectedCount > config('cs.product_category_hottest_limit')) {
            $oldest = ProductCategory::query()
                ->where('is_hottest', '=', true)
                ->where('id', '!=', $category->id)
                ->oldest('updated_at')
                ->first();

            if (!empty($oldest->id)) {
                $oldest->is_hottest = false;
                $oldest->save();
            }
        }
    }

    /**
     * 處理最新產品分類數量限制
     */
    protected function handleIsNewestLimit(ProductCategory $category): void
    {
        $selectedCount = ProductCategory::query()
            ->where('is_newest', '=', true)
            ->count();

        if ($selectedCount > config('cs.product_category_newest_limit')) {
            $oldest = ProductCategory::query()
                ->where('is_newest', '=', true)
                ->where('id', '!=', $category->id)
                ->oldest('updated_at')
                ->first();

            if ($oldest) {
                $oldest->is_newest = false;
                $oldest->save();
            }
        }
    }
}
