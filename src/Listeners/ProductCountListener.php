<?php

namespace <PERSON><PERSON><PERSON>g\BaseFilamentPlugin\Listeners;

use <PERSON><PERSON>org\BaseFilamentPlugin\Events\ProductCategoryDeleted;
use Stephenchenorg\BaseFilamentPlugin\Events\ProductCategoryUpdated;
use Stephen<PERSON>org\BaseFilamentPlugin\Events\ProductCreated;
use Stephenchenorg\BaseFilamentPlugin\Events\ProductDeleting;
use Stephenchenorg\BaseFilamentPlugin\Events\ProductUpdated;
use Stephenchenorg\BaseFilamentPlugin\Events\TagAttached;
use Stephen<PERSON>org\BaseFilamentPlugin\Events\TagDetached;
use Stephen<PERSON>org\BaseFilamentPlugin\Models\Product;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductCategory;
use Stephenchenorg\BaseFilamentPlugin\Models\Tag;

class ProductCountListener
{
    /**
     * Handle the event.
     */
    public function handle(
        ProductCreated|ProductDeleting|ProductUpdated|TagAttached|TagDetached|ProductCategoryUpdated|ProductCategoryDeleted $event
    ): void
    {
        match (true) {
            $event instanceof ProductCreated => $this->handleProductCreated($event),
            $event instanceof ProductDeleting => $this->handleProductDeleting($event),
            $event instanceof ProductUpdated => $this->handleProductUpdated($event),
            $event instanceof TagAttached => $this->handleTagAttached($event),
            $event instanceof TagDetached => $this->handleTagDetached($event),
            $event instanceof ProductCategoryUpdated => $this->handleProductCategoryUpdated($event),
            $event instanceof ProductCategoryDeleted => $this->handleProductCategoryDeleted($event),
        };
    }

    /**
     * 處理 ProductCreated 事件
     */
    protected function handleProductCreated(ProductCreated $event): void
    {
        $product = $event->product;
        $category = ProductCategory::query()->find($product->product_category_id);

        if ($category) {
            $category->ancestorsWithSelf()->increment('count_total');
        }
    }

    /**
     * 處理 ProductDeleting 事件
     */
    protected function handleProductDeleting(ProductDeleting $event): void
    {
        $product = $event->product;

        $category = ProductCategory::query()->find($product->product_category_id);

        if ($category) {
            $category->ancestorsWithSelf()->decrement('count_total');
        }

        foreach ($product->tags as $tag) {
            $tag->decrement('count_total');
        }
    }

    /**
     * 處理 ProductUpdated 事件
     */
    protected function handleProductUpdated(ProductUpdated $event): void
    {
        $product = $event->product;

        if ($product->isDirty('product_category_id')) {
            $originalCategoryId = $product->getOriginal('product_category_id');
            $newCategoryId = $product->product_category_id;

            $oldCategory = ProductCategory::query()->find($originalCategoryId);
            $newCategory = ProductCategory::query()->find($newCategoryId);

            if ($oldCategory) {
                $oldCategory->ancestorsWithSelf()->decrement('count_total');
            }

            if ($newCategory) {
                $newCategory->ancestorsWithSelf()->increment('count_total');
            }
        }
    }


    protected function handleTagAttached(TagAttached $event): void
    {
        $pivot = $event->pivot;
        $taggable = $pivot->taggable;

        if ($taggable instanceof Product) {
            Tag::query()->where('id', $pivot->tag_id)->increment('product_count');
        }
    }

    protected function handleTagDetached(TagDetached $event): void
    {


        $pivot = $event->pivot;
        $taggable = $pivot->taggable;


        if ($taggable instanceof Product) {
            Tag::query()->where('id', $pivot->tag_id)->decrement('product_count');
        }
    }


    protected function handleProductCategoryUpdated(ProductCategoryUpdated $event): void
    {
        $productCategory = $event->category;

        if ($productCategory->isDirty('parent_id')) {
            $originalParentId = $productCategory->getOriginal('parent_id');
            $newParentId = $productCategory->parent_id;
            $categoryCountTotal = $productCategory->count_total;

            // 如果有舊的父分類，減去該分類的 count_total
            if ($originalParentId && $originalParentId !== -1) {
                $productCategory->ancestors()->decrement('count_total', $categoryCountTotal);
            }

            // 如果有新的父分類，增加該分類的 count_total
            if ($newParentId && $newParentId !== -1) {
                $productCategory->ancestors()->increment('count_total', $categoryCountTotal);
            }
        }
    }

    protected function handleProductCategoryDeleted(ProductCategoryDeleted $event): void
    {
        $productCategory = $event->category;
        $categoryCountTotal = $productCategory->count_total;
        $productCategory->ancestors()->decrement('count_total', $categoryCountTotal);
    }




}
