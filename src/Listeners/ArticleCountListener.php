<?php

namespace <PERSON><PERSON><PERSON>g\BaseFilamentPlugin\Listeners;

use <PERSON><PERSON><PERSON><PERSON>\BaseFilamentPlugin\Events\ArticleCreated;
use <PERSON><PERSON>or<PERSON>\BaseFilamentPlugin\Events\ArticleDeleting;
use Stephen<PERSON>org\BaseFilamentPlugin\Events\ArticleUpdated;
use <PERSON><PERSON>org\BaseFilamentPlugin\Events\TagAttached;
use <PERSON><PERSON>org\BaseFilamentPlugin\Events\TagDetached;
use Stephen<PERSON>org\BaseFilamentPlugin\Models\Article;
use Stephenchenorg\BaseFilamentPlugin\Models\ArticleCategory;
use Stephen<PERSON>org\BaseFilamentPlugin\Models\Tag;

class ArticleCountListener
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(ArticleCreated|ArticleDeleting|ArticleUpdated|TagAttached|TagDetached $event): void
    {
        match (true) {
            $event instanceof ArticleCreated => $this->handleArticleCreated($event),
            $event instanceof ArticleDeleting => $this->handleArticleDeleting($event),
            $event instanceof ArticleUpdated => $this->handleArticleUpdated($event),
            $event instanceof TagAttached => $this->handleTagAttached($event),
            $event instanceof TagDetached => $this->handleTagDetached($event),
        };
    }


    /**
     * 處理 ArticleCreated 事件
     */
    protected function handleArticleCreated(ArticleCreated $event): void
    {
        $article = $event->article;

        ArticleCategory::query()
            ->where('id', $article->article_category_id)
            ->increment('count_total');
    }

    /**
     * 處理 ArticleDeleting 事件
     */
    protected function handleArticleDeleting(ArticleDeleting $event): void
    {
        $article = $event->article;

        ArticleCategory::query()
            ->where('id', $article->article_category_id)
            ->decrement('count_total');

        foreach ($article->tags as $tag) {
            $tag->decrement('article_count');
        }
    }

    /**
     * 處理 ArticleUpdated 事件
     */
    protected function handleArticleUpdated(ArticleUpdated $event): void
    {
        $article = $event->article;

        // 如果分類有變更
        if ($article->isDirty('article_category_id')) {
            $originalCategoryId = $article->getOriginal('article_category_id');
            $newCategoryId = $article->article_category_id;

            ArticleCategory::query()
                ->where('id', $originalCategoryId)
                ->decrement('count_total');

            ArticleCategory::query()
                ->where('id', $newCategoryId)
                ->increment('count_total');
        }
    }

    /**
     * 處理 TagAttached 事件
     */
    protected function handleTagAttached(TagAttached $event): void
    {
        $pivot = $event->pivot;
        $taggable = $pivot->taggable;

        if ($taggable instanceof Article) {
            Tag::query()->where('id', $pivot->tag_id)->increment('article_count');
        }
    }

    /**
     * 處理 TagDetached 事件
     */
    protected function handleTagDetached(TagDetached $event): void
    {
        $pivot = $event->pivot;
        $taggable = $pivot->taggable;

        if ($taggable instanceof Article) {
            Tag::query()->where('id', $pivot->tag_id)->decrement('article_count');
        }
    }


}
