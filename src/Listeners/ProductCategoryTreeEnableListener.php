<?php

namespace <PERSON><PERSON>org\BaseFilamentPlugin\Listeners;

use <PERSON><PERSON>org\BaseFilamentPlugin\Events\ProductCategorySaved;
use Stephen<PERSON>org\BaseFilamentPlugin\Events\ProductSaved;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceProductCategory;

class ProductCategoryTreeEnableListener
{
    /**
     * Handle the ProductCategorySaved event.
     */
    public function handle(ProductCategorySaved|ProductSaved $event): void
    {
        if($event instanceof ProductSaved){
            $this->handleProductSaved($event);
        }
        else if($event instanceof ProductCategorySaved){
            $this->handleProductCategorySaved($event);
        }
    }

    public function handleProductSaved(ProductSaved $event): void
    {
        $product = $event->product;


        if ($product->isDirty('product_category_id') || $product->wasRecentlyCreated) {
            $product->refresh();
            if ($product->category && ServiceProductCategory::isAncestorsAllEnabled($product->category)) {
                $product->update(['is_all_ancestors_enabled' => 1]);
            } else {
                $product->update(['is_all_ancestors_enabled' => 0]);
            }
        }
    }

    public function handleProductCategorySaved(ProductCategorySaved $event): void
    {
        $category = $event->category;

        if ($category->isDirty('parent_id')) {

            if (ServiceProductCategory::isAncestorsAllEnabled($category)) {
                ServiceProductCategory::enabledRelatedProducts($category);
            }
            else
            {
                ServiceProductCategory::disabledRelatedProducts($category);
            }
        }
        else if ($category->isDirty('status')) {
            if ($category->status === 0) {
                ServiceProductCategory::disabledRelatedProducts($category);
            }
            else if (ServiceProductCategory::isAncestorsAllEnabled($category)) {
                ServiceProductCategory::enabledRelatedProducts($category);
            }
        }
    }


}
