<?php

namespace Stephenchenorg\BaseFilamentPlugin\Events;

use Stephenchenorg\BaseFilamentPlugin\Models\Article;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ArticleCreated
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * Create a new event instance.
     */
    public Article $article;

    public function __construct(Article $article)
    {
        $this->article = $article;
    }


}
