<?php

namespace Stephenchenorg\BaseFilamentPlugin\Events;

use Stephenchenorg\BaseFilamentPlugin\Models\ArticleCategory;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ArticleCategoryDeleted
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * Create a new event instance.
     */
    public ArticleCategory $category;

    public function __construct(ArticleCategory $category)
    {
        $this->category = $category;
    }

}
