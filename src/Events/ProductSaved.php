<?php

namespace Stephenchenorg\BaseFilamentPlugin\Events;

use Stephenchenorg\BaseFilamentPlugin\Models\Product;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ProductSaved
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * Create a new event instance.
     */
    public Product $product;

    public function __construct(Product $product)
    {
        $this->product = $product;
    }
}
