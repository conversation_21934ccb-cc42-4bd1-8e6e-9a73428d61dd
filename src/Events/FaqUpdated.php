<?php

namespace Stephenchenorg\BaseFilamentPlugin\Events;


use Stephenchenorg\BaseFilamentPlugin\Models\Faq;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class FaqUpdated
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * Create a new event instance.
     */
    public Faq $faq;

    public function __construct(Faq $faq)
    {
        $this->faq = $faq;
    }


}
