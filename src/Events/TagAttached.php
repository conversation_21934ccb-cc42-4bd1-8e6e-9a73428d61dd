<?php

namespace Stephenchenorg\BaseFilamentPlugin\Events;

use Stephenchenorg\BaseFilamentPlugin\Models\TagPivot;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class TagAttached
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * Create a new event instance.
     */
    public TagPivot $pivot;

    public function __construct(TagPivot $pivot)
    {
        $this->pivot = $pivot;
    }


}
