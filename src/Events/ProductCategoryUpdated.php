<?php

namespace Stephenchenorg\BaseFilamentPlugin\Events;

use Stephenchenorg\BaseFilamentPlugin\Models\ProductCategory;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ProductCategoryUpdated
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * Create a new event instance.
     */
    public ProductCategory $category;

    public function __construct(ProductCategory $category)
    {
        $this->category = $category;
    }

}
