<?php

namespace Stephenchenorg\BaseFilamentPlugin\DTO\Order;

/**
 * 訂單項目列表 DTO
 */
class OrderItemListDTO
{
    /**
     * @param OrderItemDTO[] $items
     */
    public function __construct(
        public readonly array $items
    ) {}

    /**
     * 從陣列創建 DTO
     */
    public static function fromArray(array $data): self
    {
        $items = array_map(fn($item) => OrderItemDTO::fromArray($item), $data);
        return new self(items: $items);
    }

    /**
     * 轉換為陣列
     */
    public function toArray(): array
    {
        return array_map(fn($dto) => $dto->toArray(), $this->items);
    }
}
