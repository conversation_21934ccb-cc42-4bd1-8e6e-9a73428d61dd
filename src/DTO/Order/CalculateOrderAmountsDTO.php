<?php

namespace Stephenchenorg\BaseFilamentPlugin\DTO\Order;

/**
 * 計算訂單金額 DTO
 */
class CalculateOrderAmountsDTO
{
    public function __construct(
        public readonly OrderItemListDTO $items,
        public readonly string $invoice_method,
        public readonly string $shipping_method,
        public readonly int $redeem_points = 0
    ) {}

    /**
     * 從陣列創建 DTO
     */
    public static function fromArray(array $data): self
    {
        return new self(
            items: OrderItemListDTO::fromArray($data['items']),
            invoice_method: $data['invoice_method'],
            shipping_method: $data['shipping_method'],
            redeem_points: $data['redeem_points'] ?? 0
        );
    }

    /**
     * 轉換為陣列
     */
    public function toArray(): array
    {
        return [
            'items' => $this->items->toArray(),
            'invoice_method' => $this->invoice_method,
            'shipping_method' => $this->shipping_method,
            'redeem_points' => $this->redeem_points,
        ];
    }
}
