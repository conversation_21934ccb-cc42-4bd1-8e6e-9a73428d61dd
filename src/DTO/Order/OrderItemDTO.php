<?php

namespace Stephenchenorg\BaseFilamentPlugin\DTO\Order;

/**
 * 訂單項目 DTO
 */
class OrderItemDTO
{
    public function __construct(
        public readonly int $product_specification_id,
        public readonly int $quantity
    ) {}

    /**
     * 從陣列創建 DTO
     */
    public static function fromArray(array $data): self
    {
        return new self(
            product_specification_id: $data['product_specification_id'],
            quantity: $data['quantity']
        );
    }

    /**
     * 轉換為陣列
     */
    public function toArray(): array
    {
        return [
            'product_specification_id' => $this->product_specification_id,
            'quantity' => $this->quantity,
        ];
    }


}
