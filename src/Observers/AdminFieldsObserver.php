<?php

namespace Stephenchenorg\BaseFilamentPlugin\Observers;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;

class AdminFieldsObserver
{
    /**
     * @param  Model  $model
     * @return void
     */
    public function creating(Model $model): void
    {
        if (Auth::check()) {
            $model->created_by_admin_id = Auth::id();
            $model->updated_by_admin_id = Auth::id();
        }
    }

    /**
     * @param  Model  $model
     * @return void
     */
    public function saving(Model $model): void
    {
        if (Auth::check()) {
            $model->updated_by_admin_id = Auth::id();
        }
    }
}
