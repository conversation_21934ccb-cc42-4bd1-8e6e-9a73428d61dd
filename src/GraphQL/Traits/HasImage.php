<?php

namespace Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits;

use Closure;
use Rebing\GraphQL\Support\Facades\GraphQL;

trait HasImage
{
    public function getBackgroundArgs(): array
    {
        return [
            'background' => [
                'type'        => GraphQL::type('Background'),
                'description' => 'background',
                'resolve'     => function ($root, $args)
                {
                    return $root;
                },
            ],
        ];
    }

    public function getCoverArgs(): array
    {
        return [
            'cover' => [
                'type'        => GraphQL::type('Cover'),
                'description' => 'cover',
                'resolve'     => function ($root, $args)
                {
                    return $root;
                },
            ],
        ];
    }


    /**
     * @param  Closure|null  $resolve
     * @return array[]
     */
    public function getImageArgs(Closure $resolve = null): array
    {
        $resolve = $resolve ?? function ($root, $args)
        {
            return $root;
        };

        return [
            'image' => [
                'type'        => GraphQL::type('Image'),
                'description' => 'image',
                'resolve'     => $resolve,
            ],
        ];
    }

}
