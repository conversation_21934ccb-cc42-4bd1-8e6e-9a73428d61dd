<?php

namespace Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits;

use GraphQL\Type\Definition\Type;
use Illuminate\Database\Eloquent\Builder;

trait HasNewest
{
    public function getNewestArgs(): array
    {
        return [
            'is_newest' => [
                'name' => 'is_newest',
                'type' => Type::boolean(),
            ],
        ];
    }

    /**
     * @param Builder $query
     * @param bool $value
     * @return Builder
     */
    public function filterNewestQuery(Builder $query, bool $value): Builder
    {
        return $query->where('is_newest', '=', $value);
    }


}
