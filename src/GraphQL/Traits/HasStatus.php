<?php

namespace Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits;

use Illuminate\Database\Eloquent\Builder;

trait HasStatus
{
    /**
     * @param $query
     * @param string|null $categoryRelation
     * @param bool $isTree
     * @return mixed
     */
    public function filterEnabledQuery($query, string $categoryRelation = null, bool $isTree = false): mixed
    {
        if (empty($categoryRelation)) return $query->where('status', '=', 1);

        if (!$isTree) {
            return $query
                ->where('status', '=', 1)
                ->whereHas($categoryRelation, function ($query) {
                    return $query->where('status', '=', 1);
                });
        }

        return $query->where('status', '=', 1)
            ->where('is_all_ancestors_enabled', '=', 1);
    }
}
