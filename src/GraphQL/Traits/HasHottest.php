<?php

namespace Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits;

use GraphQL\Type\Definition\Type;
use Illuminate\Database\Eloquent\Builder;

trait HasHottest
{
    public function getHottestArgs(): array
    {
        return [
            'is_hottest' => [
                'name' => 'is_hottest',
                'type' => Type::boolean(),
            ],
        ];
    }

    /**
     * @param Builder $query
     * @param bool $value
     * @return Builder
     */
    public function filterHottestQuery(Builder $query, bool $value): Builder
    {
        return $query->where('is_hottest', '=', $value);
    }


}
