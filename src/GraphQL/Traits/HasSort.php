<?php

namespace Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits;

use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;
use Stephenchenorg\BaseFilamentPlugin\Models\Article;
use GraphQL\Type\Definition\Type;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Schema;
use Rebing\GraphQL\Support\Facades\GraphQL;

trait HasSort
{
    public function getSortArgs(): array
    {
        return [
            'sort_by'     => [
                'name'         => 'sort_by',
                'type'         => Type::string(),
                'defaultValue' => 'asc',
                'rules'        => ['nullable', 'in:asc,desc'],
            ],
            'sort_column' => [
                'name'         => 'sort_column',
                'type'         => Type::string(),
                'defaultValue' => 'id',
                'rules'        => ['nullable', 'in:created_at,sort,id'],
            ],
        ];
    }

    /**
     * @param  string  $type
     * @return array
     */
    public function getPrevNextFields(string $type): array
    {
        return [
            'prev' => [
                'type'        => GraphQL::type($type),
                'description' => 'The previous article',
            ],
            'next' => [
                'type'        => GraphQL::type($type),
                'description' => 'The next article',
            ],
        ];
    }

    /**
     * @param  mixed  $query
     * @param  string  $sortColumn
     * @param  string  $sortBy
     * @return mixed
     */
    public function sortQuery(mixed $query, string $sortColumn, string $sortBy): mixed
    {
        return $query->orderBy($sortColumn, $sortBy);
    }

    /**
     * @param  Model  $record
     * @param  string  $sortColumn
     * @param  string  $sortBy
     * @param  string|null  $foreignKey
     * @return Builder
     */
    public function getPrev(Model $record, string $sortColumn, string $sortBy, string $foreignKey = null): Builder
    {
        $model = get_class($record);

        $query = $model::query()
            ->when(Schema::hasColumn($record->getTable(), 'status'), function ($query)
            {
                return $query->where('status', 1);
            })
            ->where('id', '!=', $record->id)
            ->orderBy($sortColumn, $this->getOpposite($sortBy))
            ->when($sortBy === 'asc', function ($query) use ($sortColumn, $record)
            {
                return $query->where($sortColumn, '<', $record->$sortColumn);
            })
            ->when($sortBy === 'desc', function ($query) use ($sortColumn, $record)
            {
                return $query->where($sortColumn, '>', $record->$sortColumn);
            });

        if (!empty($foreignKey)) {
            $query->where($foreignKey, '=', $record->$foreignKey);
        }

        return $query;
    }

    /**
     * @param  Model  $record
     * @param  string  $sortColumn
     * @param  string  $sortBy
     * @param  string|null  $foreignKey
     * @return Builder
     */
    public function getNext(Model $record, string $sortColumn, string $sortBy, string $foreignKey = null): Builder
    {
        $model = get_class($record);

        $query = $model::query()
            ->when(Schema::hasColumn($record->getTable(), 'status'), function ($query)
            {
                return $query->where('status', 1);
            })
            ->where('id', '!=', $record->id)
            ->orderBy($sortColumn, $sortBy)
            ->when($sortBy === 'asc', function ($query) use ($sortColumn, $record)
            {
                return $query->where($sortColumn, '>', $record->$sortColumn);
            })
            ->when($sortBy === 'desc', function ($query) use ($sortColumn, $record)
            {
                return $query->where($sortColumn, '<', $record->$sortColumn);
            });

        if (!empty($foreignKey)) {
            $query->where($foreignKey, '=', $record->$foreignKey);
        }

        return $query;
    }


    /**
     * @param $sortBy
     * @return string
     */
    public function getOpposite($sortBy): string
    {
        if ($sortBy === 'asc') {
            return 'desc';
        }
        if ($sortBy === 'desc') {
            return 'asc';
        }

        return 'asc';
    }


}
