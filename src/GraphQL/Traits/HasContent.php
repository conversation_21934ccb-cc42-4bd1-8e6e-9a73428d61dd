<?php

namespace Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits;

use GraphQL\Type\Definition\Type;
use Illuminate\Database\Eloquent\Builder;

trait HasContent
{
    public function getPlainTextArgs(string $column): array
    {
        return [
            $column => [
                'type'        => Type::string(),
                'description' => "The $column",
                'resolve'     => function ($root, $args) use ($column)
                {
                    if (empty($root->{$column})) {
                        return null;
                    }
                    $result = e($root->{$column});
                    $result = nl2br($result, false);
                    return str_replace("\n", '', $result);
                },
            ],
        ];
    }

}
