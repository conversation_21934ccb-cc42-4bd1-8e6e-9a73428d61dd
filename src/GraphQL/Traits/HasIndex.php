<?php

namespace Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits;

use GraphQL\Type\Definition\Type;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Schema;
use Matrix\Exception;
use Rebing\GraphQL\Support\Facades\GraphQL;

trait HasIndex
{
    /**
     * @param  array  $indices
     * @return array
     */
    public function getIndexArgs(array $indices = ['id']): array
    {
        $args = [];

        foreach ($indices as $index) {

            if ($index === 'id') {
                $args['id'] = [
                    'name' => 'id',
                    'type' => Type::int(),
                ];
            } else {
                $args[$index] = [
                    'name' => $index,
                    'type' => Type::string(),
                ];
            }
        }
        return $args;
    }


    /**
     * Apply filter for a given field.
     *
     * @param  Builder  $query
     * @param  array  $args
     * @param  array  $allowedFilters
     * @return Builder
     * @throws Exception
     */
    public function applyFieldFilter(Builder $query, array $args, array $allowedFilters = []): Builder
    {

        foreach ($args as $field => $value) {
            if ($value && in_array($field, $allowedFilters)) {
                return $query->where($field, $value);
            }
        }

        $argMessages = '('.implode(',', $allowedFilters).')';
        throw new Exception("請至少提供下列參數之一 : $argMessages");
    }


}
