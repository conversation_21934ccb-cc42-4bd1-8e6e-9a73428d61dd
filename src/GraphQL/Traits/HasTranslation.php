<?php

namespace Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\App;
use Rebing\GraphQL\Support\Type as GraphQLType;

trait HasTranslation
{
    /**
     * @param $root
     * @param $args
     * @param $field
     * @param $fieldValue
     * @return mixed
     */
    public function resolveTranslation($root, $args, $field, $fieldValue): mixed
    {
        $translation = $root->translations->first();

        if (!empty($fieldValue['resolve'])) {
            return $fieldValue['resolve']($translation, $args);
        }


        return $translation->$field ?? null;
    }

    /**
     * 動態生成欄位
     *
     * @param GraphQLType $type
     * @return array
     */
    public function getTranslatableFields(GraphQLType $type): array
    {
        $fields = $type->fields();

        $result = [];

        foreach ($fields as $field => $fieldValue) {

            if (in_array($field, ['id', 'created_at', 'updated_at'])) {
                continue;
            }

            $result[$field] = [
                'type' => $fieldValue['type'],
                'description' => "The $field translation",
                'resolve' => function ($root, $args) use ($fieldValue, $field) {
                    return $this->resolveTranslation($root, $args, $field, $fieldValue);
                },
            ];
        }

        return $result;
    }


    /**
     * @param Builder $query
     * @param array $relations
     * @return Builder
     */
    public function translateQuery(Builder $query, array $relations = ['translations']): Builder
    {
        $lang = App::getLocale();
        
        if (empty($relations)) {
            $query = $query->where('lang', '=', $lang);
        }

        foreach ($relations as $relation) {
            $query = $query->with([
                $relation => function ($query) use ($lang) {
                    $query->where('lang', '=', $lang);
                },
            ]);
        }

        return $query;
    }


}
