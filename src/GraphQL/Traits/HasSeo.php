<?php

namespace Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits;

use GraphQL\Type\Definition\Type;
use Illuminate\Database\Eloquent\Builder;

trait HasSeo
{
    public function getSeoArgs(): array
    {
        return [
            'seo_title'       => [
                'type'        => Type::string(),
                'description' => 'The SEO title',
            ],
            'seo_description' => [
                'type'        => Type::string(),
                'description' => 'The SEO description',
            ],
            'seo_keyword'     => [
                'type'        => Type::string(),
                'description' => 'The SEO keywords',
            ],
            'seo_head'        => [
                'type'        => Type::string(),
                'description' => 'The SEO head content',
            ],
            'seo_body'        => [
                'type'        => Type::string(),
                'description' => 'The SEO body content',
            ],
            'seo_json_ld'     => [
                'type'        => Type::string(),
                'description' => 'The SEO JSON-LD data',
            ],
            'og_title'        => [
                'type'        => Type::string(),
                'description' => 'The OG title',
            ],
            'og_image'        => [
                'type'        => Type::string(),
                'description' => 'The OG image',
            ],
            'og_description'  => [
                'type'        => Type::string(),
                'description' => 'The OG description',
            ],
        ];
    }

}
