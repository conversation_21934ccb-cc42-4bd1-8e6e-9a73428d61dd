<?php

namespace Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits;

use GraphQL\Type\Definition\Type;
use Illuminate\Database\Eloquent\Builder;

trait HasTags
{
    public function getPaginationArgs(): array
    {
        return [
            'union_tags'     => [
                'name'        => 'union_tags',
                'type'        => Type::listOf(Type::int()),
                'description' => 'Filter by at least one of these tag IDs',
            ],
            'intersect_tags' => [
                'name'        => 'intersect_tags',
                'type'        => Type::listOf(Type::int()),
                'description' => 'Filter by all of these tag IDs',
            ],
        ];
    }

    public function filterUnionTagsQuery(Builder $query, array $unionTags): array
    {
        return $query->paginate($perPage, ['*'], 'page', $page)->items();
    }
}
