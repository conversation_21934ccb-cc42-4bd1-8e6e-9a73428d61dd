<?php

namespace Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits;

use Carbon\Carbon;
use GraphQL\Type\Definition\Type;
use Illuminate\Database\Eloquent\Builder;

trait HasTimeInterval
{
    public function getTimeIntervalArgs(): array
    {
        return [
            'started_at' => [
                'name'         => 'started_at',
                'type'         => Type::string(),
                'defaultValue' => null,
            ],
            'ended_at'   => [
                'name'         => 'ended_at',
                'type'         => Type::string(),
                'defaultValue' => null,
            ],
        ];
    }

    /**
     * @param  Builder  $query
     * @param ?string  $startTime
     * @param ?string  $endTime
     * @return Builder
     */
    public function filterCreatedTimeQuery(Builder $query, ?string $startTime, ?string $endTime): Builder
    {
        if (!empty($startTime)) {
            $query->where('created_at', '>=', $startTime);
        }

        if (!empty($endTime)) {
            $query->where('created_at', '<=', $endTime);
        }

        return $query;
    }

    /**
     * @param  Builder  $query
     * @return Builder
     */
    public function filterTimeIntervalQuery(Builder $query): Builder
    {
        $query->where(function ($query)
        {
            $query->where('started_at', '<=', Carbon::now()->endOfDay())
                ->orWhereNull('started_at');
        });

        $query->where(function ($query)
        {
            $query->where('ended_at', '>=', Carbon::now()->startOfDay())
                ->orWhereNull('ended_at');
        });

        return $query;
    }
}
