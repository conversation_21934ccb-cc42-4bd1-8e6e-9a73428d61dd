<?php

namespace Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits;

use GraphQL\Type\Definition\Type;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;

trait HasPagination
{
    public function getPaginationArgs(): array
    {
        return [
            'page'     => [
                'name'         => 'page',
                'type'         => Type::int(),
                'defaultValue' => 1,
            ],
            'per_page' => [
                'name'         => 'per_page',
                'type'         => Type::int(),
                'defaultValue' => 15,
            ],
        ];
    }

    public function paginateQuery($query, int $perPage = 15, int $page = 1): LengthAwarePaginator
    {
        return $query->paginate($perPage, ['*'], 'page', $page);
    }
}
