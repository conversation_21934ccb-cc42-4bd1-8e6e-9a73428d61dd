<?php

namespace <PERSON>org\BaseFilamentPlugin\GraphQL\Mutations;

use Stephenchenorg\BaseFilamentPlugin\Enum\EnumContactType;
use <PERSON><PERSON>org\BaseFilamentPlugin\GraphQL\EnumNames;
use Stephenchenorg\BaseFilamentPlugin\Models\Contact;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceStorage;
use Exception;
use GraphQL\Type\Definition\Type;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Rebing\GraphQL\Support\Facades\GraphQL;
use Rebing\GraphQL\Support\Mutation;

class CreateContactMutation extends Mutation
{
    protected $attributes = [
        'name' => EnumNames::CreateContact,
        'description' => 'Create a new contact with file upload',
    ];

    public function type(): Type
    {
        return Type::int();
    }

    public function args(): array
    {
        return [
            'files' => [
                'name' => 'files',
                'type' => Type::listOf(GraphQL::type('Upload')),
                'rules' => ['array', 'max:5'],
            ],
            'file_path' => [
                'name' => 'file_path',
                'type' => Type::string(),
                'rules' => [
                    'max:4096',
                    'exists' => function ($attributes, $value, $fail) {
                        if (!ServiceStorage::checkFileExist($value)) {
                            $fail(trans('validation.custom.file_path.exists'));
                        }
                    }],
            ],
            'name' => [
                'name' => 'name',
                'type' => Type::string(),
                'rules' => ['required', 'max:255'],
            ],
            'phone' => [
                'name' => 'phone',
                'type' => Type::string(),
                'rules' => ['max:255'],
            ],
            'email' => [
                'name' => 'email',
                'type' => Type::string(),
                'rules' => ['max:255', 'email'],
            ],
            'line' => [
                'name' => 'line',
                'type' => Type::string(),
                'rules' => ['max:255'],
            ],
            'address' => [
                'name' => 'address',
                'type' => Type::string(),
                'rules' => ['max:255'],
            ],
            'title' => [
                'name' => 'title',
                'type' => Type::string(),
                'rules' => ['required', 'max:255'],

            ],
            'content' => [
                'name' => 'content',
                'type' => Type::string(),
                'rules' => ['max:4096'],
            ],
        ];
    }

    /**
     * @throws Exception
     */
    public function resolve($root, $args): int
    {
        try {

            DB::beginTransaction();

            $contact = new Contact();
            $contact->type = EnumContactType::CONTACT_US;
            $contact->name = $args['name'] ?? null;
            $contact->phone = $args['phone'] ?? null;
            $contact->email = $args['email'] ?? null;
            $contact->line = $args['line'] ?? null;
            $contact->address = $args['address'] ?? null;
            $contact->title = $args['title'] ?? null;
            $contact->content = $args['content'] ?? null;
            $contact->save();

            if (isset($args['file_path'])) {

                $tempPath = $args['file_path'];
                $fileName = basename($tempPath);
                $targetPath = 'uploads/contact/' . $fileName;

                ServiceStorage::commitTempFile($tempPath, $targetPath);
                $contact->files()->create([
                    'file' => $targetPath,
                ]);

            }

            if (isset($args['files'])) {
                foreach ($args['files'] as $file) {

                    Validator::make(
                        ['file' => $file],
                        ['file' => 'required|file|mimetypes:image/jpeg,image/png,application/pdf|max:2048'],
                    )->validate();

                    $filePath = $file->store('contacts', config('filament.default_filesystem_disk'));
                    $contact->files()->create([
                        'file' => $filePath,
                    ]);
                }
            }

            DB::commit();

        } catch (Exception $e) {

            DB::rollBack();

            throw $e;

        }

        return $contact->id;
    }
}
