<?php

namespace <PERSON><PERSON>org\BaseFilamentPlugin\GraphQL\Mutations\Client;

use <PERSON>chenorg\BaseFilamentPlugin\GraphQL\Base\BaseClientJwtMutation;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;
use <PERSON><PERSON>org\BaseFilamentPlugin\Service\Auth\ServiceClient;
use Exception;
use GraphQL\Type\Definition\Type;

class ClientLogoutMutation extends BaseClientJwtMutation
{
    protected $attributes = [
        'name' => EnumNames::ClientLogout,
        'description' => 'Logout a client and invalidate their token',
    ];

    /**
     * @var ServiceClient
     */
    private ServiceClient $service;

    /**
     * @param ServiceClient $service
     */
    public function __construct(ServiceClient $service)
    {
        $this->service = $service;
    }

    public function type(): Type
    {
        return Type::string();
    }

    public function args(): array
    {
        return [];
    }

    /**
     * @throws Exception
     */
    public function resolve($root, $args): string
    {
        $this->service->logout();
        return __('base-filament-plugin::auth.logout_success');
    }
}
