<?php

namespace <PERSON>org\BaseFilamentPlugin\GraphQL\Mutations\Client;

use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;
use <PERSON><PERSON>org\BaseFilamentPlugin\Service\Auth\ServiceClient;
use Exception;
use GraphQL\Type\Definition\Type;
use Rebing\GraphQL\Support\Mutation;

class ClientReadyToForgetMutation extends Mutation
{
    protected $attributes = [
        'name' => EnumNames::ClientReadyToForget,
        'description' => 'Send password reset email to client',
    ];

    /**
     * @var ServiceClient
     */
    private ServiceClient $service;

    /**
     * @param ServiceClient $service
     */
    public function __construct(ServiceClient $service)
    {
        $this->service = $service;
    }

    public function type(): Type
    {
        return Type::string();
    }

    public function args(): array
    {
        return [
            'email' => [
                'name' => 'email',
                'type' => Type::string(),
                'rules' => ['required', 'max:255', 'email'],
            ],
        ];
    }

    /**
     * @throws Exception
     */
    public function resolve($root, $args): string
    {
        $this->service->readyToForgetPassword($args);
        return __('base-filament-plugin::auth.verification_email_sent');
    }
}
