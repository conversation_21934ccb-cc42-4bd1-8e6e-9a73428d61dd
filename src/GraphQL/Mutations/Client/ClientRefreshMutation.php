<?php

namespace <PERSON>g\BaseFilamentPlugin\GraphQL\Mutations\Client;

use <PERSON><PERSON>org\BaseFilamentPlugin\GraphQL\Base\BaseClientJwtMutation;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;
use <PERSON><PERSON>org\BaseFilamentPlugin\Service\Auth\ServiceClient;
use Exception;
use GraphQL\Type\Definition\Type;
use Rebing\GraphQL\Support\Facades\GraphQL;

class ClientRefreshMutation extends BaseClientJwtMutation
{
    protected $attributes = [
        'name' => EnumNames::Token,
        'description' => 'Refresh a client JWT token',
    ];

    /**
     * @var ServiceClient
     */
    private ServiceClient $service;

    /**
     * @param ServiceClient $service
     */
    public function __construct(ServiceClient $service)
    {
        $this->service = $service;
    }

    public function type(): Type
    {
        return GraphQL::type(EnumNames::Token);
    }

    public function args(): array
    {
        return [];
    }

    /**
     * @throws Exception
     */
    public function resolve($root, $args): array
    {
        return $this->service->refresh();
    }
}
