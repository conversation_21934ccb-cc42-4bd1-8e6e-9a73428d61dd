<?php

namespace <PERSON>org\BaseFilamentPlugin\GraphQL\Mutations\Client;

use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;
use <PERSON><PERSON>org\BaseFilamentPlugin\Service\Auth\ServiceClient;
use Exception;
use GraphQL\Type\Definition\Type;
use Rebing\GraphQL\Support\Facades\GraphQL;
use Rebing\GraphQL\Support\Mutation;

class ClientLoginMutation extends Mutation
{
    protected $attributes = [
        'name' => EnumNames::ClientLogin,
        'description' => 'B to B Login',
    ];

    /**
     * @var ServiceClient
     */
    private ServiceClient $service;

    /**
     * @param ServiceClient $service
     */
    public function __construct(ServiceClient $service)
    {
        $this->service = $service;
    }

    public function type(): Type
    {
        return GraphQL::type(EnumNames::ClientLogin);
    }

    public function args(): array
    {
        return [
            'email' => [
                'name' => 'email',
                'type' => Type::string(),
                'rules' => ['max:255', 'email'],
            ],
            'phone' => [
                'name' => 'phone',
                'type' => Type::string(),
                'rules' => ['max:255', 'email'],
            ],
            'password' => [
                'name' => 'password',
                'type' => Type::string(),
                'rules' => ['max:255'],
            ],
        ];
    }

    /**
     * @throws Exception
     * @throws \Throwable
     */
    public function resolve($root, $args): array
    {
        return $this->service->login($args);
    }
}
