<?php

namespace Stephen<PERSON>org\BaseFilamentPlugin\GraphQL\Mutations\Customer;

use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;
use <PERSON>chenorg\BaseFilamentPlugin\Service\Auth\ServiceCustomer;
use Exception;
use GraphQL\Type\Definition\Type;
use Rebing\GraphQL\Support\Mutation;

class CustomerRegisterMutation extends Mutation
{
    protected $attributes = [
        'name' => EnumNames::CustomerRegister,
        'description' => 'Register a new customer account',
    ];

    /**
     * @var ServiceCustomer
     */
    private ServiceCustomer $service;

    /**
     * @param ServiceCustomer $service
     */
    public function __construct(ServiceCustomer $service)
    {
        $this->service = $service;
    }

    public function type(): Type
    {
        return Type::string();
    }

    public function args(): array
    {
        return [
            'email' => [
                'name' => 'email',
                'type' => Type::string(),
                'rules' => ['required', 'max:255', 'email'],
            ],
            'password' => [
                'name' => 'password',
                'type' => Type::string(),
                'rules' => ['required', 'min:8', 'max:20', 'regex:/^[A-Za-z0-9]{8,20}$/'],
            ],
            'password_confirmation' => [
                'name' => 'password_confirmation',
                'type' => Type::string(),
                'rules' => ['required','same:password'],
            ],
            'code' => [
                'name' => 'code',
                'type' => Type::string(),
                'rules' => ['required', 'max:255'],
            ],
        ];
    }

    /**
     * @throws Exception
     * @throws \Throwable
     */
    public function resolve($root, $args): string
    {
        $this->service->register($args);
        return __('base-filament-plugin::auth.registration_success');
    }
}
