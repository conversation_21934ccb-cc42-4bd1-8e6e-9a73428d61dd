<?php

namespace <PERSON>org\BaseFilamentPlugin\GraphQL\Mutations\Customer;

use Stephenchenorg\BaseFilamentPlugin\GraphQL\Base\BaseCustomerJwtMutation;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;
use <PERSON>chenorg\BaseFilamentPlugin\Service\Auth\ServiceCustomer;
use Exception;
use GraphQL\Type\Definition\Type;

class CustomerLogoutMutation extends BaseCustomerJwtMutation
{
    protected $attributes = [
        'name' => EnumNames::CustomerLogout,
        'description' => 'Logout a customer and invalidate their token',
    ];

    /**
     * @var ServiceCustomer
     */
    private ServiceCustomer $service;

    /**
     * @param ServiceCustomer $service
     */
    public function __construct(ServiceCustomer $service)
    {
        $this->service = $service;
    }

    public function type(): Type
    {
        return Type::string();
    }

    public function args(): array
    {
        return [];
    }

    /**
     * @throws Exception
     */
    public function resolve($root, $args): string
    {
        $this->service->logout();
        return __('base-filament-plugin::auth.logout_success');
    }
}
