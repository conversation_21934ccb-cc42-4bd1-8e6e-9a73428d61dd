<?php

namespace <PERSON>org\BaseFilamentPlugin\GraphQL\Mutations\Customer;

use Stephenchenorg\BaseFilamentPlugin\GraphQL\Base\BaseCustomerJwtMutation;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;
use <PERSON>chenorg\BaseFilamentPlugin\Service\Auth\ServiceCustomer;
use Exception;
use GraphQL\Type\Definition\Type;
use Rebing\GraphQL\Support\Facades\GraphQL;

class CustomerRefreshMutation extends BaseCustomerJwtMutation
{
    protected $attributes = [
        'name' => EnumNames::Token,
        'description' => 'Refresh a customer JWT token',
    ];

    /**
     * @var ServiceCustomer
     */
    private ServiceCustomer $service;

    /**
     * @param ServiceCustomer $service
     */
    public function __construct(ServiceCustomer $service)
    {
        $this->service = $service;
    }

    public function type(): Type
    {
        return GraphQL::type(EnumNames::Token);
    }

    public function args(): array
    {
        return [];
    }

    /**
     * @throws Exception
     */
    public function resolve($root, $args): array
    {
        return $this->service->refresh();
    }
}
