<?php

namespace <PERSON>org\BaseFilamentPlugin\GraphQL\Mutations\Customer;

use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;
use <PERSON><PERSON>org\BaseFilamentPlugin\Service\Auth\ServiceCustomer;
use Exception;
use GraphQL\Type\Definition\Type;
use Rebing\GraphQL\Support\Mutation;

class CustomerValidateEmailMutation extends Mutation
{
    protected $attributes = [
        'name' => EnumNames::CustomerValidateEmail,
        'description' => 'Validates email and sends verification code',
    ];

    /**
     * @var ServiceCustomer
     */
    private ServiceCustomer $service;

    /**
     * @param ServiceCustomer $service
     */
    public function __construct(ServiceCustomer $service)
    {
        $this->service = $service;
    }

    public function type(): Type
    {
        return Type::string();
    }

    public function args(): array
    {
        return [
            'email' => [
                'name' => 'email',
                'type' => Type::string(),
                'rules' => ['required', 'max:255', 'email'],
            ],
        ];
    }

    /**
     * @throws Exception
     */
    public function resolve($root, $args): string
    {
        $this->service->validateEmail($args['email']);
        return __('base-filament-plugin::auth.verification_email_sent');
    }
}
