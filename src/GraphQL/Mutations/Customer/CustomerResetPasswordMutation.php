<?php

namespace Stephen<PERSON>org\BaseFilamentPlugin\GraphQL\Mutations\Customer;

use Stephenchenorg\BaseFilamentPlugin\GraphQL\Base\BaseCustomerJwtMutation;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;
use <PERSON><PERSON>org\BaseFilamentPlugin\Service\Auth\ServiceCustomer;
use Exception;
use GraphQL\Type\Definition\Type;

class CustomerResetPasswordMutation extends BaseCustomerJwtMutation
{
    protected $attributes = [
        'name' => EnumNames::CustomerResetPassword,
        'description' => 'Reset password for authenticated customer',
    ];

    /**
     * @var ServiceCustomer
     */
    private ServiceCustomer $service;

    /**
     * @param ServiceCustomer $service
     */
    public function __construct(ServiceCustomer $service)
    {
        $this->service = $service;
    }

    public function type(): Type
    {
        return Type::string();
    }

    public function args(): array
    {
        return [
            'old_password' => [
                'name' => 'old_password',
                'type' => Type::string(),
                'rules' => ['required', 'max:255'],
            ],
            'password' => [
                'name' => 'password',
                'type' => Type::string(),
                'rules' => ['required', 'min:8', 'max:20', 'confirmed', 'regex:/^[A-Za-z0-9]{8,20}$/'],
            ],
            'password_confirmation' => [
                'name' => 'password_confirmation',
                'type' => Type::string(),
                'rules' => ['required', 'max:255'],
            ],
        ];
    }

    /**
     * @throws Exception
     */
    public function resolve($root, $args): string
    {
        $this->service->resetPassword($args);
        return __('base-filament-plugin::auth.password_reset_success');
    }
}
