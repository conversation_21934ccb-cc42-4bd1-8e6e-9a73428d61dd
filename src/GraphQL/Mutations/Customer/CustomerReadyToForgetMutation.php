<?php

namespace <PERSON>org\BaseFilamentPlugin\GraphQL\Mutations\Customer;

use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;
use <PERSON><PERSON>org\BaseFilamentPlugin\Service\Auth\ServiceCustomer;
use Exception;
use GraphQL\Type\Definition\Type;
use Rebing\GraphQL\Support\Mutation;

class CustomerReadyToForgetMutation extends Mutation
{
    protected $attributes = [
        'name' => EnumNames::CustomerReadyToForget,
        'description' => 'Send password reset email to customer',
    ];

    /**
     * @var ServiceCustomer
     */
    private ServiceCustomer $service;

    /**
     * @param ServiceCustomer $service
     */
    public function __construct(ServiceCustomer $service)
    {
        $this->service = $service;
    }

    public function type(): Type
    {
        return Type::string();
    }

    public function args(): array
    {
        return [
            'email' => [
                'name' => 'email',
                'type' => Type::string(),
                'rules' => ['required', 'max:255', 'email'],
            ],
        ];
    }

    /**
     * @throws Exception
     */
    public function resolve($root, $args): string
    {
        $this->service->readyToForgetPassword($args);
        return __('base-filament-plugin::auth.verification_email_sent');
    }
}
