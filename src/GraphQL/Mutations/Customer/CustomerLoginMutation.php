<?php

namespace Stephen<PERSON>org\BaseFilamentPlugin\GraphQL\Mutations\Customer;

use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;
use <PERSON><PERSON>org\BaseFilamentPlugin\Service\Auth\ServiceCustomer;
use Exception;
use GraphQL\Type\Definition\Type;
use Rebing\GraphQL\Support\Facades\GraphQL;
use Rebing\GraphQL\Support\Mutation;

class CustomerLoginMutation extends Mutation
{
    protected $attributes = [
        'name' => EnumNames::CustomerLogin,
        'description' => 'B to C Login',
    ];

    /**
     * @var ServiceCustomer
     */
    private ServiceCustomer $service;

    /**
     * @param ServiceCustomer $service
     */
    public function __construct(ServiceCustomer $service)
    {
        $this->service = $service;
    }

    public function type(): Type
    {
        return GraphQL::type(EnumNames::CustomerLogin);
    }

    public function args(): array
    {
        return [
            'email' => [
                'name' => 'email',
                'type' => Type::string(),
                'rules' => ['max:255', 'email'],
            ],
            'phone' => [
                'name' => 'phone',
                'type' => Type::string(),
                'rules' => ['max:255', 'email'],
            ],
            'password' => [
                'name' => 'password',
                'type' => Type::string(),
                'rules' => ['max:255'],
            ],
        ];
    }

    /**
     * @throws Exception
     * @throws \Throwable
     */
    public function resolve($root, $args): array
    {
        return $this->service->login($args);
    }
}
