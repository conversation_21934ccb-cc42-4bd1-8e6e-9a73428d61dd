<?php

// app/graphql/queries/quest/QuestsQuery

namespace Stephenchenorg\BaseFilamentPlugin\GraphQL\Queries\Banner;

use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasPagination;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasSort;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasStatus;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasTranslation;
use Stephenchenorg\BaseFilamentPlugin\Models\Article;
use Stephenchenorg\BaseFilamentPlugin\Models\Banner;
use GraphQL\Type\Definition\Type;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Rebing\GraphQL\Support\Facades\GraphQL;
use Rebing\GraphQL\Support\Query;

class BannersQuery extends Query
{
    use HasPagination, HasSort, HasStatus;

    protected $attributes = [
        'name' => EnumNames::Banners,
    ];

    public function args(): array
    {
        return [
            ...$this->getSortArgs(),
            ...$this->getPaginationArgs(),
        ];
    }

    public function type(): Type
    {
        return GraphQL::paginate(EnumNames::Banner);
    }

    /**
     * @param $root
     * @param $args
     * @return LengthAwarePaginator
     */
    public function resolve($root, $args): LengthAwarePaginator
    {
        $query = Banner::query();

        $query = $this->filterEnabledQuery($query);

        $query = $this->sortQuery($query, $args['sort_column'], $args['sort_by']);

        return $this->paginateQuery($query, $args['per_page'], $args['page']);
    }
}
