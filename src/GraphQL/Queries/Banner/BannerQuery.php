<?php

// app/graphql/queries/quest/QuestQuery

namespace Stephen<PERSON>org\BaseFilamentPlugin\GraphQL\Queries\Banner;

use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;
use <PERSON><PERSON>org\BaseFilamentPlugin\GraphQL\Traits\HasStatus;
use <PERSON><PERSON>org\BaseFilamentPlugin\Models\Banner;
use Stephenchenorg\BaseFilamentPlugin\Models\Faq;
use GraphQL\Type\Definition\Type;
use Rebing\GraphQL\Support\Facades\GraphQL;
use Rebing\GraphQL\Support\Query;

class BannerQuery extends Query
{
    use HasStatus;

    protected $attributes = [
        'name' => EnumNames::Banner,
    ];

    public function type(): Type
    {
        return GraphQL::type(EnumNames::Banner);
    }

    public function args(): array
    {
        return [
            'id' => [
                'name'  => 'id',
                'type'  => Type::int(),
                'rules' => ['required'],
            ],
        ];
    }

    public function resolve($root, $args)
    {
        $query =  Banner::query();

        $query = $this->filterEnabledQuery($query);

        return $query->findOrFail($args['id']);
    }
}
