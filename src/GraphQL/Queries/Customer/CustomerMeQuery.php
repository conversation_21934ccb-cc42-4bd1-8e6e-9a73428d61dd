<?php

namespace <PERSON>g\BaseFilamentPlugin\GraphQL\Queries\Customer;

use Stephenchenorg\BaseFilamentPlugin\GraphQL\Base\BaseCustomerJwtQuery;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;
use <PERSON>chenorg\BaseFilamentPlugin\Service\Auth\ServiceCustomer;
use Exception;
use GraphQL\Type\Definition\Type;
use Rebing\GraphQL\Support\Facades\GraphQL;

class CustomerMeQuery extends BaseCustomerJwtQuery
{
    protected $attributes = [
        'name' => EnumNames::CustomerMe,
        'description' => 'Get current authenticated customer information',
    ];

    /**
     * @var ServiceCustomer
     */
    private ServiceCustomer $service;

    /**
     * @param ServiceCustomer $service
     */
    public function __construct(ServiceCustomer $service)
    {
        $this->service = $service;
    }

    public function type(): Type
    {
        return GraphQL::type(EnumNames::CustomerLogin);
    }

    public function args(): array
    {
        return [];
    }

    /**
     * @throws Exception
     */
    public function resolve($root, $args): array
    {
        return $this->service->me();
    }
}
