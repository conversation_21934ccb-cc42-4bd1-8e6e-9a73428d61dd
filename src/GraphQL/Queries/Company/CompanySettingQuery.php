<?php

// app/graphql/queries/quest/QuestQuery

namespace Stephenchenorg\BaseFilamentPlugin\GraphQL\Queries\Company;

use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;
use <PERSON><PERSON>org\BaseFilamentPlugin\GraphQL\Traits\HasTranslation;
use Stephenchenorg\BaseFilamentPlugin\Models\CompanySetting;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceLanguage;
use GraphQL\Type\Definition\Type;
use Rebing\GraphQL\Support\Facades\GraphQL;
use Rebing\GraphQL\Support\Query;

class CompanySettingQuery extends Query
{
    use HasTranslation;

    protected $attributes = [
        'name' => EnumNames::CompanySetting,
    ];

    public function type(): Type
    {
        return GraphQL::type(EnumNames::CompanySetting);
    }

    public function args(): array
    {
        return [
        ];
    }

    public function resolve($root, $args)
    {
        $query = CompanySetting::query();

        $query = $this->translateQuery($query, []);

        return $query->firstOrFail();
    }
}
