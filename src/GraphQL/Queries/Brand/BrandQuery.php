<?php

// app/graphql/queries/quest/QuestQuery

namespace Stephen<PERSON>org\BaseFilamentPlugin\GraphQL\Queries\Brand;

use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasStatus;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasTranslation;
use Stephenchenorg\BaseFilamentPlugin\Models\Brand;
use Stephenchenorg\BaseFilamentPlugin\Models\Faq;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceLanguage;
use GraphQL\Type\Definition\Type;
use Rebing\GraphQL\Support\Facades\GraphQL;
use Rebing\GraphQL\Support\Query;

class BrandQuery extends Query
{
    use HasTranslation,HasStatus;

    protected $attributes = [
        'name' => EnumNames::Brand,
    ];

    public function type(): Type
    {
        return GraphQL::type(EnumNames::Brand);
    }

    public function args(): array
    {
        return [
            'id' => [
                'name'  => 'id',
                'type'  => Type::int(),
                'rules' => ['required'],
            ],
        ];
    }

    public function resolve($root, $args)
    {
        $query = Brand::query();

        $query = $this->translateQuery($query);

        $query = $this->filterEnabledQuery($query);

        return $query->findOrFail($args['id']);
    }
}
