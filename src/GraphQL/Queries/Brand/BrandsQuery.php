<?php

// app/graphql/queries/quest/QuestsQuery

namespace Stephenchenorg\BaseFilamentPlugin\GraphQL\Queries\Brand;

use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasPagination;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasSort;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasStatus;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasTranslation;
use Stephenchenorg\BaseFilamentPlugin\Models\Brand;
use Stephenchenorg\BaseFilamentPlugin\Models\Faq;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceLanguage;
use GraphQL\Type\Definition\Type;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Rebing\GraphQL\Support\Facades\GraphQL;
use Rebing\GraphQL\Support\Query;

class BrandsQuery extends Query
{
    use HasPagination, HasSort, HasTranslation, HasStatus;

    protected $attributes = [
        'name' => EnumNames::Brands,
    ];

    public function args(): array
    {
        return [

            ...$this->getSortArgs(),
            ...$this->getPaginationArgs(),

        ];
    }


    public function type(): Type
    {
        return GraphQL::paginate(EnumNames::Brand);
    }

    /**
     * @param $root
     * @param $args
     * @return LengthAwarePaginator
     */
    public function resolve($root, $args): LengthAwarePaginator
    {
        $query = Brand::query();

        $query = $this->translateQuery($query);

        $query = $this->sortQuery($query, $args['sort_column'], $args['sort_by']);

        return $this->paginateQuery($query, $args['per_page'], $args['page']);
    }
}
