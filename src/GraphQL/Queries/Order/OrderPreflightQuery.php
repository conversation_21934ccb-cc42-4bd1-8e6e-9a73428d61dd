<?php

namespace <PERSON>g\BaseFilamentPlugin\GraphQL\Queries\Order;

use Exception;
use GraphQL\Type\Definition\Type;
use Illuminate\Validation\Rule;
use Rebing\GraphQL\Support\Facades\GraphQL;
use Rebing\GraphQL\Support\Query;
use Stephen<PERSON>org\BaseFilamentPlugin\Contracts\OrderServiceInterface;
use Stephenchenorg\BaseFilamentPlugin\Enum\EnumInvoiceMethod;
use Stephenchenorg\BaseFilamentPlugin\Enum\EnumShippingMethod;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;

class OrderPreflightQuery extends Query
{
    protected $attributes = [
        'name' => EnumNames::OrderPreflight,
        'description' => 'Get order preflight information including amount calculation and available shipping methods',
    ];

    /**
     * @var OrderServiceInterface
     */
    private OrderServiceInterface $serviceOrder;

    /**
     * @param OrderServiceInterface $serviceOrder
     */
    public function __construct(OrderServiceInterface $serviceOrder)
    {
        $this->serviceOrder = $serviceOrder;
    }

    public function type(): Type
    {
        return GraphQL::type(EnumNames::OrderPreflight);
    }

    public function args(): array
    {
        return [

            // 購物車項目
            'items' => [
                'name' => 'items',
                'type' => Type::listOf(GraphQL::type('OrderItemInput')),
                'rules' => ['required', 'array', 'min:1', 'max:15'],
            ],

            // 發票方式
            'invoice_method' => [
                'name' => 'invoice_method',
                'type' => Type::string(),
                'rules' => [
                    'required',
                    Rule::in(EnumInvoiceMethod::getAvailableMethodValues())
                ],
            ],

            // 運送方式
            'shipping_method' => [
                'name' => 'shipping_method',
                'type' => Type::string(),
                'rules' => [
                    'required',
                    Rule::in(EnumShippingMethod::getAvailableMethodValues())
                ],
            ],

            // 點數折抵
            'redeem_points' => [
                'name' => 'redeem_points',
                'type' => Type::int(),
                'defaultValue' => 0,
                'rules' => ['integer', 'min:0'],
            ],
        ];
    }

    /**
     * @throws Exception
     */
    public function resolve($root, $args): array
    {
        // 檢查庫存
        $this->serviceOrder->checkInventory($args['items']);

        // 檢查紅利點數
        $maxRedeemablePoints = $this->serviceOrder->getMaxRedeemablePoints($args['items']);
        if (isset($args['redeem_points']) && $args['redeem_points'] > $maxRedeemablePoints) {
            throw new Exception("最多可使用 {$maxRedeemablePoints} 點數折抵");
        }

        // 檢查物流方式
        $availableShippingMethodAndCost = $this->serviceOrder->getAvailableShippingMethodAndCost($args['items']);
        if (!isset($availableShippingMethodAndCost[$args['shipping_method']])) {
            throw new Exception("不支援的物流方式 {$args['shipping_method']}");
        }


        // 計算訂單金額
        $orderAmounts = $this->serviceOrder->calculateOrderAmounts(
            $args['items'],
            $args['shipping_method'],
            $args['invoice_method'],
            $args['redeem_points'],
        );


        // 獲取所有可用的運送方式和費用
        $shippingMethods = [];
        foreach ($availableShippingMethodAndCost as $type => $cost) {
            $shippingMethods[] = [
                'type' => $type,
                'cost' => $cost,
            ];
        }

        return [
            'amount' => $orderAmounts,
            'shipping_methods' => $shippingMethods,
            'max_redeemable_points' => $maxRedeemablePoints,
        ];
    }

}
