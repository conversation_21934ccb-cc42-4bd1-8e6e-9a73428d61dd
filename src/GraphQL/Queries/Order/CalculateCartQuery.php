<?php

namespace <PERSON>g\BaseFilamentPlugin\GraphQL\Queries\Order;

use Exception;
use GraphQL\Type\Definition\Type;
use Illuminate\Validation\Rule;
use Rebing\GraphQL\Support\Facades\GraphQL;
use Rebing\GraphQL\Support\Query;
use Stephen<PERSON>org\BaseFilamentPlugin\Contracts\OrderServiceInterface;
use Stephenchenorg\BaseFilamentPlugin\Enum\EnumInvoiceMethod;
use Stephenchenorg\BaseFilamentPlugin\Enum\EnumShippingMethod;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;

class CalculateCartQuery extends Query
{
    protected $attributes = [
        'name' => EnumNames::CalculateCart,
        'description' => 'Calculate cart amounts including tax and shipping',
    ];

    /**
     * @var OrderServiceInterface
     */
    private OrderServiceInterface $serviceOrder;

    /**
     * @param OrderServiceInterface $serviceOrder
     */
    public function __construct(OrderServiceInterface $serviceOrder)
    {
        $this->serviceOrder = $serviceOrder;
    }

    public function type(): Type
    {
        return GraphQL::type(EnumNames::OrderAmount);
    }

    public function args(): array
    {
        return [

            // 購物車項目
            'items' => [
                'name' => 'items',
                'type' => Type::listOf(GraphQL::type('OrderItemInput')),
                'rules' => ['required', 'array', 'min:1', 'max:15'],
            ],

            // 發票方式 (必填)
            'invoice_method' => [
                'name' => 'invoice_method',
                'type' => Type::string(),
                'rules' => [
                    'required',
                    Rule::in(EnumInvoiceMethod::getAvailableMethodValues())
                ],
            ],

            // 運送方式 (必填)
            'shipping_method' => [
                'name' => 'shipping_method',
                'type' => Type::string(),
                'rules' => [
                    'required',
                    Rule::in(EnumShippingMethod::getAvailableMethodValues())
                ],
            ],

            // 點數折抵
            'redeem_points' => [
                'name' => 'redeem_points',
                'type' => Type::int(),
                'defaultValue' => 0,
                'rules' => ['integer', 'min:0'],
            ],
        ];
    }

    /**
     * @throws Exception
     */
    public function resolve($root, $args): array
    {
        $maxRedeemablePoints = $this->serviceOrder->getMaxRedeemablePoints($args['items']);

        if (isset($args['redeem_points']) && $args['redeem_points'] > $maxRedeemablePoints) {
            throw new Exception("最多可使用 {$maxRedeemablePoints} 點數折抵");
        }

        $availableShippingMethodAndCost = $this->serviceOrder->getAvailableShippingMethodAndCost($args['items']);
        if (!isset($availableShippingMethodAndCost[$args['shipping_method']])) {
            throw new Exception("不支援的物流方式 {$args['shipping_method']}");
        }

        $amounts = $this->serviceOrder->calculateOrderAmounts(
            $args['items'],
            $args['shipping_method'],
            $args['invoice_method'],
            $args['redeem_points'],
        );

        return [
            'item_amount' => $amounts['item_amount'],
            'tax' => $amounts['tax'],
            'total_amount_untaxed' => $amounts['total_amount_untaxed'],
            'total_amount_taxed' => $amounts['total_amount_taxed'],
            'shipping_cost' => $amounts['shipping_cost'],
        ];
    }
}
