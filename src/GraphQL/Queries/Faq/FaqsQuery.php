<?php

// app/graphql/queries/quest/QuestsQuery

namespace Stephen<PERSON>org\BaseFilamentPlugin\GraphQL\Queries\Faq;

use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasPagination;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasSort;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasStatus;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasTranslation;
use Stephenchenorg\BaseFilamentPlugin\Models\Faq;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceLanguage;
use GraphQL\Type\Definition\Type;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Rebing\GraphQL\Support\Facades\GraphQL;
use Rebing\GraphQL\Support\Query;

class FaqsQuery extends Query
{
    use HasPagination, HasSort, HasTranslation, HasStatus;

    protected $attributes = [
        'name' => EnumNames::Faqs,
    ];

    public function args(): array
    {
        return [

            ...$this->getSortArgs(),
            ...$this->getPaginationArgs(),

            'category_id' => [
                'name'        => 'category_id',
                'type'        => Type::int(),
                'description' => 'Filter by category ID',
            ],

        ];
    }


    public function type(): Type
    {
        return GraphQL::paginate(EnumNames::Faq);
    }

    /**
     * @param $root
     * @param $args
     * @return LengthAwarePaginator
     */
    public function resolve($root, $args): LengthAwarePaginator
    {
        $query = Faq::query();


        if (isset($args['category_id'])) {
            $query->where('faq_category_id', $args['category_id']);
        }

        $query = $this->filterEnabledQuery($query,'category');

        $query = $this->translateQuery($query,['translations', 'category.translations']);

        $query = $this->sortQuery($query, $args['sort_column'], $args['sort_by']);

        return $this->paginateQuery($query, $args['per_page'], $args['page']);
    }
}
