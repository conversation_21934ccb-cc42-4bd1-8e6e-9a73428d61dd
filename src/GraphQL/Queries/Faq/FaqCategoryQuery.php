<?php

// app/graphql/queries/quest/QuestsQuery

namespace Stephen<PERSON>org\BaseFilamentPlugin\GraphQL\Queries\Faq;

use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasPagination;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasSort;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasStatus;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasTranslation;
use Stephenchenorg\BaseFilamentPlugin\Models\FaqCategory;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceLanguage;
use GraphQL\Type\Definition\Type;
use Rebing\GraphQL\Support\Facades\GraphQL;
use Rebing\GraphQL\Support\Query;

class FaqCategoryQuery extends Query
{
    use HasTranslation,HasStatus;

    protected $attributes = [
        'name' => EnumNames::FaqCategory,
    ];

    public function type(): Type
    {
        return GraphQL::type(EnumNames::FaqCategory);
    }

    public function args(): array
    {
        return [
            'id' => [
                'name'  => 'id',
                'type'  => Type::int(),
                'rules' => ['required'],
            ],
        ];
    }

    public function resolve($root, $args)
    {
        $query = FaqCategory::query();

        $query = $this->translateQuery($query, ['translations', 'faqs.translations']);

        $query = $this->filterEnabledQuery($query);

        return $query->findOrFail($args['id']);
    }
}
