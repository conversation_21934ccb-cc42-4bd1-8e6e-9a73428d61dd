<?php

// app/graphql/queries/quest/QuestQuery

namespace Stephen<PERSON>org\BaseFilamentPlugin\GraphQL\Queries\Faq;

use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;
use <PERSON><PERSON>org\BaseFilamentPlugin\GraphQL\Traits\HasStatus;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasTranslation;
use Stephenchenorg\BaseFilamentPlugin\Models\Faq;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceLanguage;
use GraphQL\Type\Definition\Type;
use Rebing\GraphQL\Support\Facades\GraphQL;
use Rebing\GraphQL\Support\Query;

class FaqQuery extends Query
{
    use HasTranslation,HasStatus;

    protected $attributes = [
        'name' => EnumNames::Faq,
    ];

    public function type(): Type
    {
        return GraphQL::type(EnumNames::Faq);
    }

    public function args(): array
    {
        return [
            'id' => [
                'name'  => 'id',
                'type'  => Type::int(),
                'rules' => ['required'],
            ],
        ];
    }

    public function resolve($root, $args)
    {
        $query = Faq::query();

        $query = $this->translateQuery($query, ['translations', 'category.translations']);

        $query = $this->filterEnabledQuery($query,'category');

        return $query->findOrFail($args['id']);
    }
}
