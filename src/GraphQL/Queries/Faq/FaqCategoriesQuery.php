<?php

// app/graphql/queries/quest/QuestsQuery

namespace Stephenchenorg\BaseFilamentPlugin\GraphQL\Queries\Faq;

use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasPagination;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasSort;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasStatus;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasTranslation;
use Stephenchenorg\BaseFilamentPlugin\Models\FaqCategory;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceLanguage;
use GraphQL\Type\Definition\Type;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Rebing\GraphQL\Support\Facades\GraphQL;
use Rebing\GraphQL\Support\Query;

class FaqCategoriesQuery extends Query
{
    use HasPagination, HasSort, HasTranslation, HasStatus;

    protected $attributes = [
        'name' => EnumNames::FaqCategories,
    ];

    public function args(): array
    {
        return [
            ...$this->getSortArgs(),
            ...$this->getPaginationArgs(),
        ];
    }


    public function type(): Type
    {
        return GraphQL::paginate(EnumNames::FaqCategory);
    }


    /**
     * @param $root
     * @param $args
     * @return LengthAwarePaginator
     */
    public function resolve($root, $args): LengthAwarePaginator
    {
        $query = FaqCategory::query();

        $query = $this->filterEnabledQuery($query);

        $query = $this->translateQuery($query,['translations', 'faqs.translations']);

        $query = $this->sortQuery($query, $args['sort_column'], $args['sort_by']);

        return $this->paginateQuery($query, $args['per_page'], $args['page']);
    }
}
