<?php

// app/graphql/queries/quest/QuestQuery

namespace Stephen<PERSON>org\BaseFilamentPlugin\GraphQL\Queries\Product;

use GraphQL\Type\Definition\Type;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Rebing\GraphQL\Support\Facades\GraphQL;
use Rebing\GraphQL\Support\Query;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasHottest;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasNewest;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasPagination;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasSort;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasStatus;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasTimeInterval;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasTranslation;
use Stephenchenorg\BaseFilamentPlugin\Models\Product;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductCategory;

class ProductsQuery extends Query
{
    use HasPagination, HasSort, HasTranslation, HasStatus, HasTimeInterval, HasHottest, HasNewest;

    protected $attributes = [
        'name' => EnumNames::Products,
    ];

    public function args(): array
    {
        return [
            'search' => [
                'name' => 'search',
                'type' => Type::string(),
                'description' => 'search',
            ],
            'price_from' => [
                'name' => 'price_from',
                'type' => Type::int(),
                'description' => 'lower price boundary',
            ],
            'price_to' => [
                'name' => 'price_to',
                'type' => Type::int(),
                'description' => 'higher price boundary',
            ],
            'category_id' => [
                'name' => 'category_id',
                'type' => Type::int(),
                'description' => 'Filter by category ID',
                'rules' => ['nullable', 'exists:product_categories,id'],
            ],
            'union_tags' => [
                'name' => 'union_tags',
                'type' => Type::listOf(Type::int()),
                'description' => 'Filter by at least one of these tag IDs',
            ],
            'intersect_tags' => [
                'name' => 'intersect_tags',
                'type' => Type::listOf(Type::int()),
                'description' => 'Filter by all of these tag IDs',
            ],
            'type' => [
                'name' => 'type',
                'type' => Type::string(),
                'description' => 'type',
            ],
            ...$this->getHottestArgs(),
            ...$this->getNewestArgs(),
            ...$this->getTimeIntervalArgs(),
            ...$this->getSortArgs(),
            ...$this->getPaginationArgs(),

        ];
    }

    public function type(): Type
    {
        return GraphQL::paginate(EnumNames::Product);
    }

    /**
     * @param $root
     * @param $args
     * @return LengthAwarePaginator
     */
    public function resolve($root, $args): LengthAwarePaginator
    {
        $query = Product::query();

        if (isset($args['category_id'])) {
            $categoryIds = ProductCategory::query()->find($args['category_id'])->descendantsWithSelf()->pluck('id')->toArray();
            $query->whereIn('product_category_id', $categoryIds);
        }

        if (isset($args['type'])) {
            $query->where('type', $args['type']);
        }

        if (isset($args['is_hottest'])) {
            $query = $this->filterHottestQuery($query, $args['is_hottest']);
        }

        if (isset($args['is_newest'])) {
            $query = $this->filterNewestQuery($query, $args['is_newest']);
        }

        if (isset($args['search'])) {
            $search = $args['search'];
            $query->where(function ($query) use ($search) {
                return $query
                    ->whereHas('translations', function ($q) use ($search) {
                        $q->where('title', 'like', '%' . $search . '%');
                    })
                    ->orWhere('part_number', 'like', '%' . $search . '%');
            });
        }


        if (isset($args['price_from'])) {
            $query->whereHas('specifications', function ($subQuery) use ($args) {
                $subQuery->select('product_id')
                    ->groupBy('product_id')
                    ->havingRaw('MIN(listing_price) >= ?', [$args['price_from']]); // 直接使用聚合函數
            });
        }

        if (isset($args['price_to'])) {
            $query->whereHas('specifications', function ($subQuery) use ($args) {
                $subQuery->select('product_id')
                    ->groupBy('product_id')
                    ->havingRaw('MIN(listing_price) <= ?', [$args['price_to']]);
            });
        }

        if (isset($args['union_tags']) && is_array($args['union_tags'])) {
            $query->whereHas('tags', function ($q) use ($args) {
                $q->whereIn('tags.id', $args['union_tags']);
            });
        }

        if (isset($args['intersect_tags']) && is_array($args['intersect_tags'])) {
            $query->whereHas('tags', function ($q) use ($args) {
                $q->whereIn('tags.id', $args['intersect_tags']);
            }, '=', count($args['intersect_tags']));
        }


        $query = $this->filterCreatedTimeQuery($query, $args['started_at'] ?? null, $args['ended_at'] ?? null);

        $query = $this->filterTimeIntervalQuery($query);

        $query = $this->filterEnabledQuery($query,'category',true);

        $query = $this->translateQuery($query, [
            'translations', 'category.translations', 'tags.translations', 'attributes.translations',
            'attributes.items.translations',
        ]);

        $query = $this->sortQuery($query, $args['sort_column'], $args['sort_by']);

        return $this->paginateQuery($query, $args['per_page'], $args['page']);

    }

}
