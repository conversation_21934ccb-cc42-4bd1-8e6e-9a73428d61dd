<?php

// app/graphql/queries/quest/QuestQuery

namespace Stephenchenorg\BaseFilamentPlugin\GraphQL\Queries\Product;

use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasHottest;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasNewest;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasPagination;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasSort;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasStatus;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasTranslation;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductCategory;
use GraphQL\Type\Definition\Type;
use Illuminate\Database\Eloquent\Collection;
use Rebing\GraphQL\Support\Facades\GraphQL;
use Rebing\GraphQL\Support\Query;

class ProductCategoriesQuery extends Query
{
    use HasPagination, HasSort, HasTranslation, HasStatus, HasHottest,HasNewest;

    protected $attributes = [
        'name' => EnumNames::ProductCategories,
    ];


    public function args(): array
    {
        return [
            'type' => [
                'name' => 'type',
                'type' => Type::string(),
                'description' => 'type',
            ],
            'root_only' => [
                'name' => 'root_only',
                'type' => Type::boolean(),
                'defaultValue' => true,
            ],
            ...$this->getHottestArgs(),
            ...$this->getNewestArgs(),
            ...$this->getSortArgs(),
        ];
    }


    public function type(): Type
    {
        return Type::listOf(GraphQL::type(EnumNames::ProductCategory));
    }

    /**
     * @param $root
     * @param $args
     * @return Collection
     */
    public function resolve($root, $args): Collection
    {
        $query = ProductCategory::query();

        $query = $this->filterEnabledQuery($query);

        if (isset($args['type'])) {
            $query->where('type', '=', $args['type']);
        }

        if (isset($args['root_only']) && $args['root_only']) {
            $query->where('parent_id', '=', -1);
        }

        if (isset($args['is_hottest'])) {
            $query = $this->filterHottestQuery($query,$args['is_hottest']);
        }

        if (isset($args['is_newest'])) {
            $query = $this->filterNewestQuery($query,$args['is_newest']);
        }

        $query = $this->translateQuery($query, ['translations', 'products.translations']);

        $query = $this->sortQuery($query, $args['sort_column'], $args['sort_by']);

        return $query->get();

    }


}
