<?php

// app/graphql/queries/quest/QuestQuery

namespace Stephenchenorg\BaseFilamentPlugin\GraphQL\Queries\Product;

use Matrix\Exception;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasIndex;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasStatus;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasTranslation;
use Stephenchenorg\BaseFilamentPlugin\Models\Faq;
use Stephenchenorg\BaseFilamentPlugin\Models\Product;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductSpecification;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceLanguage;
use GraphQL\Type\Definition\Type;
use Rebing\GraphQL\Support\Facades\GraphQL;
use Rebing\GraphQL\Support\Query;

class ProductSpecificationQuery extends Query
{
    use HasTranslation , HasIndex,HasStatus;

    protected $attributes = [
        'name' => EnumNames::ProductSpecification,
    ];

    public function type(): Type
    {
        return GraphQL::type(EnumNames::ProductSpecification);
    }

    public function args(): array
    {
        return [
            ...$this->getIndexArgs(['id','sku']),
        ];
    }

    /**
     * @throws Exception
     */
    public function resolve($root, $args)
    {
        $query = ProductSpecification::query();

        $query = $this->translateQuery($query,['translations', 'product.translations']);

        $query = $this->applyFieldFilter($query, $args, ['id','sku']);

        $query = $this->filterEnabledQuery($query);

        return $query->firstOrFail();
    }
}
