<?php

// app/graphql/queries/quest/QuestQuery

namespace Stephenchenorg\BaseFilamentPlugin\GraphQL\Queries\Product;

use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasPagination;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasSort;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasStatus;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasTranslation;
use Stephenchenorg\BaseFilamentPlugin\Models\Faq;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductSpecification;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductCategory;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceLanguage;
use GraphQL\Type\Definition\Type;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Rebing\GraphQL\Support\Facades\GraphQL;
use Rebing\GraphQL\Support\Query;

class ProductSpecificationsQuery extends Query
{
    use HasPagination, HasSort, HasTranslation, HasStatus;

    protected $attributes = [
        'name' => EnumNames::ProductSpecifications,
    ];

    public function args(): array
    {
        return [

            ...$this->getSortArgs(),
            ...$this->getPaginationArgs(),
            'product_id' => [
                'name'        => 'product_id',
                'type'        => Type::int(),
                'description' => 'Filter by product ID',
            ],

        ];
    }

    public function type(): Type
    {
        return GraphQL::paginate(EnumNames::ProductSpecification);
    }

    /**
     * @param $root
     * @param $args
     * @return LengthAwarePaginator
     */
    public function resolve($root, $args): LengthAwarePaginator
    {
        $query = ProductSpecification::query();

        if (isset($args['product_id'])) {
            $query->where('product_id', $args['product_id']);
        }

        $query = $this->filterEnabledQuery($query);

        $query = $this->translateQuery($query, ['translations', 'product.translations']);

        $query = $this->sortQuery($query, $args['sort_column'], $args['sort_by']);

        return $this->paginateQuery($query, $args['per_page'], $args['page']);

    }

}
