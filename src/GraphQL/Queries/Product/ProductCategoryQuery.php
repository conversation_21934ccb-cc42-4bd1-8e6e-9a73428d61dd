<?php

// app/graphql/queries/quest/QuestQuery

namespace Stephenchenorg\BaseFilamentPlugin\GraphQL\Queries\Product;

use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasIndex;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasStatus;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasTranslation;
use Stephenchenorg\BaseFilamentPlugin\Models\Faq;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductCategory;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceLanguage;
use GraphQL\Type\Definition\Type;
use Matrix\Exception;
use Rebing\GraphQL\Support\Facades\GraphQL;
use Rebing\GraphQL\Support\Query;

class ProductCategoryQuery extends Query
{
    use HasTranslation, HasIndex,HasStatus;

    protected $attributes = [
        'name' => EnumNames::ProductCategory,
    ];

    public function type(): Type
    {
        return GraphQL::type(EnumNames::ProductCategory);
    }

    public function args(): array
    {
        return [
            ...$this->getIndexArgs(['id']),
        ];
    }

    /**
     * @throws Exception
     */
    public function resolve($root, $args)
    {
        $query = ProductCategory::query();

        $query = $this->applyFieldFilter($query, $args, ['id']);

        $query = $this->translateQuery($query, ['translations', 'products.translations']);

        $query = $this->filterEnabledQuery($query);

        return $query->firstOrFail();
    }
}
