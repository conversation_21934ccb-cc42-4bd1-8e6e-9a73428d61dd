<?php

// app/graphql/queries/quest/QuestQuery

namespace Stephenchenorg\BaseFilamentPlugin\GraphQL\Queries\Product;

use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasIndex;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasStatus;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasTranslation;
use Stephenchenorg\BaseFilamentPlugin\Models\Faq;
use Stephenchenorg\BaseFilamentPlugin\Models\Product;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceLanguage;
use GraphQL\Type\Definition\Type;
use Matrix\Exception;
use Rebing\GraphQL\Support\Facades\GraphQL;
use Rebing\GraphQL\Support\Query;

class ProductQuery extends Query
{
    use HasTranslation, HasIndex,HasStatus;

    protected $attributes = [
        'name' => EnumNames::Product,
    ];

    public function type(): Type
    {
        return GraphQL::type(EnumNames::Product);
    }

    public function args(): array
    {
        return [
            ...$this->getIndexArgs(['id','part_number']),
        ];
    }

    /**
     * @throws Exception
     */
    public function resolve($root, $args)
    {
        $query = Product::query();

        $query = $this->translateQuery($query, [
            'translations', 'category.translations', 'tags.translations', 'attributes.translations',
            'attributes.items.translations',
        ]);

        $query = $this->applyFieldFilter($query, $args, ['id','part_number']);

        $query = $this->filterEnabledQuery($query,'category',true);

        return $query->firstOrFail();
    }
}
