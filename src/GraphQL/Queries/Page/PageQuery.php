<?php

// app/graphql/queries/quest/QuestQuery

namespace Stephen<PERSON>org\BaseFilamentPlugin\GraphQL\Queries\Page;

use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasTranslation;
use Stephenchenorg\BaseFilamentPlugin\Models\Faq;
use Stephenchenorg\BaseFilamentPlugin\Models\Page;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceLanguage;
use Exception;
use GraphQL\Type\Definition\Type;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use Rebing\GraphQL\Support\Facades\GraphQL;
use Rebing\GraphQL\Support\Query;

class PageQuery extends Query
{
    use HasTranslation;

    protected $attributes = [
        'name' => EnumNames::Page,
    ];

    public function type(): Type
    {
        return GraphQL::type(EnumNames::Page);
    }

    public function args(): array
    {
        return [
            'id'  => [
                'name'  => 'id',
                'type'  => Type::int(),
                'rules' => ['nullable'], // id 是可選的
            ],
            'key' => [
                'name'  => 'key',
                'type'  => Type::string(),
                'rules' => ['nullable'], // key 是可選的
            ],
        ];
    }

    public function resolve($root, $args)
    {
        $query = Page::query();

        $query = $this->translateQuery($query, ['translations', 'fields.translations']);

        // 如果傳入 id，優先用 id 查詢
        if (isset($args['id'])) {
            return $query->findOrFail($args['id']);
        }

        // 如果傳入 key，用 key 查詢
        if (isset($args['key'])) {
            return $query->where('key', $args['key'])->first();
        }

        // 如果兩者都沒傳入，拋出異常
        throw new Exception('Please provide either id or key.');
    }
}
