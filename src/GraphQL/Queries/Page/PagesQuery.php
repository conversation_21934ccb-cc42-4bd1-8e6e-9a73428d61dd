<?php

// app/graphql/queries/quest/QuestQuery

namespace Stephenchenorg\BaseFilamentPlugin\GraphQL\Queries\Page;

use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasPagination;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasSort;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasStatus;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasTranslation;
use Stephenchenorg\BaseFilamentPlugin\Models\Article;
use Stephenchenorg\BaseFilamentPlugin\Models\Faq;
use Stephenchenorg\BaseFilamentPlugin\Models\Page;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceLanguage;
use Exception;
use GraphQL\Type\Definition\Type;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;
use Rebing\GraphQL\Support\Facades\GraphQL;
use Rebing\GraphQL\Support\Query;

class PagesQuery extends Query
{
    use HasPagination, HasTranslation;

    protected $attributes = [
        'name' => EnumNames::Pages,
    ];

    public function args(): array
    {
        return [
            ...$this->getPaginationArgs(),
        ];
    }


    public function type(): Type
    {
        return GraphQL::paginate(EnumNames::Page);
    }

    /**
     * @param $root
     * @param $args
     * @return LengthAwarePaginator
     */
    public function resolve($root, $args): LengthAwarePaginator
    {
        $query = Page::query();

        $query = $this->translateQuery($query,['translations', 'fields.translations']);

        return $this->paginateQuery($query, $args['per_page'], $args['page']);
    }


}
