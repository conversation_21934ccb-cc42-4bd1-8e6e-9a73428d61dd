<?php

// app/graphql/queries/quest/QuestsQuery

namespace Stephenchenorg\BaseFilamentPlugin\GraphQL\Queries\Article;

use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasPagination;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasSort;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasStatus;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasTranslation;
use Stephenchenorg\BaseFilamentPlugin\Models\ArticleCategory;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceLanguage;
use GraphQL\Type\Definition\Type;
use Illuminate\Database\Eloquent\Collection;
use Rebing\GraphQL\Support\Facades\GraphQL;
use Rebing\GraphQL\Support\Query;

class ArticleCategoryQuery extends Query
{
    use HasTranslation,HasStatus;

    protected $attributes = [
        'name' => EnumNames::ArticleCategory,
    ];

    public function type(): Type
    {
        return GraphQL::type(EnumNames::ArticleCategory);
    }

    public function args(): array
    {
        return [
            'id' => [
                'name'  => 'id',
                'type'  => Type::int(),
                'rules' => ['required'],
            ],
        ];
    }

    public function resolve($root, $args)
    {
        $query = ArticleCategory::query();

        $query = $this->filterEnabledQuery($query);

        $query = $this->translateQuery($query, ['translations', 'articles.translations']);

        return $query->findOrFail($args['id']);
    }
}
