<?php

// app/graphql/queries/quest/QuestQuery

namespace Stephenchenorg\BaseFilamentPlugin\GraphQL\Queries\Article;

use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasIndex;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasPagination;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasSort;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasStatus;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasTranslation;
use Stephenchenorg\BaseFilamentPlugin\Models\Article;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceLanguage;
use GraphQL\Type\Definition\ResolveInfo;
use GraphQL\Type\Definition\Type;
use Matrix\Exception;
use Rebing\GraphQL\Support\Facades\GraphQL;
use Rebing\GraphQL\Support\Query;

class ArticleQuery extends Query
{
    use HasSort, HasTranslation, HasIndex,HasStatus;

    protected $attributes = [
        'name' => EnumNames::Article,
    ];

    public function type(): Type
    {
        return GraphQL::type(EnumNames::Article);
    }

    public function args(): array
    {
        return [
            ...$this->getIndexArgs(['id', 'slug', 'key']),
            ...$this->getSortArgs(),
        ];
    }

    /**
     * @throws Exception
     */
    public function resolve($root, $args)
    {
        $query = Article::query();

        $query = $this->filterEnabledQuery($query,'category');

        $query = $this->translateQuery($query, ['translations', 'category.translations', 'tags.translations']);

        $query = $this->applyFieldFilter($query, $args, ['id', 'key', 'slug']);

        $article = $query->firstOrFail();

        $sortColumn = $args['sort_column'];
        $sortBy = $args['sort_by'];

        $prev = $this->getPrev($article, $sortColumn, $sortBy);
        $prev = $this->translateQuery($prev, ['translations', 'category.translations', 'tags.translations']);

        $next = $this->getNext($article, $sortColumn, $sortBy);
        $next = $this->translateQuery($next,['translations', 'category.translations', 'tags.translations']);

        $article->prev = $prev->first();
        $article->next = $next->first();


        return $article;
    }
}
