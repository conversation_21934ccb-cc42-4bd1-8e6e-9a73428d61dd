<?php

// app/graphql/queries/quest/QuestsQuery

namespace Stephen<PERSON>org\BaseFilamentPlugin\GraphQL\Queries\Article;

use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasHottest;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasNewest;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasPagination;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasSort;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasStatus;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasTimeInterval;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasTranslation;
use Stephenchenorg\BaseFilamentPlugin\Models\Article;
use GraphQL\Type\Definition\Type;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Rebing\GraphQL\Support\Facades\GraphQL;
use Rebing\GraphQL\Support\Query;

class ArticlesQuery extends Query
{
    use HasPagination, HasSort, HasTranslation, HasStatus, HasTimeInterval, HasHottest,HasNewest;

    protected $attributes = [
        'name' => EnumNames::Article,
    ];

    public function args(): array
    {
        return [

            ...$this->getPaginationArgs(),
            ...$this->getSortArgs(),
            ...$this->getTimeIntervalArgs(),
            ...$this->getHottestArgs(),
            ...$this->getNewestArgs(),

            'search' => [
                'name'        => 'search',
                'type'        => Type::string(),
                'description' => 'search',
            ],

            'category_id'    => [
                'name'        => 'category_id',
                'type'        => Type::int(),
                'description' => 'Filter by category ID',
            ],
            'union_tags'     => [
                'name'        => 'union_tags',
                'type'        => Type::listOf(Type::int()),
                'description' => 'Filter by at least one of these tag IDs',
            ],
            'intersect_tags' => [
                'name'        => 'intersect_tags',
                'type'        => Type::listOf(Type::int()),
                'description' => 'Filter by all of these tag IDs',
            ],
            'started_at'     => [
                'name' => 'started_at',
                'type' => Type::string(),
            ],
            'ended_at'       => [
                'name' => 'ended_at',
                'type' => Type::string(),
            ],
        ];
    }


    public function type(): Type
    {
        return GraphQL::paginate(EnumNames::Article);
    }

    /**
     * @param $root
     * @param $args
     * @return LengthAwarePaginator
     */
    public function resolve($root, $args): LengthAwarePaginator
    {
        $query = Article::query();

        $query = $this->filterEnabledQuery($query,'category');

        if (isset($args['is_hottest'])) {
            $query = $this->filterHottestQuery($query,$args['is_hottest']);
        }

        if (isset($args['is_newest'])) {
            $query = $this->filterNewestQuery($query,$args['is_newest']);
        }

        if (isset($args['category_id'])) {
            $query->where('article_category_id', $args['category_id']);
        }

        if (isset($args['search'])) {
            $search = $args['search'];
            $query->whereHas('translations', function ($q) use ($search)
            {
                $q->where('title', 'like', '%'.$search.'%');
            });
        }

        $query = $this->filterCreatedTimeQuery($query, $args['started_at'] ?? null, $args['ended_at'] ?? null);

        $query = $this->filterTimeIntervalQuery($query);


        if (isset($args['union_tags']) && is_array($args['union_tags'])) {
            $query->whereHas('tags', function ($q) use ($args)
            {
                $q->whereIn('tags.id', $args['union_tags']);
            });
        }

        if (isset($args['intersect_tags']) && is_array($args['intersect_tags'])) {
            $query->whereHas('tags', function ($q) use ($args)
            {
                $q->whereIn('tags.id', $args['intersect_tags']);
            }, '=', count($args['intersect_tags']));
        }

        $query = $this->translateQuery($query);

        $query = $this->sortQuery($query, $args['sort_column'], $args['sort_by']);

        return $this->paginateQuery($query, $args['per_page'], $args['page']);
    }
}
