<?php

// app/graphql/queries/quest/QuestsQuery

namespace Stephenchenorg\BaseFilamentPlugin\GraphQL\Queries\Article;

use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasPagination;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasSort;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasStatus;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasTranslation;
use Stephenchenorg\BaseFilamentPlugin\Models\ArticleCategory;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceLanguage;
use GraphQL\Type\Definition\Type;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Rebing\GraphQL\Support\Facades\GraphQL;
use Rebing\GraphQL\Support\Query;

class ArticleCategoriesQuery extends Query
{
    use HasPagination, HasSort, HasTranslation, HasStatus;

    protected $attributes = [
        'name' => EnumNames::ArticleCategories,
    ];

    public function args(): array
    {
        return [

            ...$this->getPaginationArgs(),
            ...$this->getSortArgs(),

        ];
    }


    public function type(): Type
    {
        return GraphQL::paginate(EnumNames::ArticleCategory);
    }


    /**
     * @param $root
     * @param $args
     * @return LengthAwarePaginator
     */
    public function resolve($root, $args): LengthAwarePaginator
    {
        $query = ArticleCategory::query();

        $query = $this->filterEnabledQuery($query);

        $query = $this->translateQuery($query, ['translations', 'articles.translations']);

        $query = $this->sortQuery($query, $args['sort_column'], $args['sort_by']);

        return $this->paginateQuery($query, $args['per_page'], $args['page']);
    }
}
