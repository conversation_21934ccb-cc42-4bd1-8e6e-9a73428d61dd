<?php

// app/graphql/queries/quest/QuestsQuery

namespace Stephen<PERSON>org\BaseFilamentPlugin\GraphQL\Queries\Tag;

use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasIndex;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasTranslation;
use Stephenchenorg\BaseFilamentPlugin\Models\Tag;
use GraphQL\Type\Definition\Type;
use Matrix\Exception;
use Rebing\GraphQL\Support\Facades\GraphQL;
use Rebing\GraphQL\Support\Query;

class TagQuery extends Query
{
    use HasTranslation, HasIndex;

    protected $attributes = [
        'name' => EnumNames::Tag,
    ];

    public function type(): Type
    {
        return GraphQL::type(EnumNames::Tag);
    }

    public function args(): array
    {
        return [
            ...$this->getIndexArgs(['id', 'slug', 'key']),
            'id' => [
                'name'  => 'id',
                'type'  => Type::int(),
                'rules' => ['required'],
            ],
        ];
    }

    /**
     * @throws Exception
     */
    public function resolve($root, $args)
    {
        $query = Tag::query();

        $query = $this->translateQuery($query);

        $query = $this->applyFieldFilter($query, $args, ['id', 'key', 'slug']);

        return $query->firstOrFail();
    }
}
