<?php

// app/graphql/queries/quest/QuestsQuery

namespace Stephen<PERSON>org\BaseFilamentPlugin\GraphQL\Queries\Tag;

use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasPagination;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasSort;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasTranslation;
use Stephenchenorg\BaseFilamentPlugin\Models\Tag;
use GraphQL\Type\Definition\Type;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Rebing\GraphQL\Support\Facades\GraphQL;
use Rebing\GraphQL\Support\Query;

class TagsQuery extends Query
{
    use HasPagination, HasSort, HasTranslation;

    protected $attributes = [
        'name' => EnumNames::Tags,
    ];

    public function args(): array
    {
        return [
            'type' => [
                'name'  => 'type',
                'type'  => Type::string(),
                'rules' => ['nullable'],
            ],
            ...$this->getPaginationArgs(),
            ...$this->getSortArgs(),
        ];
    }

    public function type(): Type
    {
        return GraphQL::paginate(EnumNames::Tag);
    }

    /**
     * @param $root
     * @param $args
     * @return LengthAwarePaginator
     */
    public function resolve($root, $args): LengthAwarePaginator
    {
        $query = Tag::query();

        if (!empty($args['type'])) {
            $query = $query->where('type', '=', $args['type']);
        }

        $query = $this->translateQuery($query);

        $query = $this->sortQuery($query, $args['sort_column'], $args['sort_by']);

        return $this->paginateQuery($query, $args['per_page'], $args['page']);
    }
}
