<?php

namespace <PERSON>g\BaseFilamentPlugin\GraphQL\Queries\Client;

use <PERSON><PERSON>org\BaseFilamentPlugin\GraphQL\Base\BaseClientJwtQuery;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;
use <PERSON><PERSON>org\BaseFilamentPlugin\Service\Auth\ServiceClient;
use Exception;
use GraphQL\Type\Definition\Type;
use Rebing\GraphQL\Support\Facades\GraphQL;

class ClientMeQuery extends BaseClientJwtQuery
{
    protected $attributes = [
        'name' => EnumNames::ClientMe,
        'description' => 'Get current authenticated client information',
    ];

    /**
     * @var ServiceClient
     */
    private ServiceClient $service;

    /**
     * @param ServiceClient $service
     */
    public function __construct(ServiceClient $service)
    {
        $this->service = $service;
    }

    public function type(): Type
    {
        return GraphQL::type(EnumNames::ClientLogin);
    }

    public function args(): array
    {
        return [];
    }

    /**
     * @throws Exception
     */
    public function resolve($root, $args): array
    {
        return $this->service->me();
    }
}
