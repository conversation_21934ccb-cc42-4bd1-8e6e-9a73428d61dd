<?php

namespace Stephenchenorg\BaseFilamentPlugin\GraphQL\Base;

use Stephenchenorg\BaseFilamentPlugin\GraphQL\Middleware\ResolveClientJwt;
use Rebing\GraphQL\Support\Mutation;

abstract class BaseClientJwtMutation extends Mutation
{
    /**
     * Middleware that will be applied to the mutation.
     * All client mutations extending this class will have JWT authentication.
     */
    protected $middleware = [
        ResolveClientJwt::class,
    ];
}
