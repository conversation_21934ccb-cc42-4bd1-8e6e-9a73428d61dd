<?php

namespace Stephenchenorg\BaseFilamentPlugin\GraphQL\Base;

use Stephenchenorg\BaseFilamentPlugin\GraphQL\Middleware\ResolveCustomerJwt;
use Rebing\GraphQL\Support\Query;

abstract class BaseCustomerJwtQuery extends Query
{
    /**
     * Middleware that will be applied to the query.
     * All customer queries extending this class will have JWT authentication.
     */
    protected $middleware = [
        ResolveCustomerJwt::class,
    ];
}
