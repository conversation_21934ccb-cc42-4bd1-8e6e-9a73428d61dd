<?php

namespace Stephenchenorg\BaseFilamentPlugin\GraphQL\Base;

use Stephenchenorg\BaseFilamentPlugin\GraphQL\Middleware\ResolveCustomerJwt;
use Rebing\GraphQL\Support\Mutation;

abstract class BaseCustomerJwtMutation extends Mutation
{
    /**
     * Middleware that will be applied to the mutation.
     * All customer mutations extending this class will have JWT authentication.
     */
    protected $middleware = [
        ResolveCustomerJwt::class,
    ];
}
