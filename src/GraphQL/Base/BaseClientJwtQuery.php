<?php

namespace Stephenchenorg\BaseFilamentPlugin\GraphQL\Base;

use Stephenchenorg\BaseFilamentPlugin\GraphQL\Middleware\ResolveClientJwt;
use Rebing\GraphQL\Support\Query;

abstract class BaseClientJwtQuery extends Query
{
    /**
     * Middleware that will be applied to the query.
     * All client queries extending this class will have JWT authentication.
     */
    protected $middleware = [
        ResolveClientJwt::class,
    ];
}
