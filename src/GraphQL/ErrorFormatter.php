<?php

namespace Stephenchenorg\BaseFilamentPlugin\GraphQL;

use GraphQL\Error\DebugFlag;
use GraphQL\Error\Error;
use GraphQL\Error\FormattedError;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Facades\Config;
use Illuminate\Validation\ValidationException;
use Rebing\GraphQL\Error\ProvidesErrorCategory;
use Rebing\GraphQL\Error\ValidationError;
use Tymon\JWTAuth\Exceptions\JWTException;

class ErrorFormatter
{
    public static function formatError(Error $e): array
    {
        $debug = Config::get('app.debug') ? (DebugFlag::INCLUDE_DEBUG_MESSAGE | DebugFlag::INCLUDE_TRACE) : DebugFlag::NONE;
        $formatter = FormattedError::prepareFormatter(null, $debug);
        $previous = $e->getPrevious();

        $error = [];

        if ($previous) {

            if ($previous instanceof ModelNotFoundException) {
                $error['code'] = 404;
                $error['message'] = 'Resource not found';
            } else if ($previous instanceof ValidationException) {
                $error['code'] = 422;
                $error['message'] = 'validation';
                $error['data']['validation'] = $previous->validator->errors()->getMessages();
            } else if ($previous instanceof ValidationError) {
                $error['code'] = 422;
                $error['message'] = 'validation';
                $error['data']['validation'] = $previous->getValidatorMessages()->getMessages();
            } else if ($previous instanceof ProvidesErrorCategory) {
                $error['code'] = 500;
                $error['message'] = 'validation';
                $error['category'] = $previous->getCategory();
            }
            else if ($previous instanceof AuthenticationException || $previous instanceof JWTException) {
                $error['code'] = 401;
                $error['message'] = $previous->getMessage();
            }
        } elseif ($e instanceof ProvidesErrorCategory) {
            $error['code'] = 500;
            $error['message'] = $e->getMessage();
            $error['category'] = $previous->getCategory();
        }


        if (empty($error)) {
            $error = [
                'code' => 500,
                'message' => $previous?->getMessage() ?? $e?->getMessage(),
            ];
        }


        return $error;
    }
}
