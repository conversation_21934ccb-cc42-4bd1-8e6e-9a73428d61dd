<?php

namespace Stephenchenorg\BaseFilamentPlugin\GraphQL\Types\Tag;

use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasTranslation;
use Stephenchenorg\BaseFilamentPlugin\Models\TagTranslation;
use GraphQL\Type\Definition\Type;
use Rebing\GraphQL\Support\Type as GraphQLType;

class TagTranslationType extends GraphQLType
{
    use HasTranslation;

    protected $attributes = [
        'name'        => 'TagTranslation',
        'description' => 'A type that represents an article translation',
        'model'       => TagTranslation::class,
    ];

    public function fields(): array
    {
        return [
            'title' => [
                'type'        => Type::string(),
                'description' => 'The name of the exhibition',
            ],
            'lang'  => [
                'type'        => Type::string(),
                'description' => 'The language code of the translation',
            ],
        ];

    }
}
