<?php

namespace Stephen<PERSON>org\BaseFilamentPlugin\GraphQL\Types\Tag;

use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;
use <PERSON><PERSON>org\BaseFilamentPlugin\GraphQL\Traits\HasPagination;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasSort;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasTranslation;
use Stephenchenorg\BaseFilamentPlugin\Models\Tag;
use GraphQL\Type\Definition\Type;
use Rebing\GraphQL\Support\Facades\GraphQL;
use Rebing\GraphQL\Support\Type as GraphQLType;

class TagType extends GraphQLType
{
    use HasSort, HasPagination, HasTranslation;

    protected $attributes = [
        'name'        => EnumNames::Tag,
        'description' => 'Collection of articles with their respective category',
        'model'       => Tag::class,
    ];

    public function fields(): array
    {
        $fields = [
            'id'            => [
                'type'        => Type::nonNull(Type::int()),
                'description' => 'The ID of the tag',
            ],
            'key'           => [
                'type'        => Type::string(),
                'description' => 'Unique key of the tag',
            ],
            'article_count' => [
                'type'        => Type::int(),
                'description' => 'Total count of articles associated with this tag',
            ],
            'product_count' => [
                'type'        => Type::int(),
                'description' => 'Total count of products associated with this tag',
            ],
            'slug'          => [
                'type'        => Type::string(),
                'description' => 'The slug of the tag',
            ],
            'created_at'    => [
                'type'        => Type::string(),
                'description' => 'Timestamp when the tag was created',
            ],
            'updated_at'    => [
                'type'        => Type::string(),
                'description' => 'Timestamp when the tag was last updated',
            ],
            'articles'      => [
                'description' => 'The translations of the article category',
                'type'        => GraphQL::paginate(EnumNames::Article),
                'args'        => [
                    ...$this->getSortArgs(),
                    ...$this->getPaginationArgs(),
                ],
                'resolve'     => function ($root, $args)
                {
                    $query = $root->articles();
                    $query = $this->sortQuery($query, $args['sort_column'], $args['sort_by']);
                    return $this->paginateQuery($query, $args['per_page'], $args['page']);
                },
            ],

            'products' => [
                'description' => 'The translations of the product category',
                'type'        => GraphQL::paginate(EnumNames::Product),
                'args'        => [
                    ...$this->getSortArgs(),
                    ...$this->getPaginationArgs(),
                ],
                'resolve'     => function ($root, $args)
                {
                    $query = $root->products();
                    $query = $this->sortQuery($query, $args['sort_column'], $args['sort_by']);
                    return $this->paginateQuery($query, $args['per_page'], $args['page']);
                },
            ],

        ];

        return array_merge($this->getTranslatableFields(new TagTranslationType()), $fields);
    }
}
