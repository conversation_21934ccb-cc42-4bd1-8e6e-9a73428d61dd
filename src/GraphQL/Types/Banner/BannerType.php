<?php

namespace <PERSON>org\BaseFilamentPlugin\GraphQL\Types\Banner;

use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;
use <PERSON><PERSON>org\BaseFilamentPlugin\GraphQL\Traits\HasImage;
use Stephen<PERSON>org\BaseFilamentPlugin\Models\Banner;
use GraphQL\Type\Definition\Type;
use Illuminate\Support\Facades\Storage;
use Rebing\GraphQL\Support\Type as GraphQLType;

class BannerType extends GraphQLType
{
    use HasImage;

    protected $attributes = [
        'name'        => EnumNames::Banner,
        'description' => 'A type that represents a banner',
        'model'       => Banner::class,
    ];

    public function fields(): array
    {
        return [
            'id'         => [
                'type'        => Type::nonNull(Type::int()),
                'description' => 'The ID of the banner',
            ],
            'cta_title'  => [
                'type'        => Type::string(),
                'description' => 'The CTA (Call to Action) title',
            ],
            'cta_link'   => [
                'type'        => Type::string(),
                'description' => 'The CTA (Call to Action) link',
            ],
            'title'      => [
                'type'        => Type::string(),
                'description' => 'The title of the banner',
            ],
            'status'     => [
                'type'        => Type::int(),
                'description' => 'The status of the banner (0: closed, 1: open)',
            ],
            'sort'       => [
                'type'        => Type::int(),
                'description' => 'The sort order of the banner',
            ],
            ...$this->getImageArgs(),
            'created_at' => [
                'type'        => Type::string(),
                'description' => 'Timestamp when the banner was created',
            ],
            'updated_at' => [
                'type'        => Type::string(),
                'description' => 'Timestamp when the banner was last updated',
            ],
        ];
    }
}
