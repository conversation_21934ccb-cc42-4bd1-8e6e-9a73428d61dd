<?php

namespace <PERSON>org\BaseFilamentPlugin\GraphQL\Types\Brand;

use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;
use <PERSON><PERSON>org\BaseFilamentPlugin\GraphQL\Traits\HasTranslation;
use Stephen<PERSON>org\BaseFilamentPlugin\Models\Brand;
use Stephenchenorg\BaseFilamentPlugin\Models\Faq;
use GraphQL\Type\Definition\Type;
use Illuminate\Support\Facades\Storage;
use Rebing\GraphQL\Support\Type as GraphQLType;

class BrandType extends GraphQLType
{
    use HasTranslation;

    protected $attributes = [
        'name'        => EnumNames::Brand,
        'description' => 'A type that represents a FAQ',
        'model'       => Brand::class,
    ];

    public function fields(): array
    {
        $fields = [
            'id'   => [
                'type'        => Type::nonNull(Type::int()),
                'description' => 'The ID of the Brand',
            ],
            'key'  => [
                'type'        => Type::string(),
                'description' => 'Unique key of the Brand',
            ],
            'sort' => [
                'type'        => Type::int(),
                'description' => 'Sort order of the Brand',
            ],
            'logo' => [
                'type'        => Type::string(),
                'description' => 'logo of the Brand',
                'resolve'     => function ($root, $args)
                {
                    if (empty($root->logo)) {
                        return null;
                    }
                    return Storage::disk(config('filament.default_filesystem_disk'))->url($root->logo);
                },
            ],
        ];

        return array_merge($this->getTranslatableFields(new BrandTranslationType()), $fields);

    }
}
