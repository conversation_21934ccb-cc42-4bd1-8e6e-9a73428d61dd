<?php

namespace <PERSON>org\BaseFilamentPlugin\GraphQL\Types\Brand;

use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;
use <PERSON><PERSON>org\BaseFilamentPlugin\GraphQL\Traits\HasSeo;
use Stephen<PERSON>org\BaseFilamentPlugin\Models\BrandTranslation;
use Stephen<PERSON>org\BaseFilamentPlugin\Models\FaqTranslation;
use GraphQL\Type\Definition\Type;
use Rebing\GraphQL\Support\Type as GraphQLType;

class BrandTranslationType extends GraphQLType
{
    use HasSeo;

    protected $attributes = [
        'name'        => EnumNames::BrandTranslation,
        'description' => 'A type that represents a FAQ translation',
        'model'       => BrandTranslation::class,
    ];

    public function fields(): array
    {
        return [
            'title'       => [
                'type'        => Type::string(),
                'description' => 'The title of the FAQ translation',
            ],
            'description' => [
                'type'        => Type::string(),
                'description' => 'The content of the FAQ translation',
            ],
            'lang'        => [
                'type'        => Type::string(),
                'description' => 'The language code of the translation',
                'resolve'     => function ($root)
                {
                    return $root->lang;
                },
            ],
        ];
    }
}
