<?php

namespace <PERSON>org\BaseFilamentPlugin\GraphQL\Types\Page;

use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;
use <PERSON><PERSON>org\BaseFilamentPlugin\GraphQL\Traits\HasTranslation;
use <PERSON><PERSON>org\BaseFilamentPlugin\Models\PageField;
use GraphQL\Type\Definition\Type;
use Rebing\GraphQL\Support\Type as GraphQLType;

class PageFieldType extends GraphQLType
{
    use HasTranslation;

    protected $attributes = [
        'name'        => EnumNames::PageField,
        'description' => 'A type that represents a page field',
        'model'       => PageField::class,
    ];

    public function fields(): array
    {
        $fields = [
            'id'         => [
                'type'        => Type::nonNull(Type::int()),
                'description' => 'The ID of the page field',
            ],
            'key'        => [
                'type'        => Type::string(),
                'description' => 'The title of the page field',
            ],
            'type'       => [
                'type'        => Type::string(),
                'description' => 'The type of the page field',
                'resolve'     => function ($root, $args)
                {
                    return $root->type->value;
                },
            ],
            'created_at' => [
                'type'        => Type::string(),
                'description' => 'Timestamp when the page field was created',
            ],
            'updated_at' => [
                'type'        => Type::string(),
                'description' => 'Timestamp when the page field was last updated',
            ],
        ];

        return array_merge($this->getTranslatableFields(new PageFieldTranslationType()), $fields);

    }
}
