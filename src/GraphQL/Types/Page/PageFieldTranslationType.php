<?php

namespace <PERSON>org\BaseFilamentPlugin\GraphQL\Types\Page;

use Stephenchenorg\BaseFilamentPlugin\Enum\EnumFieldType;
use <PERSON><PERSON>org\BaseFilamentPlugin\GraphQL\EnumNames;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasImage;
use Stephen<PERSON>org\BaseFilamentPlugin\Models\PageFieldTranslation;
use GraphQL\Type\Definition\Type;
use Illuminate\Support\Facades\Storage;
use Rebing\GraphQL\Support\Type as GraphQLType;

class PageFieldTranslationType extends GraphQLType
{
    use HasImage;

    protected $attributes = [
        'name'        => EnumNames::PageFieldTranslation,
        'description' => 'A type that represents a page field translation',
        'model'       => PageFieldTranslation::class,
    ];

    public function fields(): array
    {
        return [
            'id'         => [
                'type'        => Type::nonNull(Type::int()),
                'description' => 'The ID of the page field translation',
            ],
            'content'    => [
                'type'        => Type::string(),
                'description' => 'The content of the translation',
                'resolve'     => function ($root, $args)
                {
                    $result = $root->content;
                    if (empty($result)) {
                        return null;
                    }
                    if ($root->field->type === EnumFieldType::IMAGE) {
                        return null;
                    }
                    if ($root->field->type === EnumFieldType::TEXTAREA) {
                        $result = e($root->content);
                        return nl2br($result, false);
                    }
                    return $result;
                },
            ],
            ...$this->getImageArgs(),
            'lang'       => [
                'type'        => Type::string(),
                'description' => 'The language code of the translation',
            ],
            'created_at' => [
                'type'        => Type::string(),
                'description' => 'Timestamp when the translation was created',
            ],
            'updated_at' => [
                'type'        => Type::string(),
                'description' => 'Timestamp when the translation was last updated',
            ],
        ];
    }
}
