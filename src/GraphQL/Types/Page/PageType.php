<?php

namespace <PERSON>org\BaseFilamentPlugin\GraphQL\Types\Page;

use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;
use <PERSON><PERSON>org\BaseFilamentPlugin\GraphQL\Traits\HasPagination;
use <PERSON><PERSON>org\BaseFilamentPlugin\GraphQL\Traits\HasSort;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasTranslation;
use Stephenchenorg\BaseFilamentPlugin\Models\Page;
use GraphQL\Type\Definition\Type;
use Rebing\GraphQL\Support\Facades\GraphQL;
use Rebing\GraphQL\Support\Type as GraphQLType;

class PageType extends GraphQLType
{
    use HasTranslation, HasSort, HasPagination;

    protected $attributes = [
        'name'        => EnumNames::Page,
        'description' => 'A type that represents a page',
        'model'       => Page::class,
    ];

    public function fields(): array
    {
        $fields = [
            'id'         => [
                'type'        => Type::nonNull(Type::int()),
                'description' => 'The ID of the page',
            ],
            'key'        => [
                'type'        => Type::string(),
                'description' => 'Unique key of the page',
            ],
            'created_at' => [
                'type'        => Type::string(),
                'description' => 'Timestamp when the page was created',
            ],
            'updated_at' => [
                'type'        => Type::string(),
                'description' => 'Timestamp when the page was last updated',
            ],
            'fields'     => [
                'description' => 'Fields associated with the page',
                'type'        => Type::listOf(GraphQL::type(EnumNames::PageField)),
            ],
        ];

        return array_merge($this->getTranslatableFields(new PageTranslationType()), $fields);

    }
}
