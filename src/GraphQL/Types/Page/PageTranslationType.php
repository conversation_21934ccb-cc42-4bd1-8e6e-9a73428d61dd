<?php

namespace <PERSON>org\BaseFilamentPlugin\GraphQL\Types\Page;

use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;
use <PERSON><PERSON>org\BaseFilamentPlugin\GraphQL\Traits\HasSeo;
use Stephen<PERSON>org\BaseFilamentPlugin\Models\PageTranslation;
use GraphQL\Type\Definition\Type;
use Rebing\GraphQL\Support\Type as GraphQLType;

class PageTranslationType extends GraphQLType
{
    use HasSeo;

    protected $attributes = [
        'name'        => EnumNames::PageTranslation,
        'description' => 'A type that represents a page translation',
        'model'       => PageTranslation::class,
    ];

    public function fields(): array
    {
        return [
            'id'    => [
                'type'        => Type::nonNull(Type::int()),
                'description' => 'The ID of the page field translation',
            ],
            'lang'  => [
                'type'        => Type::string(),
                'description' => 'The language code of the translation',
            ],
            'title' => [
                'type'        => Type::string(),
                'description' => 'The title of the translation',
            ],
            ...$this->getSeoArgs(),
        ];
    }
}
