<?php

namespace <PERSON>g\BaseFilamentPlugin\GraphQL\Types\Auth;

use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;
use <PERSON><PERSON>org\BaseFilamentPlugin\GraphQL\Traits\HasImage;
use <PERSON>chenorg\BaseFilamentPlugin\GraphQL\Traits\HasPagination;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasSort;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasTranslation;
use Stephenchenorg\BaseFilamentPlugin\Models\Customer;
use GraphQL\Type\Definition\Type;
use Rebing\GraphQL\Support\Facades\GraphQL;
use Rebing\GraphQL\Support\Type as GraphQLType;

class CustomerType extends GraphQLType
{
    use HasSort, HasPagination;

    protected $attributes = [
        'name'        => EnumNames::Customer,
        'description' => 'A type that represents a customer',
        'model'       => Customer::class,
    ];

    public function fields(): array
    {
        return [
            'id' => [
                'type'        => Type::nonNull(Type::int()),
                'description' => 'The ID of the customer',
            ],
            'name' => [
                'type'        => Type::string(),
                'description' => 'The name of the customer',
            ],
            'email' => [
                'type'        => Type::string(),
                'description' => 'The email of the customer',
            ],
            'phone' => [
                'type'        => Type::string(),
                'description' => 'The phone number of the customer',
            ],
            'created_at' => [
                'type'        => Type::string(),
                'description' => 'The creation timestamp of the customer',
            ],
            'updated_at' => [
                'type'        => Type::string(),
                'description' => 'The last update timestamp of the customer',
            ],
        ];
    }
}