<?php

namespace <PERSON>org\BaseFilamentPlugin\GraphQL\Types\Auth;

use <PERSON>chenorg\BaseFilamentPlugin\GraphQL\EnumNames;
use <PERSON><PERSON>org\BaseFilamentPlugin\GraphQL\Traits\HasImage;
use <PERSON><PERSON>org\BaseFilamentPlugin\GraphQL\Traits\HasPagination;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasSort;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasTranslation;
use Stephenchenorg\BaseFilamentPlugin\Models\Client;
use GraphQL\Type\Definition\Type;
use Rebing\GraphQL\Support\Facades\GraphQL;
use Rebing\GraphQL\Support\Type as GraphQLType;

class ClientType extends GraphQLType
{
    use HasSort, HasPagination;

    protected $attributes = [
        'name'        => EnumNames::Client,
        'description' => 'A type that represents a client',
        'model'       => Client::class,
    ];

    public function fields(): array
    {
        return [
            'id' => [
                'type'        => Type::nonNull(Type::int()),
                'description' => 'The ID of the client',
            ],
            'name' => [
                'type'        => Type::string(),
                'description' => 'The name of the client',
            ],
            'email' => [
                'type'        => Type::string(),
                'description' => 'The email of the client',
            ],
            'code' => [
                'type'        => Type::string(),
                'description' => 'The code of the client',
            ],
            'invoice_title' => [
                'type'        => Type::string(),
                'description' => 'The invoice title of the client',
            ],
            'address' => [
                'type'        => Type::string(),
                'description' => 'The address of the client',
            ],
            'invoice_address' => [
                'type'        => Type::string(),
                'description' => 'The invoice address of the client',
            ],
            'phone1' => [
                'type'        => Type::string(),
                'description' => 'The primary phone number of the client',
            ],
            'phone2' => [
                'type'        => Type::string(),
                'description' => 'The secondary phone number of the client',
            ],
            'mobile' => [
                'type'        => Type::string(),
                'description' => 'The mobile number of the client',
            ],
            'contact_person' => [
                'type'        => Type::string(),
                'description' => 'The contact person of the client',
            ],
            'vat' => [
                'type'        => Type::string(),
                'description' => 'The tax ID of the client',
            ],
            'created_at' => [
                'type'        => Type::string(),
                'description' => 'The creation timestamp of the client',
            ],
            'updated_at' => [
                'type'        => Type::string(),
                'description' => 'The last update timestamp of the client',
            ],
        ];
    }
}
