<?php

namespace Stephenchenorg\BaseFilamentPlugin\GraphQL\Types\Auth;

use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;
use Rebing\GraphQL\Support\Facades\GraphQL;
use Rebing\GraphQL\Support\Type as GraphQLType;

class ClientLoginType extends GraphQLType
{

    protected $attributes = [
        'name' => EnumNames::ClientLogin,
        'description' => 'Client login response with token and client information',
    ];


    public function fields(): array
    {
        $fields = [
            'token' => [
                'type' => GraphQL::type(EnumNames::Token),
                'description' => 'The token',
            ],
            'client' => [
                'type' => GraphQL::type(EnumNames::Client),
                'description' => 'client',
            ],
        ];

        return $fields;
    }
}
