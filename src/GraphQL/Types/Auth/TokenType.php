<?php

namespace Stephenchenorg\BaseFilamentPlugin\GraphQL\Types\Auth;

use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;
use GraphQL\Type\Definition\Type;
use Rebing\GraphQL\Support\Type as GraphQLType;

class TokenType extends GraphQLType
{
    protected $attributes = [
        'name'        => EnumNames::Token,
        'description' => 'A type that represents JWT token information',
    ];

    public function fields(): array
    {
        return [
            'token' => [
                'type'        => Type::nonNull(Type::string()),
                'description' => 'The JWT token string',
            ],
            'token_type' => [
                'type'        => Type::string(),
                'description' => 'The token type (e.g. Bearer)',
            ],
            'expired_at' => [
                'type'        => Type::string(),
                'description' => 'The token expiration timestamp',
            ],
            'refresh_expired_at' => [
                'type'        => Type::string(),
                'description' => 'The refresh token expiration timestamp',
            ],
        ];
    }
}