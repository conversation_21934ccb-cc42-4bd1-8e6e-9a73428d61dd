<?php

namespace Stephenchenorg\BaseFilamentPlugin\GraphQL\Types\Auth;

use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;
use Rebing\GraphQL\Support\Facades\GraphQL;
use Rebing\GraphQL\Support\Type as GraphQLType;

class CustomerLoginType extends GraphQLType
{

    protected $attributes = [
        'name' => EnumNames::CustomerLogin,
        'description' => 'Collection of articles with their respective category',
    ];


    public function fields(): array
    {
        $fields = [
            'token' => [
                'type' => GraphQL::type(EnumNames::Token),
                'description' => 'The token',
            ],
            'customer' => [
                'type' => GraphQL::type(EnumNames::Customer),
                'description' => 'customer',
            ],
        ];

        return $fields;
    }
}
