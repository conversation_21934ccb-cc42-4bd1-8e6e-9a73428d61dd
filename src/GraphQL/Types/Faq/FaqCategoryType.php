<?php

namespace <PERSON>org\BaseFilamentPlugin\GraphQL\Types\Faq;

use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;
use <PERSON><PERSON>org\BaseFilamentPlugin\GraphQL\Traits\HasPagination;
use <PERSON><PERSON>org\BaseFilamentPlugin\GraphQL\Traits\HasSort;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasStatus;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasTranslation;
use Stephenchenorg\BaseFilamentPlugin\Models\FaqCategory;
use GraphQL\Type\Definition\Type;
use Rebing\GraphQL\Support\Facades\GraphQL;
use Rebing\GraphQL\Support\Type as GraphQLType;

class FaqCategoryType extends GraphQLType
{
    use HasTranslation, HasSort, HasPagination,HasStatus;

    protected $attributes = [
        'name'        => EnumNames::FaqCategory,
        'description' => 'Collection of article categories',
        'model'       => FaqCategory::class,
    ];

    public function fields(): array
    {
        $fields = [
            'id'          => [
                'type'        => Type::nonNull(Type::int()),
                'description' => 'The ID of the FAQ category',
            ],
            'key'         => [
                'type'        => Type::string(),
                'description' => 'The unique key of the FAQ category',
            ],
            'status'      => [
                'type'        => Type::int(),
                'description' => 'Status of the FAQ category (0: closed, 1: open)',
            ],
            'sort'        => [
                'type'        => Type::int(),
                'description' => 'Sort order of the FAQ category',
            ],
            'count_total' => [
                'type'        => Type::int(),
                'description' => 'Total count of FAQs under this category',
            ],
            'slug'        => [
                'type'        => Type::string(),
                'description' => 'Slug of the FAQ category',
            ],
            'created_at'  => [
                'type'        => Type::string(),
                'description' => 'Timestamp when the FAQ category was created',
            ],
            'updated_at'  => [
                'type'        => Type::string(),
                'description' => 'Timestamp when the FAQ category was last updated',
            ],
            'faqs'        => [
                'description' => 'The FAQs under this category',
                'type'        => GraphQL::paginate(EnumNames::Faq),
                'args'        => [
                    ...$this->getSortArgs(),
                    ...$this->getPaginationArgs(),
                ],
                'resolve'     => function ($root, $args)
                {
                    $query = $root->faqs();
                    $query = $this->filterEnabledQuery($query);
                    $query = $this->sortQuery($query, $args['sort_column'], $args['sort_by']);
                    return $this->paginateQuery($query, $args['per_page'], $args['page']);
                },
            ],
        ];

        return array_merge($this->getTranslatableFields(new FaqCategoryTranslationType()), $fields);
    }


}
