<?php

namespace Stephen<PERSON>org\BaseFilamentPlugin\GraphQL\Types\Faq;

use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;
use <PERSON><PERSON>org\BaseFilamentPlugin\GraphQL\Traits\HasSeo;
use <PERSON><PERSON>org\BaseFilamentPlugin\Models\FaqCategoryTranslation;
use GraphQL\Type\Definition\Type;
use Rebing\GraphQL\Support\Type as GraphQLType;

class FaqCategoryTranslationType extends GraphQLType
{
    use HasSeo;

    protected $attributes = [
        'name'        => EnumNames::FaqCategoryTranslation,
        'description' => 'A type that represents a FAQ category translation',
        'model'       => FaqCategoryTranslation::class,
    ];

    public function fields(): array
    {
        return [
            'id'         => [
                'type'        => Type::nonNull(Type::int()),
                'description' => 'The ID of the FAQ category translation',
            ],
            ...$this->getSeoArgs(),
            'title'      => [
                'type'        => Type::string(),
                'description' => 'The title of the FAQ category translation',
            ],
            'content'    => [
                'type'        => Type::string(),
                'description' => 'The content of the translation',
            ],
            'lang'       => [
                'type'        => Type::string(),
                'description' => 'The language code of the translation',
                'resolve'     => function ($root)
                {
                    return $root->lang;
                },
            ],
            'created_at' => [
                'type'        => Type::string(),
                'description' => 'Timestamp when the translation was created',
            ],
            'updated_at' => [
                'type'        => Type::string(),
                'description' => 'Timestamp when the translation was last updated',
            ],
        ];
    }
}
