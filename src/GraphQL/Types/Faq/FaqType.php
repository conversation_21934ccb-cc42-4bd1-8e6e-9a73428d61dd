<?php

namespace <PERSON>org\BaseFilamentPlugin\GraphQL\Types\Faq;

use <PERSON>chenorg\BaseFilamentPlugin\GraphQL\EnumNames;
use <PERSON><PERSON>org\BaseFilamentPlugin\GraphQL\Traits\HasTranslation;
use <PERSON><PERSON>org\BaseFilamentPlugin\Models\Faq;
use GraphQL\Type\Definition\Type;
use Rebing\GraphQL\Support\Facades\GraphQL;
use Rebing\GraphQL\Support\Type as GraphQLType;

class FaqType extends GraphQLType
{
    use HasTranslation;

    protected $attributes = [
        'name'        => EnumNames::Faq,
        'description' => 'A type that represents a FAQ',
        'model'       => Faq::class,
    ];

    public function fields(): array
    {
        $fields = [
            'id'         => [
                'type'        => Type::nonNull(Type::int()),
                'description' => 'The ID of the FAQ',
            ],
            'key'        => [
                'type'        => Type::string(),
                'description' => 'Unique key of the FAQ',
            ],
            'status'     => [
                'type'        => Type::int(),
                'description' => 'Status of the FAQ (0: closed, 1: open)',
            ],
            'sort'       => [
                'type'        => Type::int(),
                'description' => 'Sort order of the FAQ',
            ],
            'created_at' => [
                'type'        => Type::string(),
                'description' => 'Timestamp when the FAQ was created',
            ],
            'updated_at' => [
                'type'        => Type::string(),
                'description' => 'Timestamp when the FAQ was last updated',
            ],
            'category'   => [
                'type'        => GraphQL::type(EnumNames::FaqCategory),
                'description' => 'The category this FAQ belongs to',
            ],
        ];

        return array_merge($this->getTranslatableFields(new FaqTranslationType()), $fields);

    }
}
