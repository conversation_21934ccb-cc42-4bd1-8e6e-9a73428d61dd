<?php

namespace <PERSON>org\BaseFilamentPlugin\GraphQL\Types\Faq;

use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;
use <PERSON><PERSON>org\BaseFilamentPlugin\GraphQL\Traits\HasSeo;
use <PERSON><PERSON>org\BaseFilamentPlugin\Models\FaqTranslation;
use GraphQL\Type\Definition\Type;
use Rebing\GraphQL\Support\Type as GraphQLType;

class FaqTranslationType extends GraphQLType
{
    use HasSeo;

    protected $attributes = [
        'name'        => EnumNames::FaqTranslation,
        'description' => 'A type that represents a FAQ translation',
        'model'       => FaqTranslation::class,
    ];

    public function fields(): array
    {
        return [
            'id'         => [
                'type'        => Type::nonNull(Type::int()),
                'description' => 'The ID of the FAQ translation',
            ],
            ...$this->getSeoArgs(),
            'title'      => [
                'type'        => Type::string(),
                'description' => 'The title of the FAQ translation',
            ],
            'content'    => [
                'type'        => Type::string(),
                'description' => 'The content of the FAQ translation',
            ],
            'lang'       => [
                'type'        => Type::string(),
                'description' => 'The language code of the translation',
                'resolve'     => function ($root)
                {
                    return $root->lang;
                },
            ],
            'created_at' => [
                'type'        => Type::string(),
                'description' => 'Timestamp when the translation was created',
            ],
            'updated_at' => [
                'type'        => Type::string(),
                'description' => 'Timestamp when the translation was last updated',
            ],
        ];
    }
}
