<?php

namespace Stephen<PERSON>org\BaseFilamentPlugin\GraphQL\Types\Image;

use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasImage;
use GraphQL\Type\Definition\Type;
use Illuminate\Support\Facades\Storage;
use Rebing\GraphQL\Support\Type as GraphQLType;

class BackgroundType extends GraphQLType
{
    use HasImage;

    protected $attributes = [
        'name'        => 'Background',
        'description' => 'background',
    ];

    public function fields(): array
    {
        return [
            'desktop'      => [
                'type'        => Type::string(),
                'description' => 'desktop',
                'resolve'     => function ($root, $args)
                {
                    if (empty($root->background)) {
                        return null;
                    }
                    return Storage::disk(config('filament.default_filesystem_disk'))->url($root->background);
                },
            ],
            'desktop_blur' => [
                'type'        => Type::string(),
                'description' => 'desktop_blur',
                'resolve'     => function ($root, $args)
                {
                    if (!empty($root->background_blur)) {
                        return Storage::disk(config('filament.default_filesystem_disk'))->url($root->background_blur);
                    }
                    if (!empty($root->background)) {
                        return Storage::disk(config('filament.default_filesystem_disk'))->url($root->background);
                    }
                    return null;
                },
            ],
            'mobile'       => [
                'type'        => Type::string(),
                'description' => 'mobile',
                'resolve'     => function ($root, $args)
                {
                    if (!empty($root->background_mobile)) {
                        return Storage::disk(config('filament.default_filesystem_disk'))->url($root->background_mobile);
                    }
                    if (!empty($root->background)) {
                        return Storage::disk(config('filament.default_filesystem_disk'))->url($root->background);
                    }
                    return null;
                },
            ],
            'mobile_blur'  => [
                'type'        => Type::string(),
                'description' => 'mobile_blur',
                'resolve'     => function ($root, $args)
                {
                    if (!empty($root->background_mobile_blur)) {
                        return Storage::disk(config('filament.default_filesystem_disk'))->url($root->background_mobile_blur);
                    }
                    if (!empty($root->background)) {
                        return Storage::disk(config('filament.default_filesystem_disk'))->url($root->background);
                    }
                    return null;
                },
            ],
        ];
    }
}
