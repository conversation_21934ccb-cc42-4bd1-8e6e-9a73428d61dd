<?php

namespace Stephen<PERSON>org\BaseFilamentPlugin\GraphQL\Types\Image;

use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasImage;
use GraphQL\Type\Definition\Type;
use Illuminate\Support\Facades\Storage;
use Rebing\GraphQL\Support\Type as GraphQLType;

class CoverType extends GraphQLType
{
    use HasImage;

    protected $attributes = [
        'name'        => 'Cover',
        'description' => 'cover',
    ];

    public function fields(): array
    {
        return [
            'desktop'      => [
                'type'        => Type::string(),
                'description' => 'desktop',
                'resolve'     => function ($root, $args)
                {
                    if (empty($root->cover)) {
                        return null;
                    }
                    return Storage::disk(config('filament.default_filesystem_disk'))->url($root->cover);
                },
            ],
            'desktop_blur' => [
                'type'        => Type::string(),
                'description' => 'desktop_blur',
                'resolve'     => function ($root, $args)
                {
                    if (!empty($root->cover_blur)) {
                        return Storage::disk(config('filament.default_filesystem_disk'))->url($root->cover_blur);
                    }
                    if (!empty($root->cover)) {
                        return Storage::disk(config('filament.default_filesystem_disk'))->url($root->cover);
                    }
                    return null;
                },
            ],
            'mobile'       => [
                'type'        => Type::string(),
                'description' => 'mobile',
                'resolve'     => function ($root, $args)
                {
                    if (!empty($root->cover_mobile)) {
                        return Storage::disk(config('filament.default_filesystem_disk'))->url($root->cover_mobile);
                    }
                    if (!empty($root->cover)) {
                        return Storage::disk(config('filament.default_filesystem_disk'))->url($root->cover);
                    }
                    return null;
                },
            ],
            'mobile_blur'  => [
                'type'        => Type::string(),
                'description' => 'mobile_blur',
                'resolve'     => function ($root, $args)
                {
                    if (!empty($root->cover_mobile_blur)) {
                        return Storage::disk(config('filament.default_filesystem_disk'))->url($root->cover_mobile_blur);
                    }
                    if (!empty($root->cover)) {
                        return Storage::disk(config('filament.default_filesystem_disk'))->url($root->cover);
                    }
                    return null;
                },
            ],
        ];
    }
}
