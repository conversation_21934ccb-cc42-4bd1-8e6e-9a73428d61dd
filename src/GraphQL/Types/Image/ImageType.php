<?php

namespace Stephen<PERSON>org\BaseFilamentPlugin\GraphQL\Types\Image;

use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasImage;
use GraphQL\Type\Definition\Type;
use Illuminate\Support\Facades\Storage;
use Rebing\GraphQL\Support\Type as GraphQLType;

class ImageType extends GraphQLType
{
    use HasImage;

    protected $attributes = [
        'name'        => 'Image',
        'description' => 'image',
    ];

    public function fields(): array
    {
        return [
            'desktop'      => [
                'type'        => Type::string(),
                'description' => 'desktop',
                'resolve'     => function ($root, $args)
                {
                    if (empty($root->image)) {
                        return null;
                    }
                    return Storage::disk(config('filament.default_filesystem_disk'))->url($root->image);
                },
            ],
            'desktop_blur' => [
                'type'        => Type::string(),
                'description' => 'desktop_blur',
                'resolve'     => function ($root, $args)
                {
                    if (!empty($root->image_blur)) {
                        return Storage::disk(config('filament.default_filesystem_disk'))->url($root->image_blur);
                    }
                    if (!empty($root->image)) {
                        return Storage::disk(config('filament.default_filesystem_disk'))->url($root->image);
                    }
                    return null;
                },
            ],
            'mobile'       => [
                'type'        => Type::string(),
                'description' => 'mobile',
                'resolve'     => function ($root, $args)
                {
                    if (!empty($root->image_mobile)) {
                        return Storage::disk(config('filament.default_filesystem_disk'))->url($root->image_mobile);
                    }
                    if (!empty($root->image)) {
                        return Storage::disk(config('filament.default_filesystem_disk'))->url($root->image);
                    }
                    return null;
                },
            ],
            'mobile_blur'  => [
                'type'        => Type::string(),
                'description' => 'mobile_blur',
                'resolve'     => function ($root, $args)
                {
                    if (!empty($root->image_mobile_blur)) {
                        return Storage::disk(config('filament.default_filesystem_disk'))->url($root->image_mobile_blur);
                    }
                    if (!empty($root->image)) {
                        return Storage::disk(config('filament.default_filesystem_disk'))->url($root->image);
                    }
                    return null;
                },
            ],
        ];
    }
}
