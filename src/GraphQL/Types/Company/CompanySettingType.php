<?php

namespace Stephenchenorg\BaseFilamentPlugin\GraphQL\Types\Company;

use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;
use <PERSON><PERSON>org\BaseFilamentPlugin\GraphQL\Traits\HasContent;
use Stephenchenorg\BaseFilamentPlugin\Models\CompanySetting;
use GraphQL\Type\Definition\Type;
use Illuminate\Support\Facades\Storage;
use Rebing\GraphQL\Support\Type as GraphQLType;

class CompanySettingType extends GraphQLType
{
    use HasContent;

    protected $attributes = [
        'name'        => EnumNames::CompanySetting,
        'description' => 'A type that represents a CompanySetting',
        'model'       => CompanySetting::class,
    ];

    public function fields(): array
    {
        return [

            'lang'         => [
                'type'        => Type::nonNull(Type::string()),
                'description' => 'The language code of the CompanySetting',
            ],
            'name'         => [
                'type'        => Type::string(),
                'description' => 'The name of the CompanySetting',
            ],
            'logo'         => [
                'type'        => Type::string(),
                'description' => 'The logo of the CompanySetting',
                'resolve'     => function ($root, $args)
                {
                    return Storage::disk(config('filament.default_filesystem_disk'))->url($root->logo);
                },
            ],
            ...$this->getPlainTextArgs('description'),
            'vat'         => [
                'type'        => Type::string(),
                'description' => 'The vat of the CompanySetting',
            ],
            'address_1'    => [
                'type'        => Type::string(),
                'description' => 'The primary address of the CompanySetting',
            ],
            'address_2'    => [
                'type'        => Type::string(),
                'description' => 'The secondary address of the CompanySetting',
            ],
            'phone_1'      => [
                'type'        => Type::string(),
                'description' => 'The primary phone number of the CompanySetting',
            ],
            'phone_2'      => [
                'type'        => Type::string(),
                'description' => 'The secondary phone number of the CompanySetting',
            ],
            'email_1'      => [
                'type'        => Type::string(),
                'description' => 'The primary email address of the CompanySetting',
            ],
            'email_2'      => [
                'type'        => Type::string(),
                'description' => 'The secondary email address of the CompanySetting',
            ],
            'line_link'    => [
                'type'        => Type::string(),
                'description' => 'The Line link of the CompanySetting',
            ],
            'fb_link'      => [
                'type'        => Type::string(),
                'description' => 'The Facebook link of the CompanySetting',
            ],
            'ig_link'      => [
                'type'        => Type::string(),
                'description' => 'The Instagram link of the CompanySetting',
            ],
            'twitter_link' => [
                'type'        => Type::string(),
                'description' => 'The Twitter link of the CompanySetting',
            ],
            'threads_link' => [
                'type'        => Type::string(),
                'description' => 'The Twitter link of the CompanySetting',
            ],
            'tg_link'      => [
                'type'        => Type::string(),
                'description' => 'The Telegram link of the CompanySetting',
            ],
        ];

    }
}
