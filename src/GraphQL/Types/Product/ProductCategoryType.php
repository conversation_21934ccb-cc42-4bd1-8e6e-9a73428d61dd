<?php

namespace <PERSON>org\BaseFilamentPlugin\GraphQL\Types\Product;

use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;
use <PERSON><PERSON>org\BaseFilamentPlugin\GraphQL\Traits\HasImage;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasPagination;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasSort;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasStatus;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasTranslation;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductCategory;
use GraphQL\Type\Definition\Type;
use Illuminate\Support\Facades\Storage;
use Rebing\GraphQL\Support\Facades\GraphQL;
use Rebing\GraphQL\Support\Type as GraphQLType;

class ProductCategoryType extends GraphQLType
{
    use HasTranslation, HasImage, HasSort, HasPagination, HasStatus;

    protected $attributes = [
        'name'        => EnumNames::ProductCategory,
        'description' => 'A type that represents a product category',
        'model'       => ProductCategory::class,
    ];

    public function fields(): array
    {
        $fields = [
            'id'          => [
                'type'        => Type::nonNull(Type::int()),
                'description' => 'The ID of the product category',
            ],
            'parent_id'          => [
                'type'        => Type::nonNull(Type::int()),
                'description' => 'The Parent ID of the product category',
            ],
            'type'        => [
                'type'        => Type::string(),
                'description' => 'The type of the product',
            ],
            'children'    => [
                'type'        => Type::listOf(GraphQL::type(EnumNames::ProductCategory)),
                'description' => 'The children of the category',
                'resolve'     => function ($root)
                {
                    return $root->getChildren()->filter(function ($child) {
                        return $child->status === 1;
                    });
                },
            ],
            'status'      => [
                'type'        => Type::int(),
                'description' => 'The status of the product category (0 for closed, 1 for open)',
            ],
            'sort'        => [
                'type'        => Type::int(),
                'description' => 'The sort order of the product category',
            ],
            ...$this->getImageArgs(),
            'count_total' => [
                'type'        => Type::int(),
                'description' => 'The total count of products under this category',
            ],
            'is_hottest'          => [
                'type'        => Type::int(),
                'description' => 'Whether the product is the hottest (0: no, 1: yes)',
            ],
            'is_newest'           => [
                'type'        => Type::int(),
                'description' => 'Whether the product is the newest (0: no, 1: yes)',
            ],
            'created_at'  => [
                'type'        => Type::string(),
                'description' => 'The creation timestamp of the product category',
            ],
            'updated_at'  => [
                'type'        => Type::string(),
                'description' => 'The last update timestamp of the product category',
            ],
            'products'    => [
                'description' => 'The translations of the product category',
                'type'        => GraphQL::paginate(EnumNames::Product),
                'args'        => [
                    ...$this->getSortArgs(),
                    ...$this->getPaginationArgs(),
                ],
                'resolve'     => function ($root, $args)
                {
                    $query = $root->products();
                    $query = $this->filterEnabledQuery($query,'category','true');
                    $query = $this->sortQuery($query, $args['sort_column'], $args['sort_by']);
                    return $this->paginateQuery($query, $args['per_page'], $args['page']);
                },
            ],
        ];

        return array_merge($this->getTranslatableFields(new ProductCategoryTranslationType()), $fields);


    }
}
