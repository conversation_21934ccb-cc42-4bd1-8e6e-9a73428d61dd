<?php

namespace Stephenchenorg\BaseFilamentPlugin\GraphQL\Types\Product;

use Stephenchenorg\BaseFilamentPlugin\Models\ProductAttributeTranslation;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductSpecificationTranslation;
use GraphQL\Type\Definition\Type;
use Rebing\GraphQL\Support\Type as GraphQLType;

class ProductAttributeItemTranslationType extends GraphQLType
{
    protected $attributes = [
        'name'        => 'ProductAttributeTranslation',
        'description' => 'A type that represents a product specification translation',
        'model'       => ProductAttributeTranslation::class,
    ];

    public function fields(): array
    {
        return [
            'id'    => [
                'type'        => Type::nonNull(Type::int()),
                'description' => 'The ID of the product specification translation',
            ],
            'title' => [
                'type'        => Type::string(),
                'description' => 'The title of the product specification translation',
            ],
            'lang'  => [
                'type'        => Type::string(),
                'description' => 'The language code of the product category translation',
            ],
        ];
    }
}
