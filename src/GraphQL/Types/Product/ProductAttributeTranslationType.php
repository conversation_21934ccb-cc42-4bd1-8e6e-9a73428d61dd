<?php

namespace <PERSON>org\BaseFilamentPlugin\GraphQL\Types\Product;

use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasTranslation;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductAttributeItemTranslation;
use <PERSON><PERSON>org\BaseFilamentPlugin\Models\ProductAttributeTranslation;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductSpecificationTranslation;
use GraphQL\Type\Definition\Type;
use Rebing\GraphQL\Support\Type as GraphQLType;

class ProductAttributeTranslationType extends GraphQLType
{
    use HasTranslation;

    protected $attributes = [
        'name'        => 'ProductAttributeItemTranslation',
        'description' => 'A type that represents a product specification translation',
        'model'       => ProductAttributeItemTranslation::class,
    ];

    public function fields(): array
    {
        return [
            'id'    => [
                'type'        => Type::nonNull(Type::int()),
                'description' => 'The ID of the product specification translation',
            ],
            'title' => [
                'type'        => Type::string(),
                'description' => 'The title of the product specification translation',
            ],
            'lang'  => [
                'type'        => Type::string(),
                'description' => 'The language code of the product category translation',
            ],
        ];
    }
}
