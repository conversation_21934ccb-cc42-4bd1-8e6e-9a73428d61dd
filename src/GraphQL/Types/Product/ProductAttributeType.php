<?php

namespace Stephenchenorg\BaseFilamentPlugin\GraphQL\Types\Product;

use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasTranslation;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductAttribute;
use GraphQL\Type\Definition\Type;
use Rebing\GraphQL\Support\Facades\GraphQL;
use Rebing\GraphQL\Support\Type as GraphQLType;

class ProductAttributeType extends GraphQLType
{

    use HasTranslation;


    protected $attributes = [
        'name'        => 'ProductAttribute',
        'description' => 'A type that represents a product specification',
        'model'       => ProductAttribute::class,
    ];

    public function fields(): array
    {
        $fields = [
            'id'    => [
                'type'        => Type::nonNull(Type::int()),
                'description' => 'The ID of the product specification',
            ],
            'items' => [
                'description' => 'The items of the product attribute',
                'type'        => Type::listOf(GraphQL::type('ProductAttributeItem')),
            ],
        ];

        return array_merge($this->getTranslatableFields(new ProductAttributeTranslationType()), $fields);
    }
}
