<?php

namespace Stephen<PERSON>org\BaseFilamentPlugin\GraphQL\Types\Product;

use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;
use <PERSON><PERSON>org\BaseFilamentPlugin\GraphQL\Traits\HasImage;
use Stephen<PERSON>org\BaseFilamentPlugin\Models\ProductImage;
use GraphQL\Type\Definition\Type;
use Illuminate\Support\Facades\Storage;
use Rebing\GraphQL\Support\Type as GraphQLType;

class ProductImageType extends GraphQLType
{
    use HasImage;

    protected $attributes = [
        'name'        => EnumNames::ProductImage,
        'description' => 'A type that represents a product image',
        'model'       => ProductImage::class,
    ];

    public function fields(): array
    {
        return [
            'id'         => [
                'type'        => Type::nonNull(Type::int()),
                'description' => 'The ID of the product image',
            ],
            'is_default' => [
                'type'        => Type::boolean(),
                'description' => 'Whether the image is the default image for the product',
            ],
            ...$this->getImageArgs(),
            'created_at' => [
                'type'        => Type::string(),
                'description' => 'The creation timestamp of the product image',
            ],
            'updated_at' => [
                'type'        => Type::string(),
                'description' => 'The last update timestamp of the product image',
            ],
        ];
    }
}
