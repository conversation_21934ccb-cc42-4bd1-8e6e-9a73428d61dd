<?php

namespace <PERSON>org\BaseFilamentPlugin\GraphQL\Types\Product;

use GraphQL\Type\Definition\Type;
use Rebing\GraphQL\Support\Facades\GraphQL;
use Rebing\GraphQL\Support\Type as GraphQLType;
use Stephenchenorg\BaseFilamentPlugin\Contracts\ServiceProductSpecificationInterface;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasImage;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasPagination;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasSort;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasTranslation;
use Stephenchenorg\BaseFilamentPlugin\Models\Product;

class ProductType extends GraphQLType
{
    use HasTranslation, HasImage, HasSort, HasPagination;


    protected $attributes = [
        'name' => EnumNames::Product,
        'description' => 'Collection of products',
        'model' => Product::class,
    ];

    protected ServiceProductSpecificationInterface $serviceProductSpecification;

    public function __construct(ServiceProductSpecificationInterface $serviceProductSpecification)
    {
        $this->serviceProductSpecification = $serviceProductSpecification;
    }

    public function fields(): array
    {
        $fields = [
            'id' => [
                'type' => Type::nonNull(Type::int()),
                'description' => 'The ID of the product',
            ],
            'part_number' => [
                'type' => Type::string(),
                'description' => 'The part number of the product',
            ],
            'is_hottest' => [
                'type' => Type::int(),
                'description' => 'Whether the product is the hottest (0: no, 1: yes)',
            ],
            'is_newest' => [
                'type' => Type::int(),
                'description' => 'Whether the product is the newest (0: no, 1: yes)',
            ],
            'price' => [
                'type' => Type::float(),
                'description' => 'The listing price of the product',
                'resolve' => function ($root) {
                    if ($root->specifications->count() == 0) {
                        return null;
                    }

                    $min = $this->serviceProductSpecification->getListingPrice($root->specifications->first());

                    foreach ($root->specifications as $spec) {
                        $price = $this->serviceProductSpecification->getListingPrice($spec);
                        $min = min($min, $price);
                    }

                    return $min;
                },
            ],
            'selling_price' => [
                'type' => Type::float(),
                'description' => 'The selling price of the product',
                'resolve' => function ($root) {
                    if ($root->specifications->count() == 0) {
                        return null;
                    }

                    $min = $this->serviceProductSpecification->getSellingPrice($root->specifications->first());

                    foreach ($root->specifications as $spec) {
                        $price = $this->serviceProductSpecification->getSellingPrice($spec);
                        $min = min($min, $price);
                    }

                    return $min;
                },
            ],
            'inventory' => [
                'type' => Type::int(),
                'description' => 'The inventory of the product',
                'resolve' => function ($root) {
                    $max = $root->specifications()->orderBy('inventory', 'desc')->first();
                    if (empty($max)) {
                        return null;
                    }
                    return $max->inventory;
                },
            ],
            'type' => [
                'type' => Type::string(),
                'description' => 'The type of the product',
            ],
            'details' => [
                'description' => 'The details of the product',
                'type' => Type::listOf(GraphQL::type('ProductDetail')),
            ],
            'status' => [
                'type' => Type::int(),
                'description' => 'Status of the product (0: closed, 1: open)',
            ],
            'sort' => [
                'type' => Type::int(),
                'description' => 'Sort order of the product',
            ],
            ...$this->getImageArgs(function ($root, $args) {
                return $root->images
                    ->sortBy([
                        ['is_default', 'desc'],
                        ['id', 'asc'],
                    ])
                    ->first();
            }),
            'product_category_id' => [
                'type' => Type::int(),
                'description' => 'ID of the associated product category',
            ],
            'created_at' => [
                'type' => Type::string(),
                'description' => 'Timestamp when the product was created',
            ],
            'updated_at' => [
                'type' => Type::string(),
                'description' => 'Timestamp when the product was last updated',
            ],
            'category' => [
                'type' => GraphQL::type(EnumNames::ProductCategory),
                'description' => 'The associated product category',
            ],
            'attributes' => [
                'description' => 'The specifications of the product',
                'type' => Type::listOf(GraphQL::type('ProductAttribute')),
            ],
            'specifications' => [
                'description' => 'The specifications of the product',
                'type' => GraphQL::paginate(EnumNames::ProductSpecification),
                'args' => [
                    ...$this->getSortArgs(),
                    ...$this->getPaginationArgs(),
                ],
                'resolve' => function ($root, $args) {
                    $query = $root->specifications();
                    $query = $this->sortQuery($query, $args['sort_column'], $args['sort_by']);
                    return $this->paginateQuery($query, $args['per_page'], $args['page']);
                },
            ],
            'tags' => [
                'description' => 'The tags associated with the product',
                'type' => GraphQL::paginate(EnumNames::Tag),
                'args' => [
                    ...$this->getSortArgs(),
                    ...$this->getPaginationArgs(),
                ],
                'resolve' => function ($root, $args) {
                    $query = $root->tags();
                    $query = $this->sortQuery($query, $args['sort_column'], $args['sort_by']);
                    return $this->paginateQuery($query, $args['per_page'], $args['page']);
                },
            ],
            'images' => [
                'description' => 'The images associated with the product',
                'type' => GraphQL::paginate(EnumNames::ProductImage),
                'args' => [
                    ...$this->getSortArgs(),
                    ...$this->getPaginationArgs(),
                ],
                'resolve' => function ($root, $args) {
                    $query = $root->images();
                    $query = $this->sortQuery($query, $args['sort_column'], $args['sort_by']);
                    return $this->paginateQuery($query, $args['per_page'], $args['page']);
                },
            ],
            'related_products' => [
                'description' => 'The related products of this product',
                'type' => GraphQL::paginate(EnumNames::Product),
                'args' => [
                    ...$this->getSortArgs(),
                    ...$this->getPaginationArgs(),
                ],
                'resolve' => function ($root, $args) {
                    $query = Product::query();
                    $tagIds = $root->tags->pluck('id')->toArray();
                    $query->whereHas('tags', function ($q) use ($tagIds) {
                        $q->whereIn('tags.id', $tagIds);
                    });
                    $query->where('id', '!=', $root->id);
                    $query = $this->sortQuery($query, $args['sort_column'], $args['sort_by']);
                    return $this->paginateQuery($query, $args['per_page'], $args['page']);
                },
            ],
        ];

        return array_merge($this->getTranslatableFields(new ProductTranslationType()), $fields);
    }
}
