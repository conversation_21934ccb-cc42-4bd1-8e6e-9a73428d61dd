<?php

namespace Stephenchenorg\BaseFilamentPlugin\GraphQL\Types\Product;

use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasTranslation;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductAttributeItem;
use GraphQL\Type\Definition\Type;
use Rebing\GraphQL\GraphQL;
use Rebing\GraphQL\Support\Type as GraphQLType;

class ProductAttributeItemType extends GraphQLType
{

    use HasTranslation;


    protected $attributes = [
        'name'        => 'ProductAttributeItem',
        'description' => 'A type that represents a product specification',
        'model'       => ProductAttributeItem::class,
    ];

    public function fields(): array
    {
        $fields = [
            'id' => [
                'type'        => Type::nonNull(Type::int()),
                'description' => 'The ID of the product specification',
            ],
        ];

        return array_merge($this->getTranslatableFields(new ProductAttributeItemTranslationType()), $fields);
    }
}
