<?php

namespace Stephen<PERSON>org\BaseFilamentPlugin\GraphQL\Types\Product;

use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;
use <PERSON><PERSON>org\BaseFilamentPlugin\GraphQL\Traits\HasSeo;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductCategory;
use GraphQL\Type\Definition\Type;
use Rebing\GraphQL\Support\Facades\GraphQL;
use Rebing\GraphQL\Support\Type as GraphQLType;

class ProductCategoryTranslationType extends GraphQLType
{
    use HasSeo;

    protected $attributes = [
        'name'        => EnumNames::ProductCategoryTranslation,
        'description' => 'Collection of product category translations',
        'model'       => ProductCategory::class,
    ];

    public function fields(): array
    {
        return [
            'id'         => [
                'type'        => Type::nonNull(Type::int()),
                'description' => 'The ID of the product category translation',
            ],
            'title'      => [
                'type'        => Type::string(),
                'description' => 'The title of the product category translation',
            ],
            ...$this->getSeoArgs(),
            'lang'       => [
                'type'        => Type::string(),
                'description' => 'The language code of the product category translation',
            ],
            'created_at' => [
                'type'        => Type::string(),
                'description' => 'The creation timestamp of the product category translation',
            ],
            'updated_at' => [
                'type'        => Type::string(),
                'description' => 'The last update timestamp of the product category translation',
            ],
        ];
    }
}
