<?php

namespace Stephen<PERSON>org\BaseFilamentPlugin\GraphQL\Types\Product;

use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;
use <PERSON>chenorg\BaseFilamentPlugin\GraphQL\Traits\HasSeo;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductTranslation;
use GraphQL\Type\Definition\Type;
use Rebing\GraphQL\Support\Type as GraphQLType;

class ProductTranslationType extends GraphQLType
{
    use HasSeo;

    protected $attributes = [
        'name'        => EnumNames::ProductTranslation,
        'description' => 'Translation details of a product',
        'model'       => ProductTranslation::class,
    ];

    public function fields(): array
    {
        return [
            'id'         => [
                'type'        => Type::nonNull(Type::int()),
                'description' => 'The ID of the product translation',
            ],
            ...$this->getSeoArgs(),
            'title'      => [
                'type'        => Type::string(),
                'description' => 'Title of the product translation',
            ],
            'content_1'    => [
                'type'        => Type::string(),
                'description' => 'content_1 of the product translation',
            ],
            'content_2'    => [
                'type'        => Type::string(),
                'description' => 'content_2 of the product translation',
            ],
            'content_3'    => [
                'type'        => Type::string(),
                'description' => 'content_3 of the product translation',
            ],
            'lang'       => [
                'type'        => Type::string(),
                'description' => 'The language code of the translation',
            ],
            'created_at' => [
                'type'        => Type::string(),
                'description' => 'Timestamp when the product translation was created',
            ],
            'updated_at' => [
                'type'        => Type::string(),
                'description' => 'Timestamp when the product translation was last updated',
            ],
        ];
    }
}
