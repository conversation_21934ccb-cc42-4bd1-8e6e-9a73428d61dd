<?php

namespace <PERSON><PERSON>org\BaseFilamentPlugin\GraphQL\Types\Product;

use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasTranslation;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductAttribute;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductDetail;
use GraphQL\Type\Definition\Type;
use Rebing\GraphQL\Support\Facades\GraphQL;
use Rebing\GraphQL\Support\Type as GraphQLType;

class ProductDetailType extends GraphQLType
{

    use HasTranslation;

    protected $attributes = [
        'name'        => 'ProductDetail',
        'description' => 'A type that represents a product specification',
        'model'       => ProductDetail::class,
    ];

    public function fields(): array
    {
        $fields = [
            'id'   => [
                'type'        => Type::nonNull(Type::int()),
                'description' => 'The ID of the product specification',
            ],
            'type' => [
                'type'        => Type::string(),
                'description' => 'The language code of the product category translation',
                'resolve'     => function ($root)
                {
                    return $root->type->value;
                },
            ],
        ];

        return array_merge($this->getTranslatableFields(new ProductDetailTranslationType()), $fields);
    }
}
