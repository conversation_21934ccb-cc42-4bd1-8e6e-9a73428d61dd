<?php

namespace Stephenchenorg\BaseFilamentPlugin\GraphQL\Types\Product;

use Stephenchenorg\BaseFilamentPlugin\Models\ProductSpecificationTranslation;
use GraphQL\Type\Definition\Type;
use Rebing\GraphQL\Support\Type as GraphQLType;

class ProductSpecificationTranslationType extends GraphQLType
{
    protected $attributes = [
        'name'        => 'ProductSpecificationTranslation',
        'description' => 'A type that represents a product specification translation',
        'model'       => ProductSpecificationTranslation::class,
    ];

    public function fields(): array
    {
        return [
            'id'         => [
                'type'        => Type::nonNull(Type::int()),
                'description' => 'The ID of the product specification translation',
            ],
            'title'      => [
                'type'        => Type::string(),
                'description' => 'The title of the product specification translation',
            ],
            'content'    => [
                'type'        => Type::string(),
                'description' => 'The content of the product specification translation',
            ],
            'lang'       => [
                'type'        => Type::string(),
                'description' => 'The language code of the translation',
            ],
            'created_at' => [
                'type'        => Type::string(),
                'description' => 'The creation timestamp of the product specification translation',
            ],
            'updated_at' => [
                'type'        => Type::string(),
                'description' => 'The last update timestamp of the product specification translation',
            ],
        ];
    }
}
