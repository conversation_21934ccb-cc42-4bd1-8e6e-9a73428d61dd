<?php

namespace <PERSON>org\BaseFilamentPlugin\GraphQL\Types\Product;

use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasTranslation;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductAttributeItemTranslation;
use <PERSON><PERSON>org\BaseFilamentPlugin\Models\ProductAttributeTranslation;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductDetailTranslation;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductSpecificationTranslation;
use GraphQL\Type\Definition\Type;
use Rebing\GraphQL\Support\Type as GraphQLType;

class ProductDetailTranslationType extends GraphQLType
{
    use HasTranslation;

    protected $attributes = [
        'name'        => 'ProductDetailTranslation',
        'description' => 'A type that represents a product specification translation',
        'model'       => ProductDetailTranslation::class,
    ];

    public function fields(): array
    {
        return [
            'content' => [
                'type'        => Type::string(),
                'description' => 'The title of the product specification translation',
            ],
            'lang'    => [
                'type'        => Type::string(),
                'description' => 'The language code of the product category translation',
            ],
        ];
    }
}
