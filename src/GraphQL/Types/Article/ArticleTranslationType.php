<?php

namespace <PERSON>org\BaseFilamentPlugin\GraphQL\Types\Article;

use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;
use <PERSON><PERSON>org\BaseFilamentPlugin\GraphQL\Traits\HasContent;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasSeo;
use Stephenchenorg\BaseFilamentPlugin\Models\ArticleTranslation;
use GraphQL\Type\Definition\Type;
use Rebing\GraphQL\Support\Type as GraphQLType;

class ArticleTranslationType extends GraphQLType
{
    use HasSeo, HasContent;

    protected $attributes = [
        'name'        => EnumNames::ArticleTranslation,
        'description' => 'A type that represents an article translation',
        'model'       => ArticleTranslation::class,
    ];

    public function fields(): array
    {
        return [
            'id'         => [
                'type'        => Type::nonNull(Type::int()),
                'description' => 'The ID of the article translation',
            ],
            'author'     => [
                'type'        => Type::string(),
                'description' => 'The name of the author',
            ],
            ...$this->getSeoArgs(),
            'title'      => [
                'type'        => Type::string(),
                'description' => 'The title of the article translation',
            ],
            ...$this->getPlainTextArgs('description'),
            'content'    => [
                'type'        => Type::string(),
                'description' => 'The content of the article translation',
            ],
            'lang'       => [
                'type'        => Type::string(),
                'description' => 'The language code of the translation',
            ],
            'created_at' => [
                'type'        => Type::string(),
                'description' => 'The creation timestamp of the article translation',
            ],
            'updated_at' => [
                'type'        => Type::string(),
                'description' => 'The last update timestamp of the article translation',
            ],
        ];
    }
}
