<?php

namespace Stephen<PERSON>org\BaseFilamentPlugin\GraphQL\Types\Article;

use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;
use <PERSON><PERSON>org\BaseFilamentPlugin\GraphQL\Traits\HasContent;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasSeo;
use Stephenchenorg\BaseFilamentPlugin\Models\ArticleCategoryTranslation;
use GraphQL\Type\Definition\Type;
use Rebing\GraphQL\Support\Type as GraphQLType;

class ArticleCategoryTranslationType extends GraphQLType
{
    use HasSeo;

    protected $attributes = [
        'name'        => EnumNames::ArticleCategoryTranslation,
        'description' => 'A type that represents an article category translation',
        'model'       => ArticleCategoryTranslation::class,
    ];

    public function fields(): array
    {
        return [
            'id'         => [
                'type'        => Type::nonNull(Type::int()),
                'description' => 'The ID of the translation',
            ],
            'title'      => [
                'type'        => Type::string(),
                'description' => 'The title of the translation',
            ],
            'content'    => [
                'type'        => Type::string(),
                'description' => 'The content of the translation',
            ],
            ...$this->getSeoArgs(),
            'lang'       => [
                'type'        => Type::string(),
                'description' => 'The language code of the translation',
            ],
            'created_at' => [
                'type'        => Type::string(),
                'description' => 'The creation timestamp',
            ],
            'updated_at' => [
                'type'        => Type::string(),
                'description' => 'The last update timestamp',
            ],
        ];
    }
}
