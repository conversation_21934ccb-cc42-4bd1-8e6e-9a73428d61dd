<?php

namespace Stephen<PERSON>org\BaseFilamentPlugin\GraphQL\Types\Article;

use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;
use <PERSON><PERSON>org\BaseFilamentPlugin\GraphQL\Traits\HasImage;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasPagination;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasSort;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasTranslation;
use Stephenchenorg\BaseFilamentPlugin\Models\Article;
use GraphQL\Type\Definition\Type;
use Rebing\GraphQL\Support\Facades\GraphQL;
use Rebing\GraphQL\Support\Type as GraphQLType;

class ArticleType extends GraphQLType
{

    use HasTranslation, HasSort, HasImage, HasPagination;

    protected $attributes = [
        'name'        => EnumNames::Article,
        'description' => 'Collection of articles with their respective category',
        'model'       => Article::class,
    ];


    public function fields(): array
    {
        $fields = [
            'id'         => [
                'type'        => Type::nonNull(Type::int()),
                'description' => 'The ID of the article',
            ],
            'key'        => [
                'type'        => Type::string(),
                'description' => 'Unique key of the article',
            ],
            'slug'       => [
                'type'        => Type::string(),
                'description' => 'Unique slug of the article',
            ],
            'is_hottest' => [
                'type'        => Type::boolean(),
                'description' => 'Is hottest article',
            ],
            'is_newest' => [
                'type'        => Type::boolean(),
                'description' => 'Is newest article',
            ],
            'status'     => [
                'type'        => Type::int(),
                'description' => 'Status of the article (0: closed, 1: open)',
            ],
            'sort'       => [
                'type'        => Type::int(),
                'description' => 'Sort order of the article',
            ],
            ...$this->getBackgroundArgs(),
            ...$this->getCoverArgs(),
            'started_at' => [
                'type'        => Type::string(),
                'description' => 'The start date and time of the article',
            ],
            'ended_at'   => [
                'type'        => Type::string(),
                'description' => 'The end date and time of the article',
            ],
            'created_at' => [
                'type'        => Type::string(),
                'description' => 'The creation timestamp of the article',
            ],
            'updated_at' => [
                'type'        => Type::string(),
                'description' => 'The last update timestamp of the article',
            ],
            'category'   => [
                'type'        => GraphQL::type(EnumNames::ArticleCategory),
                'description' => 'The category of the article',
            ],

            'tags' => [
                'description' => 'Tags associated with the article',
                'type'        => GraphQL::paginate(EnumNames::Tag),
                'args'        => [
                    ...$this->getSortArgs(),
                    ...$this->getPaginationArgs(),
                ],
                'resolve'     => function ($root, $args)
                {
                    $query = $root->tags();
                    $query = $this->sortQuery($query, $args['sort_column'], $args['sort_by']);
                    return $this->paginateQuery($query, $args['per_page'], $args['page']);
                },
            ],

            ...$this->getPrevNextFields(EnumNames::Article),
        ];

        return array_merge($fields, $this->getTranslatableFields(new ArticleTranslationType()));
    }
}
