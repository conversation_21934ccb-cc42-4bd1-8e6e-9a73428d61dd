<?php

namespace <PERSON>org\BaseFilamentPlugin\GraphQL\Types\Article;

use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;
use <PERSON><PERSON>org\BaseFilamentPlugin\GraphQL\Traits\HasImage;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasPagination;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasSort;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasStatus;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasTranslation;
use Stephenchenorg\BaseFilamentPlugin\Models\ArticleCategory;
use GraphQL\Type\Definition\Type;
use Illuminate\Support\Facades\Storage;
use Rebing\GraphQL\Support\Facades\GraphQL;
use Rebing\GraphQL\Support\Type as GraphQLType;

class ArticleCategoryType extends GraphQLType
{
    use HasTranslation, HasImage, HasPagination, HasSort, HasStatus;

    protected $attributes = [
        'name'        => EnumNames::ArticleCategory,
        'description' => 'Collection of article categories',
        'model'       => ArticleCategory::class,
    ];

    public function fields(): array
    {
        $fields = [
            'id'     => [
                'type'        => Type::nonNull(Type::int()),
                'description' => 'The ID of the article category',
            ],
            'key'    => [
                'type'        => Type::string(),
                'description' => 'The unique key of the article category',
            ],
            'status' => [
                'type'        => Type::int(),
                'description' => 'Status of the article category (0: closed, 1: open)',
            ],
            'sort'   => [
                'type'        => Type::int(),
                'description' => 'Sort order of the article category',
            ],

            ...$this->getImageArgs(),

            'count_total' => [
                'type'        => Type::int(),
                'description' => 'Total count of articles under this category',
            ],
            'slug'        => [
                'type'        => Type::string(),
                'description' => 'Slug of the article category',
            ],
            'created_at'  => [
                'type'        => Type::string(),
                'description' => 'Timestamp when the article category was created',
            ],
            'updated_at'  => [
                'type'        => Type::string(),
                'description' => 'Timestamp when the article category was last updated',
            ],
            'articles'    => [
                'type'        => GraphQL::paginate(EnumNames::Article),
                'description' => 'The translations of the article category',
                'args'        => [
                    ...$this->getSortArgs(),
                    ...$this->getPaginationArgs(),
                ],
                'resolve'     => function ($root, $args)
                {
                    $query = $root->articles();
                    $query = $this->filterEnabledQuery($query);
                    $query = $this->sortQuery($query, $args['sort_column'], $args['sort_by']);
                    return $this->paginateQuery($query, $args['per_page'], $args['page']);
                },
            ],
        ];

        return array_merge($this->getTranslatableFields(new ArticleCategoryTranslationType()), $fields);
    }
}
