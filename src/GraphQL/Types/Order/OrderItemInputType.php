<?php

namespace Stephenchenorg\BaseFilamentPlugin\GraphQL\Types\Order;

use GraphQL\Type\Definition\Type;
use Rebing\GraphQL\Support\InputType;

class OrderItemInputType extends InputType
{
    protected $attributes = [
        'name' => 'OrderItemInput',
        'description' => 'Input type for order items',
    ];

    public function fields(): array
    {
        return [
            'product_specification_id' => [
                'name' => 'product_specification_id',
                'type' => Type::nonNull(Type::int()),
                'description' => 'The product specification ID',
                'rules' => ['required', 'integer', 'min:1', 'exists:product_specifications,id'],
            ],
            'quantity' => [
                'name' => 'quantity',
                'type' => Type::nonNull(Type::int()),
                'description' => 'The quantity of the product',
                'rules' => ['required', 'integer', 'min:1'],
            ],
        ];
    }
}
