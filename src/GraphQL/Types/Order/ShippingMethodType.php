<?php

namespace Stephenchenorg\BaseFilamentPlugin\GraphQL\Types\Order;

use GraphQL\Type\Definition\Type;
use Rebing\GraphQL\Support\Type as GraphQLType;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;

class ShippingMethodType extends GraphQLType
{
    protected $attributes = [
        'name' => EnumNames::ShippingMethod,
        'description' => 'Shipping method with type and cost',
    ];

    public function fields(): array
    {
        return [
            'type' => [
                'type' => Type::nonNull(Type::string()),
                'description' => 'The shipping method type',
            ],
            'cost' => [
                'type' => Type::nonNull(Type::float()),
                'description' => 'The shipping cost for this method',
            ],
        ];
    }
}
