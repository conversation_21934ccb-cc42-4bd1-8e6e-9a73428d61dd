<?php

namespace Stephen<PERSON>org\BaseFilamentPlugin\GraphQL\Types\Order;

use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;
use GraphQL\Type\Definition\Type;
use Rebing\GraphQL\Support\Facades\GraphQL;
use Rebing\GraphQL\Support\Type as GraphQLType;

class OrderPreflightType extends GraphQLType
{
    protected $attributes = [
        'name' => EnumNames::OrderPreflight,
        'description' => 'Order preflight information including amount and shipping methods',
    ];

    public function fields(): array
    {
        return [
            'amount' => [
                'type' => GraphQL::type(EnumNames::OrderAmount),
                'description' => 'The calculated order amount details',
            ],
            'shipping_methods' => [
                'type' => Type::listOf(GraphQL::type(EnumNames::ShippingMethod)),
                'description' => 'Available shipping methods with costs',
            ],
            'max_redeemable_points' => [
                'type' => Type::int(),
                'description' => 'The maximum redeemable points for the order',
            ],
        ];
    }
}
