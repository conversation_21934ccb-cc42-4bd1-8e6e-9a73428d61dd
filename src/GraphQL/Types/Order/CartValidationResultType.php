<?php

namespace Stephenchenorg\BaseFilamentPlugin\GraphQL\Types\Order;

use GraphQL\Type\Definition\Type;
use Rebing\GraphQL\Support\Type as GraphQLType;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;

class CartValidationResultType extends GraphQLType
{
    protected $attributes = [
        'name' => EnumNames::CartValidationResult,
        'description' => 'Result of cart validation with success status and optional error messages',
    ];

    public function fields(): array
    {
        return [
            'success' => [
                'type' => Type::nonNull(Type::boolean()),
                'description' => 'Whether the cart validation was successful',
            ],
        ];
    }
}
