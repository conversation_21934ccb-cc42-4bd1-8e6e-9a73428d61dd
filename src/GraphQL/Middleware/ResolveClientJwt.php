<?php

declare(strict_types=1);

namespace Stephenchenorg\BaseFilamentPlugin\GraphQL\Middleware;

use Closure;
use GraphQL\Type\Definition\ResolveInfo;
use Illuminate\Auth\AuthenticationException;
use Rebing\GraphQL\Support\Middleware;
use <PERSON><PERSON>\JWTAuth\Exceptions\JWTException;
use <PERSON><PERSON>\JWTAuth\Exceptions\TokenExpiredException;
use Tymon\JWTAuth\Exceptions\TokenInvalidException;
use <PERSON><PERSON>\JWTAuth\Facades\JWTAuth;

class ResolveClientJwt extends Middleware
{
    /**
     * Handle the incoming request and verify JWT token for clients
     *
     * @param mixed $root
     * @param array $args
     * @param mixed $context
     * @param ResolveInfo $info
     * @param Closure $next
     * @return mixed
     * @throws AuthenticationException
     */
    public function handle($root, array $args, $context, ResolveInfo $info, Closure $next): mixed
    {
        try {
            // Set the guard to clients
            auth('clients')->authenticate();
        } catch (AuthenticationException $e) {
            throw new AuthenticationException(__('base-filament-plugin::auth.token_invalid'));
        }

        return $next($root, $args, $context, $info);
    }
}
