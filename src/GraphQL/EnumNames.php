<?php

namespace Stephenchenorg\BaseFilamentPlugin\GraphQL;

enum EnumNames: string
{
    /*
    |--------------------------------------------------------------------------
    | 文章相關
    |--------------------------------------------------------------------------
    */

    public const string Article = 'Article';
    public const string Articles = 'Articles';
    public const string ArticleTranslation = 'ArticleTranslation';


    public const string ArticleCategory = 'ArticleCategory';
    public const string ArticleCategories = 'ArticleCategories';
    public const string ArticleCategoryTranslation = 'ArticleCategoryTranslation';


    /*
    |--------------------------------------------------------------------------
    | 問答相關
    |--------------------------------------------------------------------------
    */

    public const string Faq = 'Faq';
    public const string Faqs = 'Faqs';
    public const string FaqTranslation = 'FaqTranslation';


    public const string FaqCategory = 'FaqCategory';
    public const string FaqCategories = 'FaqCategories';
    public const string FaqCategoryTranslation = 'FaqCategoryTranslation';


    /*
    |--------------------------------------------------------------------------
    | Banner
    |--------------------------------------------------------------------------
    */

    public const string Banner = 'Banner';
    public const string Banners = 'Banners';

    /*
   |--------------------------------------------------------------------------
   | 自訂頁面相關
   |--------------------------------------------------------------------------
   */

    public const string Page = 'Page';
    public const string PageTranslation = 'PageTranslation';
    public const string Pages = 'Pages';
    public const string PageField = 'PageField';
    public const string PageFieldTranslation = 'PageFieldTranslation';

    /*
    |--------------------------------------------------------------------------
    | 產品相關
    |--------------------------------------------------------------------------
    */

    public const string Product = 'Product';
    public const string Products = 'Products';
    public const string ProductTranslation = 'ProductTranslation';


    public const string ProductCategory = 'ProductCategory';
    public const string ProductCategories = 'ProductCategories';
    public const string ProductCategoryTranslation = 'ProductCategoryTranslation';

    public const string ProductSpecification = 'ProductSpecification';
    public const string ProductSpecifications = 'ProductSpecifications';

    public const string ProductSpecificationTranslation = 'ProductSpecification';

    public const string ProductImage = 'ProductImage';


    /*
    |--------------------------------------------------------------------------
    | 公司基本資料相關
    |--------------------------------------------------------------------------
    */

    public const string CompanySetting = 'CompanySetting';


    /*
    |--------------------------------------------------------------------------
    | 標籤相關
    |--------------------------------------------------------------------------
    */

    public const string Tag = 'Tag';
    public const string Tags = 'Tags';

    /*
  |--------------------------------------------------------------------------
  | 品牌合作相關
  |--------------------------------------------------------------------------
  */

    public const string Brand = 'Brand';
    public const string Brands = 'Brands';

    public const string BrandTranslation = 'BrandTranslation';

    public const string Therapist = 'Therapist';


    /*
    |--------------------------------------------------------------------------
    | B2B會員相關
    |--------------------------------------------------------------------------
    */

    public const string Customer = 'Customer';
    public const string CustomerLogin = 'CustomerLogin';
    public const string CustomerRegister = 'CustomerRegister';
    public const string CustomerValidateEmail = 'CustomerValidateEmail';
    public const string CustomerLogout = 'CustomerLogout';
    public const string CustomerResetPassword = 'CustomerResetPassword';
    public const string CustomerReadyToForget = 'CustomerReadyToForget';
    public const string CustomerForgetPassword = 'CustomerForgetPassword';
    public const string CustomerMe = 'CustomerMe';
    public const string CustomerMeUpdate = 'CustomerMeUpdate';
    public const string Token = 'Token';

    /*
    |--------------------------------------------------------------------------
    | B2B客戶相關
    |--------------------------------------------------------------------------
    */

    public const string Client = 'Client';
    public const string ClientLogin = 'ClientLogin';
    public const string ClientRegister = 'ClientRegister';
    public const string ClientValidateEmail = 'ClientValidateEmail';
    public const string ClientLogout = 'ClientLogout';
    public const string ClientResetPassword = 'ClientResetPassword';
    public const string ClientReadyToForget = 'ClientReadyToForget';
    public const string ClientForgetPassword = 'ClientForgetPassword';
    public const string ClientMe = 'ClientMe';
    public const string ClientMeUpdate = 'ClientMeUpdate';

    /*
    |--------------------------------------------------------------------------
    | 聯絡相關
    |--------------------------------------------------------------------------
    */

    public const string CreateContact = 'CreateContact';

    /*
    |--------------------------------------------------------------------------
    | 訂單相關
    |--------------------------------------------------------------------------
    */

    public const string Order = 'Order';
    public const string Orders = 'Orders';
    public const string OrderItem = 'OrderItem';
    public const string CreateOrder = 'CreateOrder';
    public const string CartValidationResult = 'CartValidationResult';
    public const string ValidateCart = 'ValidateCart';
    public const string OrderAmount = 'OrderAmount';
    public const string CalculateCart = 'CalculateCart';
    public const string OrderPreflight = 'OrderPreflight';
    public const string ShippingMethod = 'ShippingMethod';
}
