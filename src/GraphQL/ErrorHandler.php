<?php

namespace Stephenchenorg\BaseFilamentPlugin\GraphQL;

use Error as PhpError;
use Exception;
use GraphQL\Error\Error;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Contracts\Debug\ExceptionHandler;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Validation\ValidationException;
use Rebing\GraphQL\Error\AuthorizationError;
use Rebing\GraphQL\Error\ValidationError;
use Tymon\JWTAuth\Exceptions\JWTException;

class ErrorHandler
{
    /**
     * @param Error[] $errors
     * @return Error[]
     * @throws BindingResolutionException
     * @throws \Throwable
     */
    public static function handleErrors(array $errors, callable $formatter): array
    {

        $handler = app()->make(ExceptionHandler::class);

        foreach ($errors as $error) {
            // Try to unwrap exception
            $error = $error->getPrevious() ?: $error;

            // Don't report certain GraphQL errors
            if ($error instanceof ValidationError ||
                $error instanceof AuthorizationError ||
                $error instanceof ValidationException ||
                $error instanceof ModelNotFoundException ||
                $error instanceof AuthenticationException ||
                $error instanceof JWTException ||
                !(
                    $error instanceof Exception ||
                    $error instanceof PhpError
                )) {
                continue;
            }

            if (!$error instanceof Exception) {
                $error = new Exception(
                    $error->getMessage(),
                    $error->getCode(),
                    $error
                );
            }

            $handler->report($error);
        }

        return array_map($formatter, $errors);
    }
}
