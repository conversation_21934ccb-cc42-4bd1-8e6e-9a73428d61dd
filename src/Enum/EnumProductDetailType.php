<?php

namespace Stephenchenorg\BaseFilamentPlugin\Enum;

enum EnumProductDetailType: string
{
    case DESCRIPTION = 'description'; // 商品介紹
    case MAINTENANCE = 'maintenance'; // 保養守則
    case AFTER_SALES_SERVICE = 'after_sales_service'; // 售後服務

    public function getLabel(): string
    {
        return match ($this) {
            self::DESCRIPTION => '商品介紹',
            self::MAINTENANCE => '保養守則',
            self::AFTER_SALES_SERVICE => '售後服務',
        };
    }
}
