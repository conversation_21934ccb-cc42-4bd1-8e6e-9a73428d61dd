<?php

namespace Stephenchenorg\BaseFilamentPlugin\Enum;

enum EnumPaymentMethod: string
{
    case NONE = 'NONE';                  // 未指定付款方式
    case ALL = 'ALL';                    // 全金流付款方式
    case CREDIT_CARD = 'CREDIT_CARD';    // 信用卡
    case ATM = 'ATM';                    // ATM 轉帳
    case WEB_ATM = 'WEB_ATM';           // 網路 ATM
    case CVS = 'CVS';                    // 超商代碼
    case BARCODE = 'BARCODE';           // 超商條碼

    /**
     * 取得付款方式的顯示名稱
     */
    public function getLabel(): string
    {
        return match($this) {
            self::NONE => '未指定付款方式',
            self::ALL => '全金流付款',
            self::CREDIT_CARD => '信用卡',
            self::ATM => 'ATM 轉帳',
            self::WEB_ATM => '網路 ATM',
            self::CVS => '超商代碼',
            self::BARCODE => '超商條碼',
        };
    }



    /**
     * 取得系統可用的付款方式
     *
     * @return array<EnumPaymentMethod>
     */
    public static function getAvailableMethods(): array
    {
        $acceptedMethods = config('cs.accept_payment_methods');

        $availableMethods = array_values(array_filter(
            array_map(
                fn($value) => self::tryFrom($value),
                $acceptedMethods
            )
        ));

        return $availableMethods;
    }

    /**
     * 取得系統可用的付款方式值
     *
     * @return array<string>
     */
    public static function getAvailableMethodValues(): array
    {
        return array_map(fn($method) => $method->value, self::getAvailableMethods());
    }

    /**
     * 檢查付款方式是否可用
     *
     * @param string $paymentMethod
     * @return bool
     */
    public static function isMethodAvailable(string $paymentMethod): bool
    {
        return in_array($paymentMethod, self::getAvailableMethodValues());
    }

    /**
     * 轉換為 ECPay 的付款方式值
     *
     * @return string
     */
    public function toECPayValue(): string
    {
        return match($this) {
            self::ALL => 'ALL',
            self::CREDIT_CARD => 'Credit',
            self::ATM => 'ATM',
            self::WEB_ATM => 'WebATM',
            self::CVS => 'CVS',
            self::BARCODE => 'BARCODE',
        };
    }
}
