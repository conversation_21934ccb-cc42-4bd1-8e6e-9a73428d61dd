<?php

namespace Stephenchenorg\BaseFilamentPlugin\Enum;

enum EnumShippingMethod: string
{
    case NONE = 'none';                // 未指定運送方式
    case DELIVERY = 'delivery';         // 宅配
    case OK_MART = 'ok_mart';          // OK
    case SEVEN_ELEVEN = 'seven_eleven'; // 7-11
    case FAMILY_MART = 'family_mart';   // 全家
    case HI_LIFE = 'hi_life';          // 萊爾富

    /**
     * 取得運送方式的顯示名稱
     */
    public function getLabel(): string
    {
        return match ($this) {
            self::NONE => '未指定',
            self::DELIVERY => '宅配',
            self::OK_MART => 'OK',
            self::SEVEN_ELEVEN => '7-11',
            self::FAMILY_MART => '全家',
            self::HI_LIFE => '萊爾富',
        };
    }


    /**
     * 取得系統可用的運送方式
     *
     * @return array<EnumShippingMethod>
     */
    public static function getAvailableMethods(): array
    {
        $acceptedMethods = config('cs.accept_shipping_methods');

        $availableMethods = array_values(array_filter(
            array_map(
                fn($value) => self::tryFrom($value),
                $acceptedMethods
            )
        ));

        return $availableMethods;
    }

    public function getColor(): string
    {
        return match ($this) {
            self::DELIVERY => 'success',
            self::SEVEN_ELEVEN => 'warning',
            self::FAMILY_MART => 'info',
            self::OK_MART => 'primary',
            self::HI_LIFE => 'secondary',
            default => 'gray',
        };
    }

    /**
     * 取得系統可用的運送方式值
     *
     * @return array<string>
     */
    public static function getAvailableMethodValues(): array
    {
        return array_map(fn($method) => $method->value, self::getAvailableMethods());
    }

    public static function getEditableMethods(): array
    {
        $acceptedMethods = config('cs.accept_shipping_methods');
        return array_map(function ($method) {
            return self::tryFrom($method);
        }, array_filter($acceptedMethods, fn($method) => $method !== self::NONE->value));
    }
}
