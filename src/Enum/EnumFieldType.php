<?php

namespace Stephenchenorg\BaseFilamentPlugin\Enum;

enum EnumFieldType: string
{
    case TEXT = 'text';
    case TEXTAREA = 'textarea';
    case HTML = 'html';
    case IMAGE = 'image';


    public function getLabel(): string
    {
        return match ($this) {
            self::TEXT => '單行文本',
            self::TEXTAREA => '多行文本',
            self::HTML => 'HTML內容',
            self::IMAGE => '圖片',
        };
    }
}
