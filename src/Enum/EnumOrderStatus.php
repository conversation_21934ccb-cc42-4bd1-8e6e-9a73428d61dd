<?php

namespace Stephenchenorg\BaseFilamentPlugin\Enum;

use Filament\Support\Contracts\HasLabel;

enum EnumOrderStatus: string implements HasLabel
{
    case PENDING = 'pending';
    case CANCELED = 'canceled';
    case COMPLETED = 'completed';
    case FAILED = 'failed';

    /**
     * @return string|null
     */
    public function getLabel(): ?string
    {
        return match ($this) {
            self::PENDING => '未建立金流訂單',
            self::CANCELED => '已取消',
            self::COMPLETED => '已完成',
            self::FAILED => '失敗',
        };
    }

    /**
     * 取得所有狀態值
     *
     * @return array
     */
    public static function getValues(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * 取得所有狀態選項 (value => label)
     *
     * @return array
     */
    public static function getOptions(): array
    {
        $options = [];
        foreach (self::cases() as $case) {
            $options[$case->value] = $case->getLabel();
        }
        return $options;
    }
}
