<?php

namespace Stephenchenorg\BaseFilamentPlugin\Enum;

enum EnumInvoiceMethod: string
{
    case NONE = 'none';                      // 未指定發票開立方式
    case DUPLICATE = 'duplicate';            // 二聯發票
    case TRIPLICATE = 'triplicate';          // 三聯發票
    case MOBILE_BARCODE = 'mobile_barcode';  // 手機條碼載具
    case CITIZEN_CARD = 'citizen_card';      // 自然人憑證載具
    case DONATION = 'donation';              // 捐贈

    /**
     * 取得發票方式的顯示名稱
     */
    public function getLabel(): string
    {
        return match($this) {
            self::DUPLICATE => '二聯發票',
            self::TRIPLICATE => '三聯發票',
            self::MOBILE_BARCODE => '手機條碼載具',
            self::CITIZEN_CARD => '自然人憑證載具',
            self::DONATION => '捐贈',
            self::NONE => '未指定',
        };
    }



    /**
     * 檢查是否需要載具號碼
     */
    public function requiresCarrierValue(): bool
    {
        return in_array($this, [
            self::MOBILE_BARCODE,
            self::CITIZEN_CARD,
        ]);
    }

    /**
     * 檢查是否需要發票地址
     */
    public function requiresInvoiceAddress(): bool
    {
        return in_array($this, [self::DUPLICATE, self::TRIPLICATE]);
    }

    /**
     * 檢查是否需要愛心碼
     */
    public function requiresLoveCode(): bool
    {
        return $this === self::DONATION;
    }

    /**
     * 取得系統可用的發票方式
     *
     * @return array<EnumInvoiceMethod>
     */
    public static function getAvailableMethods(): array
    {
        $acceptedMethods = config('cs.accept_invoice_methods', ['duplicate']);

        $availableMethods = array_values(array_filter(
            array_map(
                fn($value) => self::tryFrom($value),
                $acceptedMethods
            )
        ));

        return $availableMethods;
    }

    /**
     * 取得系統可用的發票方式值
     *
     * @return array<string>
     */
    public static function getAvailableMethodValues(): array
    {
        return array_map(fn($method) => $method->value, self::getAvailableMethods());
    }

    /**
     * 檢查發票方式是否可用
     *
     * @param string $invoiceMethod
     * @return bool
     */
    public static function isMethodAvailable(string $invoiceMethod): bool
    {
        return in_array($invoiceMethod, self::getAvailableMethodValues());
    }

    /**
     * 檢查是否為三聯發票
     */
    public function isTriplicate(): bool
    {
        return $this === self::TRIPLICATE;
    }

    /**
     * 轉換為 ECPay 的發票方式值
     *
     * @return string
     */
    public function toECPayValue(): string
    {
        return match($this) {
            self::DUPLICATE => 'paper',      // ECPay 使用 'paper' 作為紙本發票的值
            self::TRIPLICATE => 'paper',     // ECPay 使用 'paper' 作為紙本發票的值
            self::MOBILE_BARCODE => 'mobile_barcode',
            self::CITIZEN_CARD => 'citizen_card',
            self::DONATION => 'donation',
        };
    }
}
