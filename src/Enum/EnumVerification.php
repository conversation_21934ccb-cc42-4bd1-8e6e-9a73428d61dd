<?php

namespace Stephenchenorg\BaseFilamentPlugin\Enum;

use Filament\Support\Contracts\HasLabel;

enum EnumVerification: int implements HasLabel
{
    case PHONE = 0;

    case EMAIL = 10;

    /**
     * @return string|null
     */
    public function getLabel(): ?string
    {
        return match ($this) {
            self::PHONE => '手機',
            self::EMAIL => '信箱',
        };
    }

    public static function labelFromName(string $name): ?string
    {
        foreach (self::cases() as $case) {
            if ($case->name === strtoupper($name)) {
                return $case->getLabel();
            }
        }

        return null;
    }


}
