<?php

namespace Stephenchenorg\BaseFilamentPlugin\Enum\Gateway\ECPay;

use Filament\Support\Contracts\HasLabel;

/**
 * ECPay 發票開立方式
 */
enum ECPayInvoiceMethod: string implements HasLabel
{
    case MOBILE_BARCODE = 'mobile_barcode';     // 手機條碼載具
    case CITIZEN_CARD = 'citizen_card';         // 自然人憑證載具
    case PAPER = 'paper';                       // 紙本
    case DONATION = 'donation';                 // 捐贈

    /**
     * @return string|null
     */
    public function getLabel(): ?string
    {
        return match ($this) {
            self::MOBILE_BARCODE => '手機條碼載具',
            self::CITIZEN_CARD => '自然人憑證載具',
            self::PAPER => '紙本',
            self::DONATION => '捐贈',
        };
    }

    /**
     * 是否為載具類型
     *
     * @return bool
     */
    public function isCarrier(): bool
    {
        return match ($this) {
            self::MOBILE_BARCODE, self::CITIZEN_CARD => true,
            self::PAPER, self::DONATION => false,
        };
    }

    /**
     * 取得所有載具類型
     *
     * @return array
     */
    public static function getCarrierTypes(): array
    {
        return [
            self::MOBILE_BARCODE,
            self::CITIZEN_CARD,
        ];
    }

    /**
     * 取得所有值
     *
     * @return array
     */
    public static function getValues(): array
    {
        return array_column(self::cases(), 'value');
    }
}
