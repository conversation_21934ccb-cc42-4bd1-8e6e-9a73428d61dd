<?php

namespace Stephenchenorg\BaseFilamentPlugin\Enum;

enum EnumShippingStatus: string
{
    case UNSHIPPED = 'unshipped';
    case SHIPPED = 'shipped';
    case DELIVERED = 'delivered';
    case COMPLETED = 'complete';
    case PICKUP_FAILED = 'pickup_failed';
    case RETURNED = 'returned';

    public function getLabel(): string
    {
        return match ($this) {
            self::UNSHIPPED => '待發貨',
            self::SHIPPED => '已發貨',
            self::DELIVERED => '已送達',
            self::COMPLETED => '已取貨',
            self::PICKUP_FAILED => '無人取貨',
            self::RETURNED => '已退回',
        };
    }
}
