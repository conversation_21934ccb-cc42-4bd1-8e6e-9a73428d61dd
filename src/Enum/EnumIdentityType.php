<?php

namespace Stephenchenorg\BaseFilamentPlugin\Enum;

use Filament\Support\Contracts\HasColor;
use Filament\Support\Contracts\HasDescription;
use Filament\Support\Contracts\HasLabel;

enum EnumIdentityType: int implements HasLabel, HasColor, HasDescription
{
    case FIRST_ISSUE = 0;

    case REISSUE = 10;

    case REPLACEMENT = 20;

    public function getLabel(): ?string
    {
        return match ($this) {
            self::FIRST_ISSUE => '初發',
            self::REISSUE => '補發',
            self::REPLACEMENT => '換發',
        };
    }

    public function getColor(): ?string
    {
        return match ($this) {
            self::FIRST_ISSUE => 'info',
            self::REISSUE => 'danger',
            self::REPLACEMENT => 'gray',
        };
    }

    public function getDescription(): ?string
    {
        return match ($this) {
            self::FIRST_ISSUE => '初發',
            self::REISSUE => '補發',
            self::REPLACEMENT => '換發',
        };
    }

    public static function mappingFromAPI(string $project): ?self
    {
        return match (strtolower($project)) {
            'first_issue' => self::FIRST_ISSUE,
            'reissue' => self::REISSUE,
            'replacement' => self::REPLACEMENT,
        };
    }

    /**
     * @return string
     */
    public function toApiValue(): string
    {
        return match ($this) {
            self::FIRST_ISSUE => 'first_issue',
            self::REISSUE => 'reissue',
            self::REPLACEMENT => 'replacement',
        };
    }

    /**
     * @return array[]
     */
    public static function getValidApiValues(): array
    {
        return [
            'first_issue',
            'reissue',
            'replacement',
        ];
    }
}
