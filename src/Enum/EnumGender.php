<?php

namespace Stephenchenorg\BaseFilamentPlugin\Enum;

use Filament\Support\Contracts\HasColor;
use Filament\Support\Contracts\HasDescription;
use Filament\Support\Contracts\HasLabel;

enum EnumGender: int implements HasLabel, HasColor, HasDescription
{
    case MALE = 0;
    case FEMALE = 1;
    case PREFER_NOT_TO_DISCLOSE = 2;

    /**
     * @return string|null
     */
    public function getLabel(): ?string
    {
        return match ($this) {
            self::MALE => '男',
            self::FEMALE => '女',
            self::PREFER_NOT_TO_DISCLOSE => '不願透露',
        };
    }

    /**
     * @return string|null
     */
    public function getColor(): ?string
    {
        return match ($this) {
            self::MALE => 'info',
            self::FEMALE => 'danger',
            self::PREFER_NOT_TO_DISCLOSE => 'gray',
        };
    }

    /**
     * @return string|null
     */
    public function getDescription(): ?string
    {
        return match ($this) {
            self::MALE => '男',
            self::FEMALE => '女',
            self::PREFER_NOT_TO_DISCLOSE => '不願透露',
        };
    }

    /**
     * @param  string  $value
     * @return self|null
     */
    public static function mappingFromAPI(string $value): ?self
    {
        return match (strtolower($value)) {
            'male' => self::MALE,
            'female' => self::FEMALE,
            'prefer_not_to_disclose' => self::PREFER_NOT_TO_DISCLOSE,
        };
    }

    /**
     * @return string
     */
    public function toApiValue(): string
    {
        return match ($this) {
            self::MALE => 'male',
            self::FEMALE => 'female',
            self::PREFER_NOT_TO_DISCLOSE => 'prefer_not_to_disclose',
        };
    }

    /**
     * @return array[]
     */
    public static function getValidApiValues(): array
    {
        return [
            'male',
            'female',
            'prefer_not_to_disclose',
        ];
    }

}
