<?php

namespace Stephenchenorg\BaseFilamentPlugin\Enum;

enum EnumProductType: string
{
    case Product = 'product';
    case Service = 'service';

    public static function getOptions(): array
    {
        $allowedTypes = config('cs.product_types');

        return collect(self::cases())
            ->filter(fn($type) => in_array($type->value, $allowedTypes))
            ->toArray();
    }

    public function getLabel(): string
    {
        return match ($this) {
            self::Product => '產品',
            self::Service => '服務',
        };
    }
}
