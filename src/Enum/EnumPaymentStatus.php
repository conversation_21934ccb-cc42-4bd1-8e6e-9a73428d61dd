<?php

namespace Stephenchenorg\BaseFilamentPlugin\Enum;

enum EnumPaymentStatus: string
{
    case UNPAID = 'unpaid';
    case PAID = 'paid';
    case FAILED = 'failed';
    case REFUNDING = 'refunding';
    case REFUNDED = 'refunded';
    case REFUND_FAILED = 'refund_failed';

    public function getLabel(): string
    {
        return match ($this) {
            self::UNPAID => '未付款',
            self::PAID => '已付款',
            self::FAILED => '失敗',
            self::REFUNDING => '退款中',
            self::REFUNDED => '已退款',
            self::REFUND_FAILED => '退款失敗',
        };
    }
}
