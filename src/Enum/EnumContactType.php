<?php

namespace Stephenchenorg\BaseFilamentPlugin\Enum;

enum EnumContactType: string
{
    case CONTACT_US = 'contact_us';
    case REGISTRATION_FORM = 'registration_form';
    case FEEDBACK_FORM = 'feedback_form';

    public function getLabel(): string
    {
        return match ($this) {
            self::CONTACT_US => '聯絡我們',
            self::REGISTRATION_FORM => '報名表單',
            self::FEEDBACK_FORM => '意見回饋',
        };
    }

}
