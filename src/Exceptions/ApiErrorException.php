<?php

namespace Stephenchenorg\BaseFilamentPlugin\Exceptions;

use Exception;
use Illuminate\Http\Response;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitResponse;

class ApiErrorException extends Exception
{
    use CCTraitResponse;

    /**
     * @param $message
     * @param $code
     * @param  Exception|null  $previous
     */
    public function __construct($message = 'API Error', $code = 400, Exception $previous = null)
    {
        parent::__construct($message, $code, $previous);
    }

    /**
     * @return void
     */
    public function report()
    {
        //
    }

    /**
     * @param $request
     * @return Response
     */
    public function render($request): Response
    {
        return self::jsonFail($this->getMessage(), $this->getCode());
    }
}
