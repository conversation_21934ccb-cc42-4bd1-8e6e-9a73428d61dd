<?php

use <PERSON><PERSON>org\BaseFilamentPlugin\Models\Order;
use <PERSON>chenorg\BaseFilamentPlugin\Models\Customer;
use <PERSON><PERSON>org\BaseFilamentPlugin\Filament\Resources\OrderResource;
use Stephenchenorg\BaseFilamentPlugin\Enum\EnumPaymentStatus;
use Stephen<PERSON>org\BaseFilamentPlugin\Enum\EnumShippingStatus;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Actions\Action;

beforeEach(function () {
    // 創建測試用的客戶
    $this->customer = Customer::factory()->create();
    
    // 創建測試用的訂單
    $this->order = Order::factory()->create([
        'orderable_type' => Customer::class,
        'orderable_id' => $this->customer->id,
        'payment_status' => EnumPaymentStatus::UNPAID->value,
        'shipping_status' => EnumShippingStatus::UNSHIPPED->value,
    ]);
});

it('can create order resource table with action group', function () {
    $table = OrderResource::table(\Filament\Tables\Table::make());
    
    expect($table)->toBeInstanceOf(\Filament\Tables\Table::class);
});

it('has action group with payment and shipping status actions', function () {
    $table = OrderResource::table(\Filament\Tables\Table::make());
    $actions = $table->getActions();
    
    expect($actions)->toHaveCount(2); // ActionGroup + View action
    expect($actions[0])->toBeInstanceOf(ActionGroup::class);
});

it('payment status action shows available transitions', function () {
    // 測試付款狀態轉換
    $availableTransitions = $this->order->paymentStatus()->transitions();
    
    expect($availableTransitions)->toContain(EnumPaymentStatus::PAID->value);
    expect($availableTransitions)->toContain(EnumPaymentStatus::FAILED->value);
});

it('shipping status action shows available transitions', function () {
    // 測試出貨狀態轉換
    $availableTransitions = $this->order->shippingStatus()->transitions();
    
    expect($availableTransitions)->toContain(EnumShippingStatus::SHIPPED->value);
});

it('can transition payment status using state machine', function () {
    // 測試付款狀態轉換
    expect($this->order->payment_status)->toBe(EnumPaymentStatus::UNPAID->value);
    
    $this->order->paymentStatus()->transitionTo(EnumPaymentStatus::PAID->value);
    
    expect($this->order->fresh()->payment_status)->toBe(EnumPaymentStatus::PAID->value);
});

it('can transition shipping status using state machine', function () {
    // 測試出貨狀態轉換
    expect($this->order->shipping_status)->toBe(EnumShippingStatus::UNSHIPPED->value);
    
    $this->order->shippingStatus()->transitionTo(EnumShippingStatus::SHIPPED->value);
    
    expect($this->order->fresh()->shipping_status)->toBe(EnumShippingStatus::SHIPPED->value);
});

it('action group has correct label and icon', function () {
    $table = OrderResource::table(\Filament\Tables\Table::make());
    $actions = $table->getActions();
    $actionGroup = $actions[0];
    
    expect($actionGroup->getLabel())->toBe('狀態管理');
    expect($actionGroup->getIcon())->toBe('heroicon-m-cog-6-tooth');
    expect($actionGroup->getColor())->toBe('primary');
});
