name: Bug Report
description: Report an Issue or Bug with the Package
title: "[Bug]: "
labels: ["bug"]
body:
  - type: markdown
    attributes:
      value: |
        We're sorry to hear you have a problem. Can you help us solve it by providing the following details.
  - type: textarea
    id: what-happened
    attributes:
      label: What happened?
      description: What did you expect to happen?
      placeholder: I cannot currently do X thing because when I do, it breaks X thing.
    validations:
      required: true
  - type: textarea
    id: how-to-reproduce
    attributes:
      label: How to reproduce the bug
      description: How did this occur, please add any config values used and provide a set of reliable steps if possible.
      placeholder: When I do X I see Y.
    validations:
      required: true
  - type: input
    id: package-version
    attributes:
      label: Package Version
      description: What version of our Package are you running? Please be as specific as possible
      placeholder: 2.0.0
    validations:
      required: true
  - type: input
    id: php-version
    attributes:
      label: PHP Version
      description: What version of PHP are you running? Please be as specific as possible
      placeholder: 8.2.0
    validations:
      required: true
  - type: input
    id: laravel-version
    attributes:
      label: Laravel Version
      description: What version of <PERSON><PERSON> are you running? Please be as specific as possible
      placeholder: 9.0.0
    validations:
      required: true
  - type: dropdown
    id: operating-systems
    attributes:
      label: Which operating systems does with happen with?
      description: You may select more than one.
      multiple: true
      options:
        - macOS
        - Windows
        - Linux
  - type: textarea
    id: notes
    attributes:
      label: Notes
      description: Use this field to provide any other notes that you feel might be relevant to the issue.
    validations:
      required: false
