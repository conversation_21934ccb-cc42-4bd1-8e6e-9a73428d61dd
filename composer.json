{"name": "stephenchenorg/base-filament-plugin", "description": "This is my package base-filament-plugin", "keywords": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "laravel", "base-filament-plugin"], "homepage": "https://github.com/stephenchenorg/base-filament-plugin", "support": {"issues": "https://github.com/stephenchenorg/base-filament-plugin/issues", "source": "https://github.com/stephenchenorg/base-filament-plugin"}, "license": "MIT", "authors": [{"name": "miko", "email": "<EMAIL>", "role": "Developer"}], "require": {"php": "^8.3", "althinect/filament-spatie-roles-permissions": "^2.2", "asantibanez/laravel-eloquent-state-machines": "^6.0", "codewithdennis/filament-select-tree": "3.1.52", "filament/filament": "3.2.136", "filament/forms": "^3.0", "filament/tables": "^3.0", "franzose/closure-table": "^6.1", "guava/calendar": "^1.13", "laravel/framework": "^11.9", "laravel/tinker": "^2.9", "league/flysystem-aws-s3-v3": "^3.0", "livewire/livewire": "^3.5", "maatwebsite/excel": "^3.1", "mll-lab/laravel-graphiql": "^v4.0.1", "mohamedsabil83/filament-forms-tinyeditor": "2.3", "power-components/livewire-powergrid": "^6.1", "predis/predis": "^2.2", "rebing/graphql-laravel": "^9.6", "spatie/laravel-activitylog": "4.10", "spatie/laravel-package-tools": "^1.15.0", "stephenchenorg/cscorefilamentplugin": "dev-main", "symfony/http-client": "^7.2", "symfony/mailgun-mailer": "^7.2", "tsaiyihua/laravel-ecpay": "^3.3", "tymon/jwt-auth": "^2.2"}, "require-dev": {"orchestra/testbench": "^9.0", "nunomaduro/collision": "^8.0", "pestphp/pest": "^3.2", "pestphp/pest-plugin-laravel": "^3.0", "pestphp/pest-plugin-livewire": "^3.0"}, "autoload": {"psr-4": {"Stephenchenorg\\BaseFilamentPlugin\\": "src/", "Stephenchenorg\\BaseFilamentPlugin\\Database\\Factories\\": "database/factories/", "Stephenchenorg\\BaseFilamentPlugin\\Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Stephenchenorg\\BaseFilamentPlugin\\Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": "@php ./vendor/bin/testbench package:discover --ansi", "test": "vendor/bin/pest", "test-coverage": "vendor/bin/pest --coverage"}, "config": {"sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "phpstan/extension-installer": true}}, "extra": {"laravel": {"providers": ["Stephenchenorg\\BaseFilamentPlugin\\BaseFilamentPluginServiceProvider"], "aliases": {"BaseFilamentPlugin": "Stephenchenorg\\BaseFilamentPlugin\\Facades\\BaseFilamentPlugin"}}}, "repositories": [{"type": "vcs", "url": "**************:stephenchenorg/core.filament.plugin.git"}], "minimum-stability": "dev", "prefer-stable": true}